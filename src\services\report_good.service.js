import { http } from "./Base/base.service";

export const reportGoodService = {
  async getList(query) {
    return await http.get("/report/goods", {
      params: query,
    });
  },

  async export(query) {
    return await http.get("/report/export-csv-goods", {
      params: query,
    });
  },

  async exportPDF(query){
    return await http.get("/report/export-pdf-goods", {
      params: query,
      responseType: "blob"
    })
  }
};
