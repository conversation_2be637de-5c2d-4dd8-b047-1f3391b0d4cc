<script setup>
import EButton from "@/components/Elements/EButton.vue";
import EListEmpty from "@/components/Elements/EListEmpty.vue";
import { reportSaleService } from "@/services/report_sale.service";
import { useTemplateStore } from "@/stores/template";
import { Chart, registerables } from "chart.js";
import moment from "moment";
import { onMounted, reactive, ref, watch } from "vue";
import { Line } from "vue-chartjs";
import { Dataset, DatasetInfo, DatasetItem, DatasetShow } from "vue-dataset";
import FlatPickr from "vue-flatpickr-component";
import { useI18n } from "vue-i18n";

Chart.register(...registerables);
const store = useTemplateStore();
const dateRange = ref(defaultDateRange());
const handleChange = async (val) => {
  if (val?.length === 2) {
    await onFetchList();
  }
};
const paymentMethod = ref("All");
const configRange = ref({
  mode: "range",
  dateFormat: "d-m-Y",
  onChange: handleChange,
  locale: { firstDayOfWeek: 1 },
});
const { t, locale } = useI18n();
// Helper variables
const cols = reactive([
  {
    name: t("pages.report.fields.period_field"),
    field: "period",
    sort: "",
  },
  {
    name: t("pages.report.fields.description"),
    field: "description",
    sort: "",
  },
  {
    name: t("pages.report.fields.total_orders"),
    field: "orders",
    sort: "",
  },
  {
    name: t("pages.report.fields.no_of_items"),
    field: "item_no",
    sort: "",
  },
  {
    name: t("pages.report.fields.no_of_sales_ex_vat"),
    field: "vat_ex",
    sort: "",
  },
  {
    name: "MVA",
    field: "mva",
    sort: "",
  },
  {
    name: t("pages.report.fields.sales_inc_vat"),
    field: "vat_inc",
    sort: "",
  },
]);
const updateCols = () => {
  cols[0].name = t("pages.report.fields.period_field");
  cols[1].name = t("pages.report.fields.description");
  cols[2].name = t("pages.report.fields.total_orders");
  cols[3].name = t("pages.report.fields.no_of_items");
  cols[4].name = t("pages.report.fields.no_of_sales_ex_vat");
  cols[5].name = t("pages.report.fields.sales_inc_vat");
};

// Watch for language changes
watch(locale, () => {
  updateCols();
});
const total = ref();

const chartLinesBarsRadarData = ref({
  labels: ["MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"],
  datasets: [
    {
      label: "This Week",
      fill: false,
      backgroundColor: "#0C84C3",
      borderColor: "#0C84C3",
      pointBackgroundColor: "#0C84C3",
      pointBorderColor: "#fff",
      pointHoverBackgroundColor: "#0C84C3",
      pointHoverBorderColor: "rgba(0, 0, 0, .3)",
      data: [30, 32, 40, 45, 43, 38, 55],
    },
  ],
});

const listSaleReports = ref([]);

const limit = ref(10);
const currentPage = ref(1);
const visible = ref(true);
const totalItem = ref();
const MVA = ref();
const onFetchList = async () => {
  let start_date;
  let end_date;
  let start_date_params;
  let end_date_params;
  const dates = [];
  const [startDateString, endDateString] = dateRange.value.split(" to ");
  start_date_params = moment(startDateString, "DD-MM-YYYY").toISOString();
  end_date_params = endDateString
    ? moment(endDateString, "DD-MM-YYYY").endOf("day").toISOString()
    : moment(startDateString, "DD-MM-YYYY").endOf("day").toISOString();
  start_date = moment(startDateString, "DD-MM-YYYY");
  end_date = endDateString ? moment(endDateString, "DD-MM-YYYY") : start_date;
  let currentDate = moment(startDateString, "DD-MM-YYYY").clone();
  while (
    currentDate.isSameOrBefore(
      moment(endDateString || startDateString, "DD-MM-YYYY"),
      "day"
    )
  ) {
    dates.push(currentDate.format("DD-MM-YYYY"));
    currentDate.add(1, "day");
  }

  store.pageLoader({ mode: "on" });
  const response = await reportSaleService.getList({
    start_date: start_date_params,
    end_date: end_date_params,
    page: currentPage.value,
    limit: limit.value,
    payment_gateway:
      paymentMethod.value === "All" ? undefined : paymentMethod.value,
  });
  if (!response?.error) {
    listSaleReports.value = response.data[0].data || [];
    MVA.value = response.data[1];
    // console.log("kaka", MVA.value);
    let totalOrder = 0;
    let totalItems = 0;
    let totalSaleVat = 0;
    let totalMVA = 0;
    let totalSaleNoVat = 0;
    listSaleReports.value.forEach((itm) => {
      totalOrder += Number(itm?.total_orders);
      totalItems += Number(itm?.total_items);
      totalSaleVat += Number(itm?.sale_vat);
      totalMVA += Number(itm?.mva);
      totalSaleNoVat += Number(itm?.sale_no_vat);
    });
    total.value = {
      totalItems: totalItems,
      totalOrder: totalOrder,
      totalSaleNoVat: totalSaleNoVat,
      totalSaleVat: totalSaleVat,
      totalMVA: totalMVA,
    };
    totalItem.value = response.data[0].total;
    chartLinesBarsRadarData.value = {
      labels: dates,
      datasets: [
        {
          label: `${start_date._i} - ${end_date.format("DD-MM-YYYY")}`,
          fill: false,
          backgroundColor: "#0C84C3",
          borderColor: "#0C84C3",
          pointBackgroundColor: "#0C84C3",
          pointBorderColor: "#fff",
          pointHoverBackgroundColor: "#0C84C3",
          pointHoverBorderColor: "rgba(0, 0, 0, .3)",
          data: dates?.map((val) => {
            const itm = listSaleReports.value.find(
              (data) =>
                data?.date === moment(val, "DD-MM-YYYY").format("YYYY-MM-DD")
            )?.total_orders;
            return itm ? itm : 0;
          }),
        },
      ],
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            min: 0,
            suggestedMax: 5, // Ensures the y-axis goes up to 5 when the data is empty
          },
        },
      },
    };
  }
  store.pageLoader({ mode: "off" });
};

watch(limit, async () => {
  currentPage.value = 1;
  await onFetchList();
});

onMounted(async () => {
  await onFetchList();
  document.querySelectorAll("#datasetLength label").forEach((el) => {
    el.remove();
  });
  let selectLength = document.querySelector("#datasetLength select");
  if (selectLength) {
    selectLength.classList = "";
    selectLength.classList.add("form-select");
    selectLength.style.width = "80px";
  }
});

async function handleExportCS() {
  let start_date;
  let end_date;
  const [startDateString, endDateString] = dateRange.value.split(" to ");
  start_date = moment(startDateString, "DD-MM-YYYY");
  end_date = endDateString ? moment(endDateString, "DD-MM-YYYY") : start_date;
  const response = await reportSaleService.export({
    start_date: start_date.format("YYYY-MM-DD"),
    end_date: end_date.format("YYYY-MM-DD"),
    payment_gateway:
      paymentMethod.value === "All" ? undefined : paymentMethod.value,
  });
  const blod = new Blob([response], { type: "text/csv;charset=utf-8" });
  const url = URL.createObjectURL(blod);
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", "export_data.csv");
  link.click();
}

async function handleExportPDF() {
  let start_date;
  let end_date;
  const [startDateString, endDateString] = dateRange.value.split(" to ");
  start_date = moment(startDateString, "DD-MM-YYYY");
  end_date = endDateString ? moment(endDateString, "DD-MM-YYYY") : start_date;
  const response = await reportSaleService.exportPDF({
    start_date: start_date.format("YYYY-MM-DD"),
    end_date: end_date.format("YYYY-MM-DD"),
    payment_gateway:
      paymentMethod.value === "All" ? undefined : paymentMethod.value,
  });
  const blod = new Blob([response], { type: "application/pdf" });
  const url = URL.createObjectURL(blod);
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", "export_data_sales.pdf");
  link.click();
}

function defaultDateRange() {
  const startDate = moment().startOf("isoWeek").format("DD-MM-YYYY");
  const endDate = moment().endOf("isoWeek").format("DD-MM-YYYY");
  return `${startDate} to ${endDate}`;
}
watch(paymentMethod, async () => {
  await onFetchList();
});
</script>

<template>
  <BasePageHeading
    :title="t('pages.report.titles.sale_report')"
    :subtitle="t('pages.report.labels.label_head_list_sales')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.report.labels.manages")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            {{ t("pages.report.titles.sale_report") }}
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <div class="row">
      <div class="col-12 col-md-4">
        <FlatPickr
          class="form-control"
          id="example-flatpickr-range"
          :placeholder="t('pages.report.placeholder.date_range')"
          v-model="dateRange"
          :config="configRange"
        />
      </div>
      <div class="col-12 col-md-4">
        <select class="form-select" v-model="paymentMethod">
          <option value="All">All</option>
          <option value="vipps">Vipps</option>
          <option value="stripe">Stripe</option>
          <option value="Terminal">Terminal</option>
          <option value="cash">Cash</option>
        </select>
      </div>
      <div class="col-12 col-md-4">
        <div class="d-flex justify-content-end items-center gap-1">
          <e-button type="info" size="sm" @click="() => handleExportCS()">
            {{ t("pages.report.labels.btn_export") }}
          </e-button>
          <e-button type="info" size="sm" @click="() => handleExportPDF()">
            {{ t("pages.report.labels.btn_pdf") }}
          </e-button>
        </div>
      </div>
      <hr class="my-4" />
      <div class="col-12">
        <BaseBlock
          :title="t('pages.report.fields.chart')"
          content-full
          content-class="text-center"
        >
          <div class="py-3">
            <Line
              :data="chartLinesBarsRadarData"
              :options="{
                tension: 0.4,
                maintainAspectRatio: false,
                scales: {
                  y: {
                    beginAtZero: true,
                    min: 0,
                    suggestedMax: 5, // Ensures the y-axis goes up to 5 when the data is empty
                  },
                },
              }"
              style="height: 350px"
            />
          </div>
        </BaseBlock>
      </div>
    </div>
    <BaseBlock :title="t('pages.report.titles.sale_report')">
      <Dataset
        v-slot="{ ds }"
        :ds-data="listSaleReports"
        :ds-search-in="['id', 'name']"
      >
        <div class="row" :data-page-count="ds.dsPagecount">
          <div id="datasetLength" class="col-md-8 py-2">
            <DatasetShow v-show="false" :dsShowEntries="100" />
            <div class="form-inline">
              <select class="form-select" style="width: 80px" v-model="limit">
                <option :value="5">5</option>
                <option :value="10">10</option>
                <option :value="25">25</option>
                <option :value="50">50</option>
                <option :value="100">100</option>
              </select>
            </div>
          </div>
          <!-- <div class="col-md-4 py-2">
            <DatasetSearch :ds-search-placeholder="t('pages.placeholder.search')" />
          </div> -->
        </div>

        <hr />
        <div class="row" v-if="listSaleReports?.length">
          <div class="col-md-12">
            <div class="table-responsive">
              <table class="table table-striped mb-0">
                <thead>
                  <tr>
                    <th
                      v-for="th in cols"
                      :key="th.field"
                      :class="['sort', th.sort]"
                    >
                      {{ th.name }} <i class="gg-select float-end"></i>
                    </th>
                  </tr>
                </thead>
                <DatasetItem tag="tbody" class="fs-sm">
                  <template #default="{ row }">
                    <tr>
                      <td style="min-width: 50px">
                        {{ row.date }}
                      </td>
                      <td>
                        {{
                          row.date
                            ? moment(row.date, "YYYY-MM-DD").format("dddd")
                            : ""
                        }}
                      </td>
                      <td>{{ row.total_orders }}</td>
                      <td>{{ row.total_items }}</td>
                      <td>
                        {{ Number(row.sale_no_vat).toFixed(2) + ",-kr" }}
                      </td>
                      <td>
                        {{
                          row.sale_no_vat
                            ? Number(row.mva).toFixed(2) + ",-kr"
                            : ""
                        }}
                      </td>
                      <td>
                        {{ Number(row.sale_vat).toFixed(2) + ",-kr" }}
                      </td>
                    </tr>
                  </template>
                </DatasetItem>
                <tfoot>
                  <tr>
                    <td>{{ t("pages.report.fields.total") }}</td>
                    <td></td>
                    <td>{{ total.totalOrder }}</td>
                    <td>{{ total.totalItems }}</td>
                    <td>
                      {{ Number(total.totalSaleNoVat).toFixed(2) + ",-kr" }}
                    </td>
                    <td>{{ Number(total.totalMVA).toFixed(2) + ",-kr" }}</td>
                    <td>
                      {{ Number(total.totalSaleVat).toFixed(2) + ",-kr" }}
                    </td>
                  </tr>
                  <tr v-if="MVA[15]">
                    <td>MVA 15%</td>
                    <td></td>
                    <td>{{ MVA[15]?.order_count }}</td>
                    <td>{{ MVA[15]?.item_count }}</td>
                    <td>
                      {{ Number(MVA[15]?.sale_no_vat).toFixed(2) + ",-kr" }}
                    </td>
                    <td>{{ Number(MVA[15]?.mva).toFixed(2) + ",-kr" }}</td>
                    <td>
                      {{ Number(MVA[15]?.sale_with_vat).toFixed(2) + ",-kr" }}
                    </td>
                  </tr>
                  <tr v-if="MVA[25]">
                    <td>MVA 25%</td>
                    <td></td>
                    <td>{{ MVA[25]?.order_count }}</td>
                    <td>{{ MVA[25]?.item_count }}</td>
                    <td>
                      {{ Number(MVA[25]?.sale_no_vat).toFixed(2) + ",-kr" }}
                    </td>
                    <td>{{ Number(MVA[25]?.mva).toFixed(2) + ",-kr" }}</td>
                    <td>
                      {{ Number(MVA[25]?.sale_with_vat).toFixed(2) + ",-kr" }}
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        </div>
        <EListEmpty v-else />
        <div
          class="d-flex flex-md-row flex-column justify-content-between align-items-center"
        >
          <DatasetInfo class="py-3 fs-sm" />
          <el-pagination
            v-if="visible"
            v-model:current-page="currentPage"
            @current-change="onFetchList"
            background
            v-model:page-size="limit"
            layout="prev, pager, next"
            :prev-text="t('pages.footer.previous')"
            :next-text="t('pages.footer.next')"
            :total="totalItem"
          />
        </div>
      </Dataset>
    </BaseBlock>
  </div>
</template>

<style lang="scss">
@import "flatpickr/dist/flatpickr.css";
@import "@/assets/scss/vendor/flatpickr";

tfoot > tr > td {
  font-weight: bold;
}
</style>
