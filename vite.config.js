import { fileURLToPath, URL } from "url";
import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite'
import { resolve, dirname } from 'node:path'

import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue(),
    VueI18nPlugin({
      runtimeOnly: false,
      include: resolve(
          dirname(fileURLToPath(import.meta.url)),
          './src/i18n/locales/**'
      ),
    }),
  ],
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
  },
  server: {
    host: true,
  },
});
