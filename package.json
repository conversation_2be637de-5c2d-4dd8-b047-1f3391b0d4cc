{"name": "oneui-vue-edition", "version": "2.6.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview --port 5050", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "browserslist": [">= 0.5%", "last 2 major versions", "not dead", "Chrome >= 60", "Firefox >= 60", "Firefox ESR", "iOS >= 12", "Safari >= 12", "not Explorer <= 11"], "dependencies": {"@chenfengyuan/vue-countdown": "^2.1.2", "@ckeditor/ckeditor5-build-balloon": "^40.0.0", "@ckeditor/ckeditor5-build-balloon-block": "^40.0.0", "@ckeditor/ckeditor5-build-classic": "^40.0.0", "@ckeditor/ckeditor5-build-inline": "^40.0.0", "@ckeditor/ckeditor5-vue": "^5.1.0", "@ckpack/vue-color": "^1.5.0", "@fortawesome/fontawesome-free": "^6.4.2", "@fullcalendar/core": "^6.1.9", "@fullcalendar/daygrid": "^6.1.9", "@fullcalendar/interaction": "^6.1.9", "@fullcalendar/list": "^6.1.9", "@fullcalendar/timegrid": "^6.1.9", "@fullcalendar/vue3": "^6.1.9", "@highlightjs/vue-plugin": "^2.1.0", "@intlify/unplugin-vue-i18n": "^4.0.0", "@popperjs/core": "^2.11.8", "@tiptap/pm": "^2.2.3", "@tiptap/starter-kit": "^2.1.11", "@tiptap/vue-3": "^2.1.11", "@vueform/slider": "^2.1.7", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "@vueuse/core": "^11.1.0", "axios": "^1.6.7", "bootstrap": "5.3.2", "chart.js": "^4.4.0", "dayjs": "^1.11.10", "dropzone": "^6.0.0-beta.2", "element-plus": "^2.8.3", "highlight.js": "^11.9.0", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "nprogress": "^0.2.0", "pinia": "^2.1.6", "react-dnd-html5-backend": "^16.0.1", "simplebar": "^6.2.5", "sweetalert2": "^11.7.32", "vee-validate": "^4.12.4", "vue": "^3.2.31", "vue-chartjs": "^5.2.0", "vue-cropperjs": "^5.0.0", "vue-dataset": "^3.6.1", "vue-easy-lightbox": "^1.16.0", "vue-flatpickr-component": "^11.0.3", "vue-i18n": "9", "vue-router": "^4.2.5", "vue-select": "^4.0.0-beta.3", "vue-star-rating": "^2.1.0", "vue3-cookies": "^1.0.6", "vue3-dnd": "^2.0.4", "lodash": "^4.17.21", "yup": "^1.3.3"}, "devDependencies": {"@vitejs/plugin-vue": "^4.4.0", "eslint": "^8.51.0", "eslint-plugin-vue": "^9.17.0", "sass": "^1.69.1", "vite": "^4.4.11"}}