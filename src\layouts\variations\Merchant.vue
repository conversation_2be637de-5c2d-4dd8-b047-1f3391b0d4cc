<script setup>
import { useTemplateStore } from "@/stores/template";
import BaseLayout from "@/layouts/BaseLayout.vue";
import BaseNavigation from "@/components/BaseNavigation.vue";
import EIcon from "@/components/Elements/EIcon.vue";
import { useI18n } from "vue-i18n";
import useAuth from "@/composables/useAuth";
import { useMultiTenant } from "@/stores/multiTenant";
import { storeService } from "@/services/store.service";
import { common } from "@/stores/storeCommon";
import { useCookies } from "vue3-cookies";
import useNotify from "@/composables/useNotify";
import Trans from "@/i18n/i18nUtils";
import moment from "moment-timezone";
import CountDown from "@/components/CountDown.vue";
import { computed, onMounted, ref } from "vue";
import ELanguageSwitcher from "@/components/Elements/ELanguageSwitcher.vue";
import useAppRouter from "@/composables/useRouter";
import { ACCOUNTANT, EMPLOYEE, MANAGER } from "@/data/role";
import Alert from "../../components/Alert.vue";

// Main store
const store = useTemplateStore();
// Set default elements for this layout
store.setLayout({
  header: true,
  sidebar: true,
  sideOverlay: true,
  footer: false,
});

// Set various template options for this layout variation
store.headerStyle({ mode: "light" });
store.mainContent({ mode: "narrow" });
const { userInfo, resetUser, userPlanId, setUser } = useAuth();
const multiTenant = useMultiTenant();
const router = useAppRouter();
const { setNotify } = useNotify();
const { cookies } = useCookies();
const userRole = cookies.get("role_merchant");
const plans = cookies.get("plans");
const commonStore = common();
const rush_type = ref(9);
const rush_for = ref(1);
const options = ref([
  {
    value: 9,
    label: "Permanent",
  },
  {
    value: 1,
    label: "15 minutes",
  },
  {
    value: 2,
    label: "30 minutes",
  },
  {
    value: 3,
    label: "1 hour",
  },
  {
    value: 4,
    label: "3 hours",
  },
  {
    value: 5,
    label: "6 hours",
  },
  {
    value: 6,
    label: "8 hours",
  },
]);
const optionsNor = ref([
  {
    value: 9,
    label: "Permanent",
  },
  {
    value: 1,
    label: "15 minutter",
  },
  {
    value: 2,
    label: "30 minutter",
  },
  {
    value: 3,
    label: "1 time",
  },
  {
    value: 4,
    label: "3 time",
  },
  {
    value: 5,
    label: "6 timer",
  },
  {
    value: 6,
    label: "8 timer",
  },
]);
const isShowModal = ref(false);
let store_id = JSON.parse(localStorage.getItem("user_merchant")).store.id;
const isInRush = ref();
const rushEnd = ref();
const getStoreDetail = async () => {
  try {
    store.pageLoader({ mode: "on" });
    store_id = JSON.parse(localStorage.getItem("user_merchant")).store.id;
    const res = await storeService.get(store_id);
    commonStore.setRushMode(
      res.data.plans.filter((item) => item?.id === 12)[0]?.active
    );
    isInRush.value = res.data.in_rush;
    rushEnd.value = res.data.rush_end;
    console.log(isInRush.value, rushEnd.value);
    console.log(
      rushEnd.value,
      moment.tz("Europe/Oslo").format("YYYY-MM-DD HH:mm:ss")
    );
    store.pageLoader({ mode: "off" });
  } catch (error) {
    store.pageLoader({ mode: "off" });
  }
};
const onChangeRushMode = () => {
  if (!isInRush.value) {
    rush_type.value = 9;
    rush_for.value = 1;
    isShowModal.value = true;
  } else {
    inActiveRushMode();
  }
};
const inActiveRushMode = async () => {
  try {
    const payload = {
      in_rush: 0,
    };
    store.pageLoader({ mode: "on" });
    const response = await storeService.post(payload);
    if (!response?.error) {
      console.log("res", response);
      await getStoreDetail();
      store.pageLoader({ mode: "off" });
      isShowModal.value = false;
      return setNotify({
        title: "Success",
        message: response.message,
        type: "success",
      });
    }
  } catch (error) {
    store.pageLoader({ mode: "off" });
  }
};
const activeRushMode = async () => {
  try {
    const payload = {
      in_rush: 1,
      rush_type: rush_type.value,
      rush_for: rush_for.value,
      store_id: store_id,
    };
    store.pageLoader({ mode: "on" });
    const response = await storeService.post(payload);
    if (!response?.error) {
      console.log("res", response);
      await getStoreDetail();
      store.pageLoader({ mode: "off" });
      isShowModal.value = false;
      return setNotify({
        title: "Success",
        message: response.message,
        type: "success",
      });
    }
  } catch (error) {
    store.pageLoader({ mode: "off" });
  }
};
const optionStores = ref([]);
const selectedStore = ref();
onMounted(async () => {
  if (cookies.get("isMultiTenant") === "true") {
    await multiTenant.getListStores();
    if (!multiTenant.arrayStores.some((item) => item.id === store_id)) {
      onSignOut();
    }
  }
  multiTenant.arrayStores =
    JSON.parse(localStorage.getItem("arrayStores")) || [];
  if (localStorage.getItem("arrayStores")) {
    optionStores.value = multiTenant.arrayStores.map((item) => ({
      value: item.id,
      label: item.name,
    }));
    selectedStore.value = Number(cookies.get("current_store"));
  }
  getStoreDetail();
});
const changeStore = async (id) => {
  store.pageLoader({ mode: "on" });
  await multiTenant.loginIn(id, cookies, router, Trans, store);
  setUser(JSON.parse(localStorage.getItem("user_merchant")));
  await getStoreDetail();
  multiTenant.watchChange = !multiTenant.watchChange;
  store.pageLoader({ mode: "off" });
};
const userPlans = computed(() => userInfo.value?.plans);
// const userPlansKiosk = computed(() => userPlans.value?.find(item => item.id === userPlanId.KIOSK))
const userPlansTable = computed(() =>
  userPlans.value?.find((item) => item.id === userPlanId.TABLE)
);
const { t, locale } = useI18n();
const onSignOut = () => {
  resetUser();
  cookies.remove("token_merchant");
  localStorage.removeItem("user_merchant");
  cookies.remove("role_merchant");
  cookies.remove("isMultiTenant");
  cookies.remove("token_multi_tenant");
  cookies.remove("current_store");
  localStorage.removeItem("arrayStores");
  router.push({ name: "merchant-auth-signin" });
};
</script>

<template>
  <BaseLayout>
    <!-- Side Overlay Content -->
    <!-- Using the available v-slot, we can override the default Side Overlay content from layouts/partials/SideOvelay.vue -->
    <template #side-overlay-content>
      <div class="content-side">
        <p>Side Overlay content..</p>
      </div>
    </template>
    <!-- END Side Overlay Content -->

    <!-- Sidebar Content -->
    <!-- Using the available v-slot, we can override the default Sidebar content from layouts/partials/Sidebar.vue -->
    <template #sidebar-content>
      <div class="">
        <div>
          <a
            class="d-flex py-4 px-4 flex-row-reverse"
            href="javascript:void(0)"
          >
            <div
              class="flex-shrink-0 me-3 ms-2 overlay-container overlay-bottom"
            >
              <img
                class="img-avatar img-avatar40"
                src="/assets/media/avatars/avatar3.jpg"
                alt="Avatar"
              />
            </div>
            <div class="flex-grow-1">
              <div
                style="word-break: break-all"
                class="fw-normal fs-sm text-white"
              >
                {{ userInfo?.name }}
              </div>
              <div class="fw-normal fs-sm text-muted">Online</div>
            </div>
          </a>
          <div v-if="cookies.get('isMultiTenant') === 'true'" class="store-con">
            <el-select-v2
              @change="changeStore"
              v-model="selectedStore"
              :options="optionStores"
              size="large"
            />
          </div>
        </div>
        <div class="px-4">
          <BaseNavigation
            :nodes="[
              {
                name: t(`menu.apps`),
                heading: true,
                hidden: userRole !== MANAGER,
              },
              {
                name: t(`menu.dashboard`),
                to: 'merchant-dashboard',
                icon: 'fa fa-house',
                module: 'dashboard',
              },
              {
                name: t(`menu.manages`),
                heading: true,
              },
              {
                name: t(`menu.products`),
                to: 'merchant-products-list',
                icon: 'fa fa-bowl-food',
                module: 'products',
                hidden: userRole !== MANAGER && userRole !== EMPLOYEE,
              },
              {
                name: t(`menu.categories`),
                to: 'merchant-categories-list',
                icon: 'fa fa-boxes-stacked',
                module: 'categories',
                hidden: userRole !== MANAGER,
              },
              {
                name: t(`menu.topping`),
                icon: 'fa fa-bell-concierge',
                hidden: userRole !== MANAGER && userRole !== EMPLOYEE,
                sub: [
                  {
                    name: t(`menu.topping`),
                    to: 'merchant-toppings-list',
                    hidden: userRole !== MANAGER && userRole !== EMPLOYEE,
                  },
                  {
                    name: t(`menu.topping_groups`),
                    to: 'merchant-topping-groups-list',
                    hidden: userRole !== MANAGER,
                  },
                  {
                    name: t(`menu.topping_fav_groups`),
                    to: 'merchant-fav-topping-groups-list',
                    hidden: userRole !== MANAGER,
                  },
                ],
              },
              {
                name: t(`menu.tables`),
                to: 'merchant-tables-list',
                icon: 'si si-screen-tablet',
                module: 'tables',
                hidden: !userPlansTable?.active || userRole !== MANAGER,
              },
              {
                name: t(`menu.orders`),
                to: 'merchant-orders-list',
                icon: 'fa fa-newspaper',
                module: 'orders',
                hidden:
                  userRole !== MANAGER &&
                  userRole !== ACCOUNTANT &&
                  userRole !== EMPLOYEE,
              },
              {
                name: t(`menu.customers`),
                to: 'merchant-customers-list',
                icon: 'fa fa-users',
                module: 'customers',
                hidden: userRole !== MANAGER && userRole !== ACCOUNTANT,
              },
              {
                name: t(`menu.units`),
                to: 'merchant-units-list',
                icon: 'fab fa-unity',
                module: 'units',
                hidden: userRole !== MANAGER,
              },
              {
                name: t(`menu.producers`),
                to: 'merchant-producers-list',
                icon: 'fab fa-product-hunt',
                module: 'producers',
                hidden: userRole !== MANAGER,
              },
              {
                name: t(`menu.suppliers`),
                to: 'merchant-suppliers-list',
                icon: 'fa fa-diagram-project',
                module: 'suppliers',
                hidden: userRole !== MANAGER,
              },
              {
                name: t(`menu.analytics`),
                heading: true,
                hidden: userRole !== MANAGER,
              },
              {
                name: t(`menu.reports`),
                icon: 'fa fa-chart-column',
                subActivePaths: '/merchant/reports',
                hidden: userRole !== MANAGER && userRole !== ACCOUNTANT,
                sub: [
                  {
                    name: t(`menu.daily_summary`),
                    to: 'merchant-daily-summary-list',
                    module: 'daily_summary',
                  },
                  {
                    name: t(`menu.sale_report`),
                    to: 'merchant-report-sales',
                    hidden: userRole !== MANAGER && userRole !== ACCOUNTANT,
                  },
                  {
                    name: t(`menu.good_report`),
                    to: 'merchant-report-goods',
                    hidden: userRole !== MANAGER && userRole !== ACCOUNTANT,
                  },
                  {
                    name: t(`menu.time_report`),
                    to: 'merchant-report-time',
                    hidden: userRole !== MANAGER && userRole !== ACCOUNTANT,
                  },
                  {
                    name: t(`menu.sms_report`),
                    to: 'merchant-report-sms',
                    hidden: userRole !== MANAGER && userRole !== ACCOUNTANT,
                  },
                  {
                    name: t(`menu.order_report`),
                    to: 'merchant-report-order',
                    hidden: userRole !== MANAGER && userRole !== ACCOUNTANT,
                  },
                  {
                    name: t('menu.tip_report'),
                    to: 'merchant-report-tip',
                    hidden:
                      (userRole !== MANAGER && userRole !== ACCOUNTANT) ||
                      !(plans === 'true'),
                  },
                ],
              },
              {
                name: t(`menu.printer_logs`),
                to: 'merchant-printer-logs-list',
                icon: 'si si-printer',
                module: 'printer-logs',
                hidden: userRole !== MANAGER,
              },
              {
                name: t(`menu.users`),
                to: 'merchant-users-list',
                icon: 'fa fa-user-group',
                module: 'users',
                hidden: userRole !== MANAGER,
              },
              {
                name: t(`menu.transactions`),
                to: 'merchant-transactions-list',
                icon: 'fa fa-address-card',
                module: 'transactions',
                hidden:
                  userRole !== MANAGER &&
                  userRole !== ACCOUNTANT &&
                  userRole !== EMPLOYEE,
              },
              {
                name: t(`menu.opening_hours`),
                to: 'merchant-opening-hours-list',
                icon: 'fa fa-hourglass-half',
                module: 'opening-hours',
                hidden: userRole !== MANAGER,
              },
            ]"
          />
        </div>
      </div>
    </template>
    <!-- END Sidebar Content -->

    <!-- Header Content Left -->
    <!-- Using the available v-slot, we can override the default Header content from layouts/partials/Header.vue -->
    <template #header-content-left>
      <!-- Toggle Sidebar -->
      <button
        type="button"
        class="btn btn-sm btn-alt-secondary me-2 d-lg-none"
        @click="store.sidebar({ mode: 'toggle' })"
      >
        <i class="fa fa-fw fa-bars"></i>
      </button>
      <!-- END Toggle Sidebar -->

      <!-- Toggle Mini Sidebar -->
      <!--      <button-->
      <!--        type="button"-->
      <!--        class="btn btn-sm btn-alt-secondary me-2 d-none d-lg-inline-block"-->
      <!--        @click="store.sidebarMini({ mode: 'toggle' })"-->
      <!--      >-->
      <!--        <i class="fa fa-fw fa-bars"></i>-->
      <!--      </button>-->
      <!-- END Toggle Mini Sidebar -->
    </template>
    <!-- END Header Content Left -->

    <!-- Header Content Right -->
    <!-- Using the available v-slot, we can override the default Header content from layouts/partials/Header.vue -->
    <template #header-content-right>
      <!-- Toggle Side Overlay -->
      <!-- <form class="d-none d-md-inline-block me-4">
        <div class="input-group input-group-sm">
          <input
            type="text"
            class="form-control form-control-alt"
            placeholder="Search.."
            id="page-header-search-input2"
            name="page-header-search-input2"
          />
          <span class="input-group-text border-0">
            <i class="fa fa-fw fa-search"></i>
          </span>
        </div>
      </form> -->

      <!-- <EIcon name="bell" class="text-muted me-4" role="button" />
      <EIcon name="flag" class="text-muted me-4" role="button" /> -->
      <div v-if="commonStore.rushModeOn" class="sw-container">
        <div style="width: 100px">{{ t("pages.placeholder.rush-mode") }}</div>
        <div
          style="cursor: pointer"
          @click="onChangeRushMode"
          class="form-check form-switch"
        >
          <input
            style="pointer-events: none"
            class="form-check-input"
            type="checkbox"
            :checked="isInRush"
          />
        </div>
        <CountDown
          @load="getStoreDetail"
          v-if="isInRush && rushEnd"
          :targetDate="rushEnd"
        />
        <div class="text-danger" v-else-if="isInRush">Permanent</div>
      </div>
      <ELanguageSwitcher />
      <EIcon
        name="right-from-bracket"
        class="text-muted ms-3"
        role="button"
        @click="onSignOut"
      />
      <!--      <button-->
      <!--        type="button"-->
      <!--        class="btn btn-sm btn-alt-secondary"-->
      <!--        @click="store.sideOverlay({ mode: 'toggle' })"-->
      <!--      >-->
      <!--        <i class="fa fa-fw fa-list-ul fa-flip-horizontal"></i>-->
      <!--      </button>-->
      <!-- END Toggle Side Overlay -->
    </template>
    <!-- END Header Content Right -->

    <template #page-top-content>
      <Alert />
    </template>

    <!-- Footer Content Left -->
    <!-- Using the available v-slot, we can override the default Footer content from layouts/partials/Footer.vue -->
    <template #footer-content-left>
      <strong>My App</strong>
      &copy; {{ store.app.copyright }}
    </template>
    <!-- END Footer Content Left -->
  </BaseLayout>
  <el-dialog
    :destroy-on-close="true"
    v-model="isShowModal"
    :title="t('pages.placeholder.ative_rush_mode')"
    width="500"
    @close="closeForm"
  >
    <div class="dialog-container">
      <el-select-v2
        v-model="rush_type"
        :options="locale === 'en' ? options : optionsNor"
        placeholder="Please select"
        size="large"
      />
      <el-radio-group style="margin-top: 15px" v-model="rush_for">
        <el-radio :value="1" size="large">{{
          t("pages.kiosk.fields.takeaway")
        }}</el-radio>
        <el-radio :value="2" size="large">{{ t("pages.kiosk.name") }}</el-radio>
        <el-radio :value="3" size="large">{{
          t("pages.placeholder.takeaway-kiosk")
        }}</el-radio>
      </el-radio-group>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <div class="cancel-button">
          <el-button class="cancel-button" @click="isShowModal = false">{{
            t("pages.placeholder.cancel")
          }}</el-button>
        </div>
        <el-button type="primary" @click="activeRushMode">
          {{ t("pages.placeholder.confirm") }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<style scoped>
.sw-container {
  display: flex;
  align-items: center;
  margin-right: 10px;
}
.cancel-button >>> .el-button {
  background-color: #ebeef2 !important;
  color: #212529 !important;
  border-color: #ebeef2 !important;
}
.cancel-button {
  display: inline-block;
}
.dialog-container {
  padding: 30px 30px 0 30px;
}
.store-con {
  padding: 10px 1.25rem;
}
</style>
