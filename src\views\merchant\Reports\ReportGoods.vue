<script setup>
import { reactive, onMounted, ref } from "vue";
import {
  Dataset,
  DatasetItem,
  DatasetInfo,
  DatasetSearch,
  DatasetShow,
} from "vue-dataset";
import EButton from "@/components/Elements/EButton.vue";
import { reportGoodService } from "@/services/report_good.service";
import EListEmpty from "@/components/Elements/EListEmpty.vue";
import { useTemplateStore } from "@/stores/template";
import FlatPickr from "vue-flatpickr-component";
import moment from "moment";
import { watch } from "vue";
import { useI18n } from "vue-i18n";
import useAppRouter from "@/composables/useRouter";
import { storeData } from "@/stores/storeData";

const store = useTemplateStore();
const dataFetch = storeData();
const { t, locale } = useI18n();

// Helper variables
const cols = reactive([
  {
    name: t("pages.report.fields.product"),
    field: "product",
    sort: "",
  },
  {
    name: t("pages.report.fields.type"),
    field: "type",
    sort: "",
  },
  {
    name: t("pages.report.fields.total_sales"),
    field: "sales",
    sort: "",
  },
  {
    name: t("pages.report.fields.unit_price"),
    field: "unit_price",
    sort: "",
  },
  {
    name: t("pages.report.fields.no_of_sales_ex_vat"),
    field: "sale_no_vat",
    sort: "",
  },
  {
    name: t("pages.report.fields.sales_inc_vat"),
    field: "sale_vat",
    sort: "",
  },
]);

const updateCols = () => {
  cols[0].name = t("pages.report.fields.product");
  cols[1].name = t("pages.report.fields.type");
  cols[2].name = t("pages.report.fields.total_sales");
  cols[3].name = t("pages.report.fields.unit_price");
  cols[4].name = t("pages.report.fields.no_of_sales_ex_vat");
  cols[5].name = t("pages.report.fields.sales_inc_vat");
};

// Watch for language changes
watch(locale, () => {
  updateCols();
});

const listGoodReports = ref([]);
const limit = ref(10);
const currentPage = ref(1);
const total = ref();
const visible = ref(true);
const dateRange = ref(defaultDateRange());
const handleChangeDate = (val) => {
  if (val?.length === 2) {
    dateRange.value = val;
  }
};
const configRange = ref({
  mode: "range",
  dateFormat: "d-m-Y",
  onChange: handleChangeDate,
  locale: { firstDayOfWeek: 1 },
});

const onFetchList = async () => {
  let start_date;
  let end_date;
  const dates = [];
  start_date = moment(dateRange.value?.[0], "YYYY-MM-DD");
  end_date = moment(dateRange.value?.[1], "YYYY-MM-DD");
  let currentDate = moment(dateRange.value?.[0], "YYYY-MM-DD").clone();
  while (
    currentDate.isSameOrBefore(
      moment(dateRange.value?.[1], "YYYY-MM-DD"),
      "day"
    )
  ) {
    dates.push(currentDate.format("YYYY-MM-DD"));
    currentDate.add(1, "day");
  }
  store.pageLoader({ mode: "on" });
  const response = await reportGoodService.getList({
    start_date: moment(start_date?._i).toISOString(),
    end_date: moment(end_date?._i).endOf("day").toISOString(),
    page: currentPage.value,
    limit: limit.value,
  });
  if (!response?.error) {
    listGoodReports.value = response?.data?.data;
    total.value = response?.data?.total;
  }
  store.pageLoader({ mode: "off" });
};

watch(limit, async () => {
  currentPage.value = 1;
  await onFetchList();
});

onMounted(async () => {
  if (dataFetch.storeData?.["merchant-report-goods"]?.length > 0) {
    await (listGoodReports.value =
      dataFetch.storeData?.["merchant-report-goods"]);
    total.value = dataFetch.total?.["merchant-report-goods"];
    store.pageLoader({ mode: "off" });
  } else {
    await onFetchList();
  }
  document.querySelectorAll("#datasetLength label").forEach((el) => {
    el.remove();
  });
  let selectLength = document.querySelector("#datasetLength select");
  if (selectLength) {
    selectLength.classList = "";
    selectLength.classList.add("form-select");
    selectLength.style.width = "80px";
  }
});

watch(dateRange, async () => {
  if (dateRange?.value?.length === 2) {
    await onFetchList();
  }
});

function defaultDateRange() {
  const startDate = moment().startOf("isoWeek").format("YYYY-MM-DD");
  const endDate = moment().endOf("isoWeek").format("YYYY-MM-DD");
  return [startDate, endDate];
}

async function handleExportCS() {
  let start_date;
  let end_date;
  start_date = moment(dateRange.value?.[0], "YYYY-MM-DD");
  end_date = moment(dateRange.value?.[1], "YYYY-MM-DD");
  const response = await reportGoodService.export({
    start_date: moment(start_date?._i).format("YYYY-MM-DD"),
    end_date: moment(end_date?._i).format("YYYY-MM-DD"),
  });
  const blod = new Blob([response], { type: "text/csv;charset=utf-8" });
  const url = URL.createObjectURL(blod);
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", "export_data.csv");
  link.click();
}
async function handleExportPDF() {
  let start_date;
  let end_date;
  start_date = moment(dateRange.value?.[0], "YYYY-MM-DD");
  end_date = moment(dateRange.value?.[1], "YYYY-MM-DD");
  const response = await reportGoodService.exportPDF({
    start_date: moment(start_date?._i).format("YYYY-MM-DD"),
    end_date: moment(end_date?._i).format("YYYY-MM-DD"),
  });
  const blod = new Blob([response], { type: "application/pdf" });
  const url = URL.createObjectURL(blod);
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", "export_pdf_good.pdf");
  link.click();
}

const router = useAppRouter();

const onNav = (id, type) => {
  if (type === "product") {
    router.pushByPath(`/products/form/${id}`);
  }
  if (type === "topping") {
    router.pushByPath(`/toppings/${id}/update`);
  }
};
</script>

<template>
  <BasePageHeading
    :title="t('pages.report.titles.goods_report')"
    :subtitle="t('pages.report.labels.label_head_list_good')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.report.labels.manages")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            {{ t("pages.report.titles.goods_report") }}
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <div class="row">
      <div class="col-12">
        <div class="d-flex justify-content-end items-center gap-1">
          <e-button type="info" size="sm" @click="() => handleExportCS()">
            {{ t("pages.report.labels.btn_export") }}
          </e-button>
          <e-button type="info" size="sm" @click="() => handleExportPDF()">
            {{ t("pages.report.labels.btn_pdf") }}
          </e-button>
        </div>
      </div>
      <hr class="my-4" />
    </div>
    <BaseBlock :title="t('pages.report.titles.goods_report')">
      <Dataset
        v-slot="{ ds }"
        :ds-data="listGoodReports"
        :ds-search-in="['id', 'name']"
      >
        <div class="row" :data-page-count="ds.dsPagecount">
          <div id="datasetLength" class="col-md-4 py-2">
            <DatasetShow v-show="false" :dsShowEntries="100" />
            <div class="form-inline">
              <select class="form-select" style="width: 80px" v-model="limit">
                <option :value="5">5</option>
                <option :value="10">10</option>
                <option :value="25">25</option>
                <option :value="50">50</option>
                <option :value="100">100</option>
              </select>
            </div>
          </div>
          <div class="col-md-4 py-2">
            <FlatPickr
              class="form-control"
              id="example-flatpickr-range"
              :placeholder="t('pages.report.placeholder.date_range')"
              :config="configRange"
              :v-model="dateRange"
            />
          </div>
          <div class="col-md-4 py-2">
            <DatasetSearch
              :ds-search-placeholder="t('pages.report.placeholder.seach')"
            />
          </div>
        </div>

        <hr />
        <div class="row" v-if="listGoodReports?.length">
          <div class="col-md-12">
            <div class="table-responsive">
              <table class="table table-striped mb-0">
                <thead>
                  <tr>
                    <th
                      v-for="th in cols"
                      :key="th.field"
                      :class="['sort', th.sort]"
                    >
                      {{ th.name }} <i class="gg-select float-end"></i>
                    </th>
                  </tr>
                </thead>
                <DatasetItem tag="tbody" class="fs-sm">
                  <template #default="{ row }">
                    <tr>
                      <td>
                        <div style="display: flex; gap: 6px">
                          <img
                            alt="product"
                            width="51"
                            height="51"
                            :src="row?.thumbnail"
                            v-if="row?.type === 'product'"
                            @error.prevent="
                              (e) =>
                                (e.target.src =
                                  '/assets/media/photos/default_photo.png')
                            "
                          />
                          <div style="display: flex; flex-direction: column">
                            <button
                              type="link"
                              class="btn btn-link"
                              @click="onNav(row?.id, row?.type)"
                            >
                              <span
                                style="
                                  font-size: 16px;
                                  line-height: 20px;
                                  font-weight: 400;
                                "
                                >{{ row.name }}</span
                              >
                            </button>
                            <span
                              style="
                                font-size: 16px;
                                line-height: 20px;
                                font-weight: 400;
                              "
                            >
                              {{ row.category_name }}</span
                            >
                          </div>
                        </div>
                      </td>
                      <td>
                        {{ row.type }}
                      </td>
                      <td>
                        {{ row.total_sale }}
                      </td>
                      <td>
                        {{ Number(row.unit_price).toFixed(2) + ",kr" }}
                      </td>
                      <td>{{ Number(row.sale_no_vat).toFixed(2) }}</td>
                      <td>{{ row.sale_vat }}</td>
                    </tr>
                  </template>
                </DatasetItem>
              </table>
            </div>
          </div>
        </div>
        <EListEmpty v-else />
        <div
          class="d-flex flex-md-row flex-column justify-content-between align-items-center"
        >
          <DatasetInfo class="py-3 fs-sm" />
          <el-pagination
            v-if="visible"
            v-model:current-page="currentPage"
            @current-change="onFetchList"
            background
            v-model:page-size="limit"
            layout="prev, pager, next"
            :prev-text="t('pages.footer.previous')"
            :next-text="t('pages.footer.next')"
            :total="total"
          />
        </div>
      </Dataset>
    </BaseBlock>
  </div>
</template>

<style lang="scss">
@import "flatpickr/dist/flatpickr.css";
@import "@/assets/scss/vendor/flatpickr";
</style>
