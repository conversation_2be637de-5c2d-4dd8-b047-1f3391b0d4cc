<script setup>
import { reactive, ref, watch } from "vue";
import {Dataset, DatasetItem, DatasetInfo, DatasetShow} from "vue-dataset";
import EListEmpty from "@/components/Elements/EListEmpty.vue";
import { productService } from "@/services/product.service";
import { useTemplateStore } from "@/stores/template";
import useNotify from "@/composables/useNotify";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { DndProvider } from 'vue3-dnd'
import {HTML5Backend} from "react-dnd-html5-backend";
import CategoryDndSort from "@/views/merchant/Categories/Components/CategoryDndSort.vue";
import {sortBy} from "lodash";

const props = defineProps({
  idCategory: {
    type: Number
  },
  hideModal: Function,
});

const {t} = useI18n();
const store = useTemplateStore()
const products = ref([])
const currentPage = ref(1)
const totalPage = ref(1)
const router = useRouter()

const getData = async (id) => {
  try {
    store.pageLoader({ mode: 'on' })
    const res = await productService.getList({
      category_id: id,
      limit: limit.value,
      page: currentPage.value
    })
    products.value = res.data?.data || [];
    totalPage.value = res.data.meta.last_page;
  } catch (error) {
    console.log(error);
  } finally {
    store.pageLoader({ mode: 'off' })
  }
}

const handleUpdatePriority = async (data, idCate) => {
  await productService.updatePriority({priorities: data});
  await getData(idCate)
}


let debounceTimeout;
const moveCard = async (dragIndex, hoverIndex) => {
  const item = products.value[dragIndex]
  products.value.splice(dragIndex, 1)
  products.value.splice(hoverIndex, 0, item)

  const listId = products.value.map((item) => item.id);
  clearTimeout(debounceTimeout);
  debounceTimeout = setTimeout(() => {
    handleUpdatePriority(listId, props.idCategory);
  }, 1200);
}

const goToPage = async (target) => {
  currentPage.value = target
  await getData(props.idCategory)
}

watch(() => props.idCategory, async (newValue, oldValue) => {
  if (newValue !== oldValue && newValue) {
    await getData(newValue);
  } else {
    products.value = []
  }
});

const onNavEdit = (id) => {
  props.hideModal()
  router.push(`/merchant/products/form/${id}`)
}

const {setNotify} = useNotify()
const onChangeInStock = async (id, inStock, row) => {
  const payload = {
    ...row,
    in_stock: !inStock,
  }
  try {
    const result = await productService.patch(id, payload)
    if (!result?.error) {
      row.in_stock = !inStock
    }
  } catch (e) {
    setNotify({
      title: 'Error',
      message: e?.message
    })
  }
}

const cols = reactive([
  {
    name: t(`pages.products.fields.product`),
    field: "product",
    sort: "",
  },
  {
    name: t(`pages.products.fields.category`),
    field: "category",
    sort: "",
  },
  {
    name: t(`pages.products.fields.in_stock`),
    field: "stock",
    sort: "",
  },
  {
    name: t(`pages.products.fields.printer`),
    field: "printer",
    sort: "",
  },
  {
    name: t(`pages.products.fields.price`),
    field: "price",
    sort: "",
  },
]);
const limit = ref(10)
watch(limit, async () => {
  currentPage.value = 1
  await getData(props.idCategory)
});
</script>

<template>
  <div class="p-4">
    <Dataset
      v-slot="{ ds }"
      :ds-data="products"
      :ds-sortby="sortBy"
      :ds-search-in="['name']"
    >
      <div class="row" :data-page-count="ds.dsPagecount">
        <div id="datasetLength" class="col-md-8 py-2">
          <DatasetShow v-show="false" :dsShowEntries="100" />
          <div class="form-inline">
            <select class="form-select" style="width: 80px;"  v-model="limit">
              <option value="5">5</option>
              <option value="10">10</option>
              <option value="25">25</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
          </div>
        </div>
        <div class="col-md-4 py-2">
        </div>
      </div>
      <div class="row" v-if="products?.length">
        <div class="col-md-12">
          <div class="table-responsive">
            <table class="table table-striped mb-0">
              <thead>
                <tr>
                  <th scope="col">{{ t('pages.products.fields.id') }}</th>
                  <th
                    v-for="th in cols"
                    :key="th.field"
                    :class="['sort', th.sort]"
                  >
                    {{ th.name }}
                  </th>
                </tr>
              </thead>
              <DndProvider :backend="HTML5Backend">
                <DatasetItem tag="tbody" class="fs-sm">
                <template #default="{ row, rowIndex }">
                  <CategoryDndSort :id="row?.id" :index="rowIndex" :move-card="moveCard">
                    <th scope="row">{{ row?.id }}</th>
                    <td>
                      <img
                        @click="onNavEdit(row?.id)"
                        :src="row?.thumbnail"
                        width="60"
                        height="60"
                        class="me-2 cursor-pointer"
                        @error.prevent="
                          (e) =>
                            (e.target.src =
                              '/assets/media/photos/default_photo.png')
                        "
                      />
                      <span @click="onNavEdit(row?.id)" class="cursor-pointer btn btn-link" >{{ row?.name }}</span>
                    </td>
                    <td>{{ row?.category?.name }}</td>
                    <td style="max-width: 150px">
                      <div class="form-check form-switch">
                        <input
                          class="form-check-input"
                          type="checkbox"
                          value=""
                          :checked="row?.in_stock"
                          @change="onChangeInStock(row?.id, row?.in_stock, row)"
                        />
                      </div>
                    </td>
                    <td>{{ row?.printer?.name }}</td>
                    <td>{{ row?.ordinary_price }}</td>
                  </CategoryDndSort>
                </template>
              </DatasetItem>
              </DndProvider>
            </table>
          </div>
        </div>
      </div>
      <EListEmpty v-else />
      <div
        class="d-flex flex-md-row flex-column justify-content-between align-items-center"
      >
        <DatasetInfo class="py-3 fs-sm" />
        <ul class="pagination flex-wrap py-3 fs-sm">
          <li class="page-item" :class="{'disabled': currentPage === 1 || totalPage === 1}" @click="goToPage(currentPage - 1)">
            <a class="page-link" href="#" tabindex="-1" aria-disabled="true">{{ t('pages.footer.previous') }}</a>
          </li>
          <li class="page-item" v-for="item in totalPage" :class="{'active': item === currentPage}" :key="item" @click="goToPage(item)">
            <a class="page-link" href="#">{{ item }}</a>
          </li>
          <li class="page-item" :class="{'disabled': currentPage === totalPage || totalPage === 1}" @click="goToPage(currentPage + 1)">
            <a class="page-link" href="#" tabindex="-1" aria-disabled="true">{{ t('pages.footer.next') }}</a>
          </li>
        </ul>
      </div>
    </Dataset>
  </div>
</template>

<style lang="scss">
.done-icon {
  color: #019521;
  font-size: 26px;
}
.error-icon {
  color: #cd0808;
  font-size: 26px;
}
.disabled {
  pointer-events: none;
}
</style>
