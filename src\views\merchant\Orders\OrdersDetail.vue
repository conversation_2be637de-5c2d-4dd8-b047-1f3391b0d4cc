<script setup>
import EIcon from "@/components/Elements/EIcon.vue";
import useAppRouter from "@/composables/useRouter";
import { orderService } from "@/services/order.service";
import { useTemplateStore } from "@/stores/template";
import moment from "moment";
import { computed, onMounted, reactive, ref, watch, onBeforeMount } from "vue";
import { Dataset, DatasetItem } from "vue-dataset";
import { useI18n } from "vue-i18n";
import Trans from "@/i18n/i18nUtils";
import { useRoute, useRouter } from "vue-router";
import { scrollTo } from "@/stores/scollItemInlist";
import EListEmpty from "@/components/Elements/EListEmpty.vue";
import useNotify from "@/composables/useNotify";
import { storeData } from "@/stores/storeData";

const router = useAppRouter();
const router_link = useRouter();
const route = useRoute();
const store = useTemplateStore();
const { t, locale } = useI18n();
const title = ref(t("pages.order.titles.show"));
let data = reactive();
const { id } = route.params;
const idOrder = route.params?.id;
const transaction_id = route.query?.transaction_id;
let vat = reactive([]);
const dataFetch = storeData();

const transactionStatus = ref();
const scrollStore = scrollTo();

const { setNotify } = useNotify();

const detaiTitle = ref(t("pages.order.name"));
const cols = reactive([
  {
    name: t("pages.order.fields.product"),
    field: "id",
    sort: "",
  },
  {
    name: t("pages.order.fields.unit_price"),
    field: "Order",
    sort: "",
  },
  {
    name: t("pages.order.fields.tax"),
    field: "customer",
    sort: "",
  },
  {
    name: t("pages.order.fields.qty"),
    field: "queue_nr",
    sort: "",
  },
  {
    name: t("pages.order.fields.price"),
    field: "status",
    sort: "",
  },
]);

const colsTransaction = reactive([
  {
    name: t("pages.order.fields.id"),
    field: "id",
    sort: "",
  },
  {
    name: t("pages.order.fields.payment_method"),
    field: "Order",
    sort: "",
  },
  {
    name: t("pages.order.fields.amount"),
    field: "customer",
    sort: "",
  },
  {
    name: t("pages.order.fields.date"),
    field: "order_nr",
    sort: "",
  },
  {
    name: t("pages.order.fields.status_table"),
    field: "queue_nr",
    sort: "",
  },
]);

const updateCols = () => {
  cols[0].name = t("pages.order.fields.product");
  cols[1].name = t("pages.order.fields.unit_price");
  cols[2].name = t("pages.order.fields.tax");
  cols[3].name = t("pages.order.fields.qty");
  cols[4].name = t("pages.order.fields.price");
  cols[5].name = t("pages.order.fields.id");
  cols[6].name = t("pages.order.fields.payment_method");
  cols[7].name = t("pages.order.fields.amount");
  cols[8].name = t("pages.order.fields.date");
  cols[9].name = t("pages.order.fields.status_table");
};

// Watch for language changes
watch(locale, () => {
  updateCols();
});

const apiGetItem = async () => {
  store.pageLoader({ mode: "on" });
  const response = ref();

  if (transaction_id || query.value) {
    response.value = await orderService.getDetailRefund(id);
  } else {
    response.value = await orderService.getDetail(id);
  }
  await apiGetVat(response.value.data?.code);
  data = response.value?.data || [];
  transactionStatus.value = response.value?.data?.status;
  if (response.value?.data?.detail?.length) {
    data = response.value?.data || [];
    detaiTitle.value = t("pages.order.titles.detail", {
      item: response.value?.data.code_show,
    });
    store.pageLoader({ mode: "off" });
  }
};

const apiGetVat = async (code) => {
  try {
    const response = await orderService.getVat(code);
    vat = response.data;
  } catch (error) {
    console.log("error :", error);
  }
};
const detailTileType = () => {
  if (order.value?.detail_type === "kiosk") {
    if (order.value?.type === "takeaway" && order.value?.is_preorder) {
      return `Takeaway Scheduled: ${order.value?.order_date}`;
    } else if (order.value?.type === "takeaway") {
      return "Kiosk - Takeaway";
    } else if (order.value?.table) {
      return `To Table - ${order.value?.table?.table_id}`;
    } else if (order.value?.type === "inrestaurant") {
      return "Kiosk - Dine In";
    } else {
      return "Kiosk - Dine In";
    }
  } else if (order.value?.detail_type === "takeaway") {
    if (order.value?.is_preorder) {
      return "Online - Schedule";
    } else {
      return "Online - ASAP";
    }
  }
};
const detailTileTypeNor = () => {
  if (order.value?.detail_type === "kiosk") {
    if (order.value?.type === "takeaway" && order.value?.is_preorder) {
      return `Takeaway planlagt: ${order.value?.order_date}`;
    } else if (order.value?.type === "takeaway") {
      return "Kiosk - Takeaway";
    } else if (order.value?.table) {
      return `Til bordet - ${order.value?.table?.table_id}`;
    } else if (order.value?.type === "inrestaurant") {
      return "Kiosk - Spise inne";
    } else {
      return "Kiosk - Spise inne";
    }
  } else if (order.value?.detail_type === "takeaway") {
    if (order.value?.is_preorder) {
      return "Online - Tidsplan";
    } else {
      return "Online - ASAP";
    }
  }
};
const typeOrder = ref("");
const typeOrderNor = ref("");

onMounted(async () => {
  try {
    await apiGetItem();
    typeOrder.value = detailTileType();
    typeOrderNor.value = detailTileTypeNor();
  } catch (error) {
    console.error("Error fetching data:", error);
  }
});

const itemData = computed(() => {
  return data.detail;
});
const order = computed(() => {
  return data;
});

const transactionData = computed(() => {
  if (!data.transaction?.length) {
    return [data.transaction];
  }
  // // return [
  // //   data.transaction.find((item) => item.status === "completed")
  // //     ? data.transaction.find((item) => item.status === "completed")
  // //     : data.transaction[0],
  // // ];
  return data.transaction;
});
const handleClickRefund = async () => {
  console.log(data);
  for (let i = 0; i < data?.transaction?.length; i++) {
    if (data?.transaction[i]?.status === "completed") {
      if (data?.transaction[i]?.payment_gateway === "vipps") {
        try {
          store.pageLoader({ mode: "on" });
          const response = await orderService.refundPaymentVipps(data.code);
          transactionStatus.value = "refunded";
          await apiGetItem();
          setNotify({
            title: "Successful",
            message: response?.message,
            type: "success",
          });
        } catch (error) {
          setNotify({
            title: "Error",
            message: error?.message,
          });
        } finally {
          store.pageLoader({ mode: "off" });
        }
      }
      if (data?.transaction[i]?.payment_gateway === "stripe") {
        try {
          store.pageLoader({ mode: "on" });
          const response = await orderService.refundPaymentStripe(data.code);
          await apiGetItem();
          transactionStatus.value = "refunded";
          setNotify({
            title: "Successful",
            message: response?.message,
            type: "success",
          });
        } catch (error) {
          setNotify({
            title: "Error",
            message: error?.message,
          });
        } finally {
          store.pageLoader({ mode: "off" });
        }
      }
      if (data?.transaction[i]?.payment_gateway === "cash") {
        try {
          store.pageLoader({ mode: "on" });
          const response = await orderService.refundPaymentCash(data.code);
          await apiGetItem();
          transactionStatus.value = "refunded";
          setNotify({
            title: "Successful",
            message: response?.message,
            type: "success",
          });
        } catch (error) {
          setNotify({
            title: "Error",
            message: error?.message,
          });
        } finally {
          store.pageLoader({ mode: "off" });
        }
      }
      if (data?.transaction[i]?.payment_gateway === "bambora") {
        try {
          store.pageLoader({ mode: "on" });
          const response = await orderService.refundPaymentBambora(data.code);
          await apiGetItem();
          transactionStatus.value = "refunded";
          setNotify({
            title: "Successful",
            message: response?.message,
            type: "success",
          });
        } catch (error) {
          setNotify({
            title: "Error",
            message: error?.message,
          });
        } finally {
          store.pageLoader({ mode: "off" });
        }
      }
    }
  }
};
const onRedirectLink = async (link) => {
  return window.open(`${link}`);
};
const query = ref();
onBeforeMount(() => {
  // Thực hiện các logic cần thiết trước khi render
  query.value = route.query?.transaction_id;
});
const goToOriginalOrder = () => {
  const route = router_link.resolve({
    path: Trans.i18nRouteByPath("/orders/" + data.order_id),
    query: { transaction_id: null },
  });
  window.open(route.href, "_blank"); // Mở trong tab mới
};
const goToRefundOrder = () => {
  const route = router_link.resolve({
    path: Trans.i18nRouteByPath("/orders/" + data?.refunds[0].id),
    query: { transaction_id: data?.refunds[0].transaction_id },
  });
  window.open(route.href, "_blank"); // Mở trong tab mới
};
</script>
<template>
  <BasePageHeading
    :title="title"
    :go-back="true"
    :subtitle="t('pages.order.labels.label_head_list')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.order.labels.manages")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <router-link to="/merchant/orders">{{
              t("pages.order.titles.list")
            }}</router-link>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <span>{{ t("pages.order.titles.show") }}</span>
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>
  <div class="container">
    <div class="row justify-content-center pt-sm-2 pt-md-3">
      <div class="col-sm-10 col-md-8">
        <div div class="title-type">
          {{ locale === "en" ? typeOrder : typeOrderNor }}
        </div>
        <BaseBlock :title="detaiTitle">
          <template #options>
            <e-icon
              @click="
                () => {
                  router.back();
                  dataFetch.setData([], 'merchant-orders-list');
                  dataFetch.setTotal(0, 'merchant-orders-list');
                  scrollStore.getElement(idOrder);
                }
              "
              name="arrow-left"
              role="button"
              class="icon_arrow_left"
            />
          </template>
          <div>
            <h4 class="mb-2 fw-bold">{{ t("pages.order.labels.items") }}</h4>
            <Dataset v-if="data && data.detail?.length" :ds-data="itemData">
              <div class="row" v-if="itemData">
                <div class="col-md-12">
                  <div class="table-responsive">
                    <table class="table table-striped mb-0">
                      <thead>
                        <tr>
                          <th
                            v-for="(th, index) in cols"
                            :key="th.field"
                            :class="['sort', th.sort]"
                            @click="onSort($event, index)"
                          >
                            {{ th.name }} <i class="gg-select float-end"></i>
                          </th>
                        </tr>
                      </thead>
                      <DatasetItem tag="tbody" class="fs-sm">
                        <template #default="{ row }">
                          <tr>
                            <td
                              class="products"
                              v-if="row?.product"
                              :style="
                                row?.item_status === 'refund'
                                  ? 'text-decoration: line-through; color: #9d9c9c'
                                  : ''
                              "
                              style="width: 80px"
                            >
                              {{ row?.product?.name }}
                            </td>
                            <td
                              class="products"
                              v-if="row?.product && row?.product?.price"
                            >
                              {{ row?.product?.price + ",-kr" }}
                            </td>
                            <td
                              class="products"
                              v-if="row?.product && row?.product?.vat_rate"
                            >
                              {{ row?.product?.vat_rate + "%" }}
                            </td>
                            <td class="products" v-if="row?.quantity">
                              {{ "x" + row?.quantity }}
                            </td>
                            <td
                              class="products"
                              v-if="
                                row?.product &&
                                row?.product?.price &&
                                row?.product?.vat_rate
                              "
                            >
                              {{
                                Number(row?.product?.price) *
                                  Number(row?.quantity) +
                                ",-kr"
                              }}
                            </td>
                          </tr>
                          <template
                            v-if="
                              row?.custom_detail?.category_for_extras?.toppings
                                ?.length
                            "
                          >
                            <tr
                              class="test"
                              v-for="th in row?.custom_detail
                                ?.category_for_extras?.toppings"
                              :key="th.field"
                            >
                              <td
                                :style="
                                  row?.item_status === 'refund'
                                    ? 'text-decoration: line-through; color: #9d9c9c'
                                    : 'color: #1491aa'
                                "
                              >
                                + {{ th?.topping?.name }}
                              </td>
                              <td>{{ th?.topping?.price + ",-kr" }}</td>
                              <td>{{ th?.topping?.vat_rate }}%</td>
                              <td>x{{ th?.quantity }}</td>
                              <td>
                                {{
                                  Number(th?.topping?.price) *
                                  Number(th?.quantity)
                                }},-kr
                              </td>
                            </tr>
                          </template>
                          <template
                            v-if="
                              row?.custom_detail?.category_for_extras?.products
                                ?.length
                            "
                          >
                            <tr
                              class="test"
                              v-for="th in row?.custom_detail
                                ?.category_for_extras?.products"
                              :key="th.field"
                            >
                              <td
                                :style="
                                  row?.item_status === 'refund'
                                    ? 'text-decoration: line-through; color: #9d9c9c'
                                    : 'color: #1491aa'
                                "
                              >
                                +{{ th?.product?.name }}
                              </td>
                              <td>{{ th?.product?.price + ",-kr" }}</td>
                              <td>{{ th?.product?.vat_rate }}%</td>
                              <td>x{{ th?.quantity }}</td>
                              <td>
                                {{
                                  Number(th?.product?.price) *
                                  Number(th?.quantity)
                                }},-kr
                              </td>
                            </tr>
                          </template>
                          <template
                            v-if="
                              row?.custom_detail?.remove_something?.toppings
                                ?.length
                            "
                          >
                            <tr
                              class="test"
                              v-for="th in row?.custom_detail?.remove_something
                                ?.toppings"
                              :key="th.field"
                            >
                              <td
                                :style="
                                  row?.item_status === 'refund'
                                    ? 'text-decoration: line-through; color: #9d9c9c'
                                    : 'color: #d61f47'
                                "
                              >
                                - {{ th?.topping?.name }}
                              </td>
                              <td>{{ th?.topping?.price + ",-kr" }}</td>
                              <td>{{ th?.topping?.vat_rate }}%</td>
                              <td>x{{ th?.quantity }}</td>
                              <td>
                                <span
                                  v-if="
                                    Number(th?.topping?.price) *
                                    Number(th?.quantity)
                                  "
                                  >-</span
                                >
                                {{
                                  Number(th?.topping?.price) *
                                  Number(th?.quantity)
                                }},-kr
                              </td>
                            </tr>
                          </template>
                          <template
                            v-if="
                              row?.custom_detail?.remove_something?.products
                                ?.length
                            "
                          >
                            <tr
                              class="test"
                              v-for="th in row?.custom_detail?.remove_something
                                ?.products"
                              :key="th.field"
                            >
                              <td
                                :style="
                                  row?.item_status === 'refund'
                                    ? 'text-decoration: line-through; color: #9d9c9c'
                                    : 'color: #d61f47'
                                "
                              >
                                - {{ th?.product?.name }}
                              </td>
                              <td>{{ th?.product?.price + ",-kr" }}</td>
                              <td>{{ th?.product?.vat_rate }}%</td>
                              <td>x{{ th?.quantity }}</td>
                              <td>
                                <span
                                  v-if="
                                    Number(th?.product?.price) *
                                    Number(th?.quantity)
                                  "
                                  >-</span
                                >
                                {{
                                  Number(th?.product?.price) *
                                  Number(th?.quantity)
                                }},-kr
                              </td>
                            </tr>
                          </template>
                          <template
                            v-if="
                              row?.custom_detail?.choose_between?.toppings
                                ?.length
                            "
                          >
                            <tr
                              class="test"
                              v-for="th in row?.custom_detail?.choose_between
                                ?.toppings"
                              :key="th.field"
                            >
                              <td>*{{ th?.topping?.name }}</td>
                              <td>{{ th?.topping?.price + ",-kr" }}</td>
                              <td>{{ th?.topping?.vat_rate }}%</td>
                              <td>x{{ th?.quantity }}</td>
                              <td>
                                {{
                                  Number(th?.topping?.price) *
                                  Number(th?.quantity)
                                }},-kr
                              </td>
                            </tr>
                          </template>
                          <template
                            v-if="
                              row?.custom_detail?.choose_between?.products
                                ?.length
                            "
                          >
                            <tr
                              class="test"
                              v-for="th in row?.custom_detail?.choose_between
                                ?.products"
                              :key="th.field"
                            >
                              <td>*{{ th?.product?.name }}</td>
                              <td>{{ th?.product?.price + ",-kr" }}</td>
                              <td>{{ th?.product?.vat_rate }}%</td>
                              <td>x{{ th?.quantity }}</td>
                              <td>
                                {{
                                  Number(th?.product?.price) *
                                  Number(th?.quantity)
                                }},-kr
                              </td>
                            </tr>
                          </template>
                          <template
                            v-if="
                              row?.custom_detail?.change_with?.add?.toppings
                                ?.length
                            "
                          >
                            <tr
                              class="test"
                              v-for="th in row?.custom_detail?.change_with?.add
                                ?.toppings"
                              :key="th.field"
                            >
                              <td
                                :style="
                                  row?.item_status === 'refund'
                                    ? 'text-decoration: line-through; color: #9d9c9c'
                                    : 'color: #ea580b'
                                "
                              >
                                * {{ th?.topping?.name }}
                              </td>
                              <td>{{ th?.topping?.price + ",-kr" }}</td>
                              <td>{{ th?.topping?.vat_rate }}%</td>
                              <td>x{{ th?.quantity }}</td>
                              <td>
                                {{
                                  Number(th?.topping?.price) *
                                  Number(th?.quantity)
                                }},-kr
                              </td>
                            </tr>
                          </template>
                          <template
                            v-if="
                              row?.custom_detail?.change_with?.add?.products
                                ?.length
                            "
                          >
                            <tr
                              class="test"
                              v-for="th in row?.custom_detail?.change_with?.add
                                ?.products"
                              :key="th.field"
                            >
                              <td
                                :style="
                                  row?.item_status === 'refund'
                                    ? 'text-decoration: line-through; color: #9d9c9c'
                                    : 'color: #ea580b'
                                "
                              >
                                *{{ th?.product?.name }}
                              </td>
                              <td>{{ th?.product?.price + ",-kr" }}</td>
                              <td>{{ th?.product?.vat_rate }}%</td>
                              <td>x{{ th?.quantity }}</td>
                              <td>
                                {{
                                  Number(th?.product?.price) *
                                  Number(th?.quantity)
                                }},-kr
                              </td>
                            </tr>
                          </template>
                          <!-- <template
                            v-if="
                              row?.custom_detail?.change_with?.remove?.toppings
                                ?.length
                            "
                          >
                            <tr
                              class="test"
                              v-for="th in row?.custom_detail?.change_with
                                ?.remove?.toppings"
                              :key="th.field"
                            >
                              <td>*{{ th?.topping?.name }}</td>
                              <td>{{ th?.topping?.price + ",-kr" }}</td>
                              <td>{{ th?.topping?.vat_rate }}%</td>
                              <td>x{{ th?.quantity }}</td>
                              <td>
                                <span v-if="th?.topping?.price === '0'">-</span
                                >{{
                                  Number(th?.topping?.price) *
                                  Number(th?.quantity)
                                }},-kr
                              </td>
                            </tr>
                          </template>
                          <template
                            v-if="
                              row?.custom_detail?.change_with?.remove?.products
                                ?.length
                            "
                          >
                            <tr
                              class="test"
                              v-for="th in row?.custom_detail?.change_with
                                ?.remove?.products"
                              :key="th.field"
                            >
                              <td>*{{ th?.product?.name }}</td>
                              <td>{{ th?.product?.price + ",-kr" }}</td>
                              <td>{{ th?.product?.vat_rate }}%</td>
                              <td>x{{ th?.quantity }}</td>
                              <td>
                                -{{
                                  Number(th?.product?.price) *
                                  Number(th?.quantity)
                                }},-kr
                              </td>
                            </tr>
                          </template> -->
                        </template>
                      </DatasetItem>
                    </table>
                  </div>
                </div>
              </div>
            </Dataset>
            <div class="d-flex justify-content-end">
              <ul class="list-group border-none">
                <li class="list-group-item border-0 fw-bold">
                  {{ t("pages.order.labels.item_subtotal") }}:
                  <span>{{ data?.amount_total_without_vat + ",-kr" }}</span>
                </li>
                <li
                  v-for="th in vat"
                  :key="th.vat_rate"
                  class="list-group-item border-0 fw-bold"
                >
                  <div
                    v-if="
                      Number(th?.price - th?.purchase_price).toFixed(2) !== 0.0
                    "
                  >
                    {{ t("pages.order.labels.mva", { item: th?.vat_rate }) }}:
                    <span>{{
                      Number(th?.price - th?.purchase_price).toFixed(2) + ",-kr"
                    }}</span>
                  </div>
                </li>
                <li
                  v-if="data && data?.tip !== '0.00'"
                  class="list-group-item border-0 fw-bold"
                >
                  {{ t("pages.order.labels.tip") }}:
                  <span v-if="data && data?.tip !== '0.00'">{{
                    data?.tip + ",-kr"
                  }}</span>
                </li>
                <li class="list-group-item border-0 fw-bold">
                  {{ t("pages.order.labels.order_total") }}:
                  <span v-if="data && data.amount_total">{{
                    Number(data?.amount_total).toFixed(2) + ",-kr"
                  }}</span>
                </li>
              </ul>
            </div>
          </div>
          <div class="mt-62">
            <div
              style="
                display: flex;
                flex-direction: row;
                gap: 5px;
                align-items: center;
              "
            >
              <h4 class="mb-2 fw-bold">
                {{ t("pages.order.labels.customer_receipts") }}
              </h4>
              <a
                :v-if="!!data?.receipt_pdf_url"
                class="mb-2"
                href="javascript:void(0)"
                @click="onRedirectLink(data.receipt_pdf_url)"
                >View</a
              >
            </div>
          </div>
          <div class="mt-62">
            <div
              style="
                display: flex;
                flex-direction: row;
                gap: 5px;
                align-items: center;
              "
            >
              <h4 class="mb-2 fw-bold">
                {{ t("pages.order.labels.kitchen_receipts") }}
              </h4>
              <a
                :v-if="!!data?.receipt_pdf_url"
                class="mb-2"
                href="javascript:void(0)"
                @click="onRedirectLink(data.receipt_kitchen_pdf_url)"
                >View</a
              >
            </div>
          </div>
          <div
            class="mt-62"
            v-if="
              !!data?.capture_url &&
              data.transaction.some((t) => t.payment_gateway === 'bambora_pos')
            "
          >
            <div
              style="
                display: flex;
                flex-direction: row;
                gap: 5px;
                align-items: center;
              "
            >
              <h4
                v-if="
                  !!data?.capture_url &&
                  data.transaction.some(
                    (t) => t.payment_gateway === 'bambora_pos'
                  )
                "
                class="mb-2 fw-bold"
              >
                {{ t("pages.order.labels.bambora_receipts") }}
              </h4>
              <a
                v-if="
                  !!data?.capture_url &&
                  data.transaction.some(
                    (t) => t.payment_gateway === 'bambora_pos'
                  )
                "
                class="mb-2"
                href="javascript:void(0)"
                @click="onRedirectLink(data.capture_url)"
                >View</a
              >
            </div>
          </div>
          <div class="mt-62 mb-4">
            <h4 class="mb-2 fw-bold">
              {{ t("pages.order.labels.transaction") }}
            </h4>
            <Dataset v-if="data && data.transaction" :ds-data="transactionData">
              <div class="row" v-if="transactionData?.length">
                <div class="col-md-12">
                  <div class="table-responsive">
                    <table class="table table-striped mb-0">
                      <thead class="table-head">
                        <tr>
                          <th
                            v-for="(th, index) in colsTransaction"
                            :key="th.field"
                            :class="['sort', th.sort]"
                            @click="onSort($event, index)"
                          >
                            {{ th.name }} <i class="gg-select float-end"></i>
                          </th>
                        </tr>
                      </thead>
                      <DatasetItem tag="tbody" class="fs-sm">
                        <template #default="{ row }">
                          <tr>
                            <td style="width: 80px">{{ row?.id }}</td>
                            <td>{{ row?.payment_gateway }}</td>
                            <td>
                              <span v-if="transaction_id">-</span
                              >{{ Number(row?.amount_total).toFixed(2) }}
                            </td>
                            <td>
                              {{
                                moment(row?.created_at).format(
                                  "DD.MM.YYYY HH:mm:ss"
                                )
                              }}
                            </td>
                            <td>{{ row?.status }}</td>
                          </tr>
                        </template>
                      </DatasetItem>
                    </table>
                  </div>
                </div>
              </div>
            </Dataset>
            <EListEmpty v-else />
          </div>
        </BaseBlock>
      </div>
      <div class="row g-3 mt-0 px-16 g-4 justify-content-center mb-4">
        <div class="mt-0 d-flex flex-column gap-2 col-sm-5 col-md-4 item-card">
          <h4 class="mb-0 lh-36">{{ t("pages.order.labels.status") }}</h4>
          <div v-if="transaction_id">
            <div style="display: flex">
              <div type="button" class="btn btn-alt-primary bg-gray">
                <img src="@/assets/images/refund.png" alt="" />
                {{ t("pages.order.labels.refunded") }}
              </div>
              <a class="link-href" @click="goToOriginalOrder">
                {{ t("pages.order.labels.go_to_original_order") }}
              </a>
            </div>
          </div>
          <div
            v-if="
              data &&
              transactionStatus === 'completed' &&
              !transaction_id &&
              !data.refunded_at
            "
            type="button"
            class="btn btn-success"
          >
            <i
              class="fa-regular fa-check-circle done-icon"
              style="color: #fff; font-size: 16px"
            ></i>
            {{ t("pages.order.labels.completed") }}
          </div>
          <div
            v-else-if="
              (data && transactionStatus === 'refunded' && !transaction_id) ||
              (data && data.refunded_at)
            "
          >
            <div style="display: flex">
              <div type="button" class="btn btn-alt-primary bg-gray">
                <img src="@/assets/images/refund.png" alt="" />
                {{ t("pages.order.labels.refunded") }}
              </div>
              <a class="link-href" @click="goToRefundOrder">
                {{ t("pages.order.labels.go_to_refund_order") }}
              </a>
            </div>
          </div>
          <div v-else-if="!transaction_id" type="button" class="btn btn-danger">
            <i
              class="fa fa-times-circle error-icon"
              style="color: #fff; font-size: 16px"
            ></i>
            {{ t("pages.order.labels.uncompleted") }}
          </div>
          <button
            v-if="
              data &&
              transactionStatus === 'completed' &&
              !transaction_id &&
              !data.refunded_at &&
              !data.transaction.some((t) => t.payment_gateway === 'bambora_pos')
            "
            type="button"
            class="btn btn-alt-primary bg-blue"
            @click="handleClickRefund"
          >
            <img src="@/assets/images/refund.png" alt="" />
            {{ t("pages.order.labels.refund") }}
          </button>
          <span
            v-if="
              data &&
              transactionStatus !== 'uncompleted' &&
              !data.refunded_at &&
              !transaction_id &&
              transactionStatus !== 'refunded'
            "
          >
            {{ t("pages.order.labels.payment_capture") }}
            <i class="fa-regular fa-check-circle" style="color: #019521"></i>
          </span>
        </div>
        <div class="mt-0 col-sm-5 col-md-4 item-card">
          <h4 class="mb-0 lh-36">{{ t("pages.order.labels.information") }}</h4>
          <dl class="row pl-0">
            <dt class="col-sm-4 pe-0">{{ t("pages.order.labels.name") }}</dt>
            <dd
              class="col-sm-8 pl-0"
              v-if="data && data.customer && data.customer.name"
            >
              {{ data.customer.name }}
            </dd>
            <dd class="col-sm-8 pl-0" v-else></dd>
            <dt class="col-sm-4 pe-0">{{ t("pages.order.labels.email") }}</dt>
            <dd
              class="col-sm-8 pl-0"
              v-if="data && data.customer && data.customer.email"
            >
              {{ data.customer.email }}
            </dd>
            <dd class="col-sm-8 pl-0" v-else></dd>
            <dt class="col-sm-4 pe-0">{{ t("pages.order.labels.phone") }}</dt>
            <dd
              class="col-sm-8 pl-0"
              v-if="data && data.customer && data.customer.phone"
            >
              {{ data.customer.phone }}
            </dd>
            <dd class="col-sm-8 pl-0" v-else></dd>

            <dt class="col-sm-4 pe-0">
              {{ t("pages.order.labels.type_order") }}
            </dt>
            <dd class="col-sm-8 pl-0">{{ data?.type }}</dd>

            <dt class="col-sm-4 pe-0">
              {{ t("pages.order.labels.table_no") }}
            </dt>
            <dd v-if="data && data?.table?.table_id" class="col-sm-8 pl-0">
              {{ data?.table?.table_id }}
            </dd>
            <dd v-else class="col-sm-8 pl-0">
              {{ t("pages.order.labels.no_table_selected") }}
            </dd>
            <dt class="col-sm-4 pe-0">{{ t("pages.order.labels.note") }}</dt>
            <dd class="col-sm-8 pl-0">{{ data?.note }}</dd>
            <dt class="col-sm-4 pe-0">{{ t("pages.order.labels.type") }}</dt>
            <dd class="col-sm-8 pl-0 text-capitalize">
              {{
                data?.is_preorder
                  ? t("pages.order.labels.schedule")
                  : t("pages.order.labels.asap")
              }}
            </dd>
            <dt class="col-sm-4 pe-0">
              {{ t("pages.order.labels.order_date") }}
            </dt>
            <dd class="col-sm-8 pl-0 text-capitalize">
              {{
                moment(data?.order_date, "YYYY-MM-DD HH:mm:ss").format(
                  "DD-MM-YYYY HH:mm:ss"
                )
              }}
            </dd>
            <dt class="col-sm-4 pe-0">
              {{ t("pages.order.labels.marketing_accepted") }}
            </dt>
            <dd class="col-sm-8 pl-0 text-capitalize">
              {{ data?.marketing_accepted }}
            </dd>

            <dt
              class="col-sm-4 pe-0"
              v-if="data && transactionStatus === 'uncompleted'"
            >
              {{ t("pages.order.labels.failure_reason") }}
            </dt>
            <dd
              v-if="data && transactionStatus === 'uncompleted'"
              class="col-sm-8 pl-0 text-capitalize"
            >
              {{ data?.failure_reason }}
            </dd>
          </dl>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.item-card {
  padding-inline: 25px;
  padding-block: 20px;
  background-color: #fff;
  border-radius: 0.5rem;
}
.px-16 {
  padding-inline: 28px;
  gap: 7px;
}
.lh-36 {
  line-height: 36px;
}
.mt-62 {
  margin-top: 62px;
}
table > thead {
  border-block: solid;
  border-block-color: #e5e7eb;
}
.block-content {
  padding-inline: 0 !important;
}
button {
  width: fit-content;
}
.bg-blue {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 6px;
  background-color: #0c84c3;
  width: fit-content;
  color: #fff;
}
.btn-success {
  width: fit-content;
}
.btn-danger {
  width: fit-content;
}
.bg-gray {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 6px;
  background-color: #464c51 !important;
  color: #fff;
  width: fit-content;
}
.test td {
  --bs-table-bg-type: #fff !important;
}
.test td:first-child {
  padding-left: 25px;
}
.products td {
  --bs-table-bg-type: #212529 !important;
}
.title-type {
  color: #143e66;
  padding: 20px;
  margin-bottom: 20px;
  background-color: #cfe4ff;
  display: flex;
  justify-content: center;
}
.link-href {
  padding-top: 5px;
  padding-left: 5px;
  cursor: pointer;
  color: #a48ad4;
}
</style>
