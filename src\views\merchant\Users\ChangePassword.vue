<script setup>
import { useI18n } from 'vue-i18n';

const props = defineProps(['v$','state'])
const {v$, state} = props;
const {t} = useI18n()

</script>

<template>
    <div class="row">
      <div class="col-md-12">
            <div class="row justify-content-center">
              <div class="col-sm-10 col-md-8">
                <div class="mb-4">
                  <label class="form-label" for="form-id"
                    >{{ t('pages.user.titles.new_password') }}<span class="text-danger">*</span></label
                  >
                  <input
                    type="password"
                    class="form-control"
                    id="form-password"
                    name="form-password"
                    :placeholder="t('pages.user.placeholder.new_password')"
                    :class="{
                      'is-invalid': v$.newPassword.$errors.length
                    }"
                    v-model="state.newPassword"
                    @blur="v$.newPassword.$touch"
                  />
                  <div v-if="v$.newPassword.$errors.length" class="invalid-feedback animated fadeIn">
                    <span v-if="v$.newPassword.$errors[0].$params.type === 'required'">Please provide a new password</span>
                    <span v-else>New password min length 6</span>
                  </div>
                </div>
                <div class="mb-4">
                  <label class="form-label" for="form-id"
                    >{{ t('pages.user.titles.confirm_new_password') }}<span class="text-danger">*</span></label
                  >
                  <input
                    type="password"
                    class="form-control"
                    id="form-password"
                    name="form-password"
                    :placeholder="t('pages.user.placeholder.confirm_password')"
                    :class="{
                      'is-invalid': v$.confirmPassword.$errors.length
                    }"
                    v-model="state.confirmPassword"
                    @blur="v$.confirmPassword.$touch"
                  />
                  <div v-if="v$.confirmPassword.$errors.length" class="invalid-feedback animated fadeIn">{{ t('pages.user.validate.require_confirm_password') }}</div>

                </div>
              </div>
            </div>
      </div>
    </div>
</template>

<style lang="scss">
</style>
