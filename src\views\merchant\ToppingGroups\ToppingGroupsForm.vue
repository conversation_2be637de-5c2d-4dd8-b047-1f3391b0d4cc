<script setup>
import EIcon from "@/components/Elements/EIcon.vue";
import useAppRouter from "@/composables/useRouter";
import { toppingGroupService } from "@/services/toppingGroup.service";
import { useTemplateStore } from "@/stores/template";
import useVuelidate from "@vuelidate/core";
import { minLength, required } from "@vuelidate/validators";
import { computed, onMounted, reactive, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import useNotify from "@/composables/useNotify";
import { storeData } from "@/stores/storeData";
import { scrollTo } from "@/stores/scollItemInlist";

const store = useTemplateStore();
const scrollStore = scrollTo();
const dataFetch = storeData();
const route = useRoute();
const router = useAppRouter();
const { setNotify } = useNotify();
const { t } = useI18n();
const typeSubmit = ref();
const props = route.params;
const id = props?.id;

const toppingGroup = ref();

let state = reactive({
  name: null,
});

const rules = computed(() => {
  return {
    name: {
      required,
      minLength: minLength(3),
    },
  };
});

let v$ = useVuelidate(rules, state);

async function onSubmit() {
  try {
    const result = await v$.value.$validate();

    if (!result) return;
    store.pageLoader({ mode: "on" });

    if (id) {
      await toppingGroupService.update(id, state);
    } else {
      await toppingGroupService.create(state);
    }
    if (typeSubmit.value === "confirm") {
      scrollStore.getElement(id);
      dataFetch.setData([], "merchant-topping-groups-list");
      dataFetch.setTotal(0, "merchant-topping-groups-list");
      router.pushByName({ name: "merchant-topping-groups-list" });
    } else {
      state.name = null;
      v$.value.$reset();
    }
    store.pageLoader({ mode: "off" });
    setNotify({
      title: "Success",
      message: id
        ? "Topping Group update success"
        : "Topping Group create success",
      type: "success",
    });
  } catch (e) {
    console.log(e);
    store.pageLoader({ mode: "off" });
    setNotify({
      title: "Error",
      message: e?.message,
    });
  }
}

const apiGetItem = async () => {
  try {
    store.pageLoader({ mode: "on" });
    const response = await toppingGroupService.get(id);
    toppingGroup.value = response.data;

    state = reactive({
      name: response.data.name,
    });

    v$ = useVuelidate(rules, state);
    store.pageLoader({ mode: "off" });
  } catch (error) {
    console.log(error);
    store.pageLoader({ mode: "off" });
  }
};

onMounted(async () => {
  try {
    if (id) apiGetItem();
  } catch (error) {
    console.error("Error fetching data:", error);
  }
});

const handleSubmit = (type) => {
  typeSubmit.value = type;
};
</script>

<template>
  <BasePageHeading
    :title="
      id
        ? t('pages.topping_group.titles.update')
        : t('pages.topping_group.titles.create')
    "
    :go-back="true"
    :subtitle="t('pages.topping_group.labels.label_head_list')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.topping_group.labels.manages")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <router-link to="/merchant/topping-groups">{{
              t("pages.topping_group.titles.list")
            }}</router-link>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <span>{{
              id
                ? t("pages.topping_group.titles.update")
                : t("pages.topping_group.titles.create")
            }}</span>
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <div class="row justify-content-center">
      <div class="col-sm-12 col-md-8">
        <form @submit.prevent="onSubmit">
          <BaseBlock
            :title="
              id
                ? t('pages.topping_group.titles.detail', {
                    item: toppingGroup?.id,
                  })
                : t('pages.topping_group.titles.add')
            "
          >
            <template #options>
              <e-icon
                @click="
                  () => {
                    router.back();
                    dataFetch.setData([], 'merchant-topping-groups-list');
                    dataFetch.setTotal(0, 'merchant-topping-groups-list');
                  }
                "
                name="arrow-left"
                role="button"
                class="icon_arrow_left"
              />
            </template>

            <div class="row justify-content-center py-sm-3 py-md-5">
              <div class="col-sm-10 col-md-8">
                <div class="mb-4">
                  <label class="form-label" for="block-form-name"
                    >{{ t("pages.topping_group.fields.name_field")
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    type="text"
                    class="form-control"
                    id="block-form-name"
                    name="block-form-name"
                    :placeholder="
                      t('pages.topping_group.placeholder.enter_name')
                    "
                    :class="{
                      'is-invalid': v$.name.$errors.length,
                    }"
                    v-model="state.name"
                    @blur="v$.name.$touch"
                  />
                  <div
                    v-if="v$.name.$errors.length"
                    class="invalid-feedback animated fadeIn"
                  >
                    Please enter name
                  </div>
                </div>

                <div
                  class="my-4"
                  :style="{
                    textAlign: 'end',
                    display: 'flex',
                    gap: '5px',
                    justifyContent: 'end',
                  }"
                >
                  <button
                    type="submit"
                    @click="handleSubmit('confirm')"
                    class="btn btn-sm btn-primary"
                    :style="{ color: '#fff' }"
                  >
                    {{ t("buttons.confirm") }}
                  </button>
                  <button
                    v-if="!id"
                    type="submit"
                    class="btn btn-sm btn-primary"
                    @click="handleSubmit('confirm_add')"
                    :style="{ color: '#fff' }"
                  >
                    {{ t("pages.products.buttons.confirm_add") }}
                  </button>
                </div>
              </div>
            </div>
          </BaseBlock>
        </form>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.icon_arrow_left {
  background-color: rgb(243, 235, 235);
  border-radius: 100%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
