import axios from "axios";
import { useCookies } from "vue3-cookies";
import router from "@/router/starter";
import Tr from "@/i18n/i18nUtils";

const { cookies } = useCookies();

const http = axios.create({
  baseURL: import.meta.env.VITE_API_MERCHANT,
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: false,
});

http.interceptors.request.use((config) => {
  const token = cookies.get("token_multi_tenant");
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

http.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    let response = error.response?.data;

    if (error.response?.status === 401) {
      response.code = error.response?.status;
      cookies.remove("token_merchant");
      cookies.remove("isMultiTenant");
      cookies.remove("current_store");
      cookies.remove("token_multi_tenant");
      localStorage.removeItem("arrayStores");
      router.push(Tr.i18nRoute({ name: "merchant-auth-signin" }));
    }
    if (error.response?.status === 403) {
      response.code = error.response?.status;
      router.push(Tr.i18nRoute({ name: "merchant-auth-signin" }));
    }
    if (!error?.response || error?.response?.status === 500) {
      response = { ...response, code: 500 };
    }
    return Promise.reject(response);
  }
);

export { http };
