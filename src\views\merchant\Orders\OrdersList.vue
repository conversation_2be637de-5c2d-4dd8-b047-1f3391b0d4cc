<script setup>
import { computed, onMounted, reactive, ref, watch } from "vue";
import moment from "moment";
import FlatPickr from "vue-flatpickr-component";
import { Dataset, DatasetItem, DatasetInfo, DatasetShow } from "vue-dataset";
import EModal from "@/components/Elements/EModal.vue";
import EListEmpty from "@/components/Elements/EListEmpty.vue";
import { orderService } from "@/services/order.service";
import ModalDetailCustomer from "../Customers/ModalDetailCustomer.vue";
import { useI18n } from "vue-i18n";
import Trans from "@/i18n/i18nUtils";
import { useDebounceFn } from "@vueuse/core";
import { useTemplateStore } from "@/stores/template";
import { storeData } from "@/stores/storeData";
import { scrollTo } from "@/stores/scollItemInlist";
import { searchValue } from "@/stores/searchData";
import useAppRouter from "@/composables/useRouter";

const store = useTemplateStore();
const scrollStore = scrollTo();
const searchStore = searchValue();
const dataFetch = storeData();
const router = useAppRouter();
const { t, locale } = useI18n();
const cols = reactive([
  {
    name: t("pages.order.fields.id"),
    field: "id",
    sort: "",
  },
  {
    name: t("pages.order.fields.order"),
    field: "Order",
    sort: "",
  },
  {
    name: t("pages.order.fields.customer"),
    field: "customer",
    sort: "",
  },
  {
    name: t("pages.order.fields.order_nr"),
    field: "order_nr",
    sort: "",
  },
  {
    name: t("pages.order.fields.queue_nr"),
    field: "queue_nr",
    sort: "",
  },
  {
    name: t("pages.order.fields.status"),
    field: "status",
    sort: "",
  },
  {
    name: t("pages.order.fields.created_at"),
    field: "created_at",
    sort: "",
  },
  {
    name: t("pages.order.fields.table_no"),
    field: "table_no",
    sort: "",
  },
  {
    name: t("pages.order.fields.items"),
    field: "items",
    sort: "",
  },
  {
    name: t("pages.order.fields.total"),
    field: "totals",
    sort: "",
  },
]);
const updateCols = () => {
  cols[0].name = t("pages.order.fields.id");
  cols[1].name = t("pages.order.fields.order");
  cols[2].name = t("pages.order.fields.customer");
  cols[3].name = t("pages.order.fields.order_nr");
  cols[4].name = t("pages.order.fields.queue_nr");
  cols[5].name = t("pages.order.fields.status");
  cols[6].name = t("pages.order.fields.created_at");
  cols[7].name = t("pages.order.fields.table_no");
  cols[8].name = t("pages.order.fields.items");
  cols[9].name = t("pages.order.fields.total");
};

// Watch for language changes
watch(locale, () => {
  updateCols();
});
const listOrderCustomer = ref();

const sortBy = computed(() => {
  return cols.reduce((acc, o) => {
    if (o.sort) {
      o.sort === "asc" ? acc.push(o.field) : acc.push("-" + o.field);
    }
    return acc;
  }, []);
});

const listOrders = ref();
const listUser = ref();
const search = ref();
const dateRange = ref(defaultDateRange());
const handleChange = async (val) => {
  if (val?.length === 2) {
    currentPage.value = 1;
    await onFetchList();
  }
};
const configRange = ref({
  mode: "range",
  dateFormat: "d-m-Y",
  locale: { firstDayOfWeek: 1 },
  onChange: handleChange,
});
const limit = ref(10);
const currentPage = ref(1);
const totalPage = ref(1);
const total = ref();

// On sort th click
function onSort(event, i) {
  let toset;
  const sortEl = cols[i];

  if (!event.shiftKey) {
    cols.forEach((o) => {
      if (o.field !== sortEl.field) {
        o.sort = "";
      }
    });
  }

  if (!sortEl.sort) {
    toset = "asc";
  }

  if (sortEl.sort === "desc") {
    toset = event.shiftKey ? "" : "asc";
  }

  if (sortEl.sort === "asc") {
    toset = "desc";
  }

  sortEl.sort = toset;
}
const onFetchList = async () => {
  store.pageLoader({ mode: "on" });
  scrollStore.setPage(currentPage.value);
  searchStore.setValueSearch(search?.value);
  let start_date;
  let end_date;
  const dates = [];
  const [startDateString, endDateString] = dateRange.value.split(" to ");
  start_date = moment(startDateString, "DD-MM-YYYY").toISOString();
  end_date = endDateString
    ? moment(endDateString, "DD-MM-YYYY").endOf("day").toISOString()
    : moment(startDateString, "DD-MM-YYYY").endOf("day").toISOString();
  let currentDate = moment(startDateString, "DD-MM-YYYY").clone();
  while (
    currentDate.isSameOrBefore(
      moment(endDateString || startDateString, "DD-MM-YYYY"),
      "day"
    )
  ) {
    dates.push(currentDate.format("DD-MM-YYYY"));
    currentDate.add(1, "day");
  }
  const response = await orderService.getList({
    start_date: start_date,
    end_date: end_date,
    search: search?.value,
    limit: limit.value,
    page: currentPage.value,
  });
  if (!response?.error) {
    listOrders.value = response?.data?.data || [];
    totalPage.value = response.data.last_page;
    total.value = response.data.total;
  }
  store.pageLoader({ mode: "off" });
};

const goToPage = async (target) => {
  currentPage.value = target;
  await onFetchList();
};

const handleOpenModal = async (id) => {
  store.pageLoader({ mode: "on" });
  const response = await orderService.getList({
    customer_id: id,
    limit: -1,
  });
  store.pageLoader({ mode: "off" });
  if (!response?.error) {
    listOrderCustomer.value = response?.data?.data || [];
  }
};
const ModalOrdersRef = ref();
const onCloseModalOrder = () => {
  ModalOrdersRef.value.closeModal();
};

watch(limit, async () => {
  currentPage.value = 1;
  await onFetchList();
});

onMounted(async () => {
  currentPage.value = scrollStore.page;
  search.value = searchStore.search;
  document.querySelectorAll("#datasetLength label").forEach((el) => {
    el.remove();
  });
  let selectLength = document.querySelector("#datasetLength select");
  selectLength.classList = "";
  selectLength.classList.add("form-select");
  selectLength.style.width = "80px";
  if (dataFetch.storeData?.["merchant-orders-list"]?.length > 0) {
    await (listOrders.value = dataFetch.storeData?.["merchant-orders-list"]);
    total.value = dataFetch.total?.["merchant-orders-list"];
    store.pageLoader({ mode: "off" });
  } else {
    await onFetchList();
  }
  if (scrollStore.formUpdateSuccess) {
    const item = document.querySelector(`.orderItem-${scrollStore.idItem}`);
    item.scrollIntoView({
      behavior: "smooth",
      block: "start",
    });
    scrollStore.setUpdateSuccess();
  }
});

const visible = ref(true);
function defaultDateRange() {
  const startDate = moment().startOf("isoWeek").format("DD-MM-YYYY");
  const endDate = moment().endOf("isoWeek").format("DD-MM-YYYY");
  return `${startDate} to ${endDate}`;
}

const renderCustomerName = (name, id) => {
  const customerName =
    name || listUser.value?.find((itm) => itm?.id === id)?.name;
  if (customerName === "undefined") return "Unavailable";
  return customerName;
};

const handleChangeSearch = useDebounceFn((e) => {
  search.value = e?.target?.value;
  onFetchList();
}, 500);
</script>

<template>
  <BasePageHeading
    :title="t('pages.order.name')"
    :subtitle="t('pages.order.labels.label_head_list')"
  >
  </BasePageHeading>

  <div class="content">
    <BaseBlock :title="t('pages.order.titles.list')">
      <Dataset
        v-slot="{ ds }"
        :ds-data="listOrders"
        :ds-sortby="sortBy"
        :ds-search-in="['code', 'customer.name']"
      >
        <div class="row" :data-page-count="ds.dsPagecount">
          <div id="datasetLength" class="col-md-3 py-2" style="flex: 1">
            <DatasetShow v-show="false" :dsShowEntries="100" />
            <div class="form-inline">
              <select class="form-select" style="width: 80px" v-model="limit">
                <option :value="5">5</option>
                <option :value="10">10</option>
                <option :value="25">25</option>
                <option :value="50">50</option>
                <option :value="100">100</option>
              </select>
            </div>
          </div>
          <div class="col-md-3 py-2">
            <input
              v-model="search"
              type="text"
              class="form-control"
              id="form-name"
              :placeholder="t('pages.placeholder.enter_order')"
              @input="handleChangeSearch"
            />
          </div>
          <div class="col-md-3 py-2">
            <FlatPickr
              class="form-control"
              id="example-flatpickr-range"
              placeholder="Select Date Range"
              v-model="dateRange"
              :config="configRange"
            />
          </div>
        </div>
        <hr />
        <div class="row" v-if="listOrders?.length">
          <div class="col-md-12">
            <div class="table-responsive">
              <table class="table table-striped mb-0">
                <thead>
                  <tr>
                    <th
                      v-for="(th, index) in cols"
                      :key="th.field"
                      :class="['sort', th.sort]"
                      @click="onSort($event, index)"
                    >
                      {{ th.name }} <i class="gg-select float-end"></i>
                    </th>
                  </tr>
                </thead>
                <DatasetItem tag="tbody" class="fs-sm">
                  <template #default="{ row }">
                    <tr :class="`orderItem-${row?.id}`">
                      <td style="width: 80px">{{ row.id }}</td>
                      <td style="color: #f59e0b">
                        <router-link
                          :to="{
                            path: Trans.i18nRouteByPath('/orders/' + row.id),
                            query: { transaction_id: row?.transaction_id },
                          }"
                          >{{ row.code_show }}</router-link
                        >
                      </td>
                      <td style="color: #f59e0b">
                        <a
                          href="javascript:void(0)"
                          @click="handleOpenModal(row?.customer_id)"
                          data-bs-toggle="modal"
                          data-bs-target="#modal-orders"
                          data-target=".bd-example-modal-lg"
                          type="button"
                          >{{
                            renderCustomerName(
                              row?.customer?.name,
                              row?.customer_id
                            )
                          }}</a
                        >
                      </td>
                      <td>{{ row.queue_nr }}</td>
                      <td>{{ row.queue_nr }}</td>
                      <td style="text-align: center">
                        <span v-if="row?.transaction_id">
                          <i class="fa fa-dollar refund-icon"></i>
                        </span>
                        <span v-else-if="row.status === 'completed'">
                          <i class="fa-regular fa-check-circle done-icon"></i>
                        </span>
                        <span v-else-if="row.status === 'uncompleted'">
                          <i class="fa fa-times-circle error-icon"></i>
                        </span>
                        <span v-else>
                          <i class="fa fa-dollar refund-icon"></i>
                        </span>
                      </td>
                      <td>
                        {{
                          moment(row.created_at).format("DD-MM-YYYY HH:mm:ss")
                        }}
                      </td>
                      <td>{{ row.table?.table_id }}</td>
                      <td>{{ row.items }}</td>
                      <td>
                        <span v-if="row?.transaction_id">-</span
                        >{{ row.amount_total + ",-kr" }}
                      </td>
                    </tr>
                  </template>
                </DatasetItem>
              </table>
            </div>
          </div>
        </div>
        <EListEmpty v-else />
        <!-- <div class="d-flex justify-content-end">
          <ul class="list-group border-none">
            <li class="list-group-item border-0 fw-bold">Subtotal</li>
            <li class="list-group-item border-0 fw-bold">Mva 25%</li>
            <li class="list-group-item border-0 fw-bold">Total</li>
          </ul>
        </div> -->
        <div
          class="d-flex flex-md-row flex-column justify-content-between align-items-center"
        >
          <DatasetInfo class="py-3 fs-sm" />
          <el-pagination
            v-if="visible"
            v-model:current-page="currentPage"
            @current-change="onFetchList"
            background
            v-model:page-size="limit"
            layout="prev, pager, next"
            :prev-text="t('pages.footer.previous')"
            :next-text="t('pages.footer.next')"
            :total="total"
          />
        </div>
      </Dataset>
    </BaseBlock>
    <EModal
      id="modal-orders"
      :title="t('pages.customer.titles.detail')"
      size="modal-xl"
      :hidden-button-ok="true"
      ref="ModalOrdersRef"
    >
      <template v-slot:childrenComponent>
        <ModalDetailCustomer
          :list-order="listOrderCustomer"
          @close="onCloseModalOrder"
        />
      </template>
    </EModal>
  </div>
</template>
<style lang="scss">
@import "flatpickr/dist/flatpickr.css";
@import "@/assets/scss/vendor/flatpickr";
.done-icon {
  color: #019521;
  font-size: 26px;
}
.error-icon {
  color: #cd0808;
  font-size: 26px;
}
.refund-icon {
  color: white;
  background-color: gray;
  border: 1px solid;
  padding: 6px 9px;
  border-radius: 50px;
}
</style>
