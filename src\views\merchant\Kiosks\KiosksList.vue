<script setup>
import EButton from '@/components/Elements/EButton.vue'
import EListEmpty from '@/components/Elements/EListEmpty.vue'
import { kioskService } from '@/services/kiosk.service'
import { useTemplateStore } from '@/stores/template'
import { computed, onMounted, reactive, ref } from 'vue'
import {
Dataset,
DatasetInfo,
DatasetItem,
DatasetPager,
DatasetSearch,
DatasetShow
} from 'vue-dataset'
// import EModal from '@/components/Elements/EModal.vue'
import useAppRouter from '@/composables/useRouter'
import Swal from "sweetalert2"
import { useI18n } from 'vue-i18n'
let toast = Swal.mixin({
  buttonsStyling: false,
  target: "#page-container",
  customClass: {
    confirmButton: "btn btn-success m-1",
    cancelButton: "btn btn-danger m-1",
    input: "form-control",
  },
});
const store = useTemplateStore()
const { t } = useI18n();
const router = useAppRouter()

// Helper variables
const cols = reactive([
  {
    name: t('pages.kiosk.fields.id'),
    field: 'id',
    sort: ''
  },
  {
    name: t('pages.kiosk.fields.kiosk_id'),
    field: 'Kiosk ID',
    sort: ''
  },
  {
    name: t('pages.kiosk.fields.name'),
    field: 'name',
    sort: ''
  },
  {
    name: t('pages.kiosk.fields.reception_printer'),
    field: 'printer_id',
    sort: ''
  }
])

// Sort by functionality
const sortBy = computed(() => {
  return cols.reduce((acc, o) => {
    if (o.sort) {
      o.sort === 'asc' ? acc.push(o.field) : acc.push('-' + o.field)
    }
    return acc
  }, [])
})

// On sort th click
// function onSort(event, i) {
//   let toset
//   const sortEl = cols[i]

//   if (!event.shiftKey) {
//     cols.forEach((o) => {
//       if (o.field !== sortEl.field) {
//         o.sort = ''
//       }
//     })
//   }

//   if (!sortEl.sort) {
//     toset = 'asc'
//   }

//   if (sortEl.sort === 'desc') {
//     toset = event.shiftKey ? '' : 'asc'
//   }

//   if (sortEl.sort === 'asc') {
//     toset = 'desc'
//   }

//   sortEl.sort = toset
// }

// const idKioskDelete = ref()
const listKiosks = ref([])

const onFetchList = async () => {
  try {
    store.pageLoader({ mode: 'on' })
    const response = await kioskService.getList()
    if (!response?.error) {
      listKiosks.value = response.data?.data || []
    }
    store.pageLoader({ mode: 'off' })
  } catch (error) {
    console.log(error)
    store.pageLoader({ mode: 'off' })
  }
}

// const apiDelete = async () => {
//   try {
//     store.pageLoader({ mode: 'on' })
//     await kioskService.delete(idKioskDelete.value)
//     listKiosks.value = listKiosks.value.filter((item) => item.id !== idKioskDelete.value)
//     idKioskDelete.value = null
//     store.pageLoader({ mode: 'off' })
//   } catch (error) {
//     console.log(error)
//     store.pageLoader({ mode: 'off' })
//   }
// }

const handleNavigate = (slug, id) => {
  window.open(`https://kiosk.nordpay.dev/${slug}/${id}`, '_blank');
}


onMounted(async () => {
  await onFetchList()

  // Remove labels from
  document.querySelectorAll('#datasetLength label').forEach((el) => {
    el.remove()
  })

  // Replace select classes
  let selectLength = document.querySelector('#datasetLength select')

  if (selectLength) {
    selectLength.classList = ''
    selectLength.classList.add('form-select')
    selectLength.style.width = '80px'
  }
})

const onOpenDeleteConfirm = (id) => {
  toast
      .fire({
        title: "Are you sure?",
        text: "You will not be able to recover this kiosk!",
        icon: "warning",
        showCancelButton: true,
        customClass: {
          confirmButton: "btn btn-danger m-1",
          cancelButton: "btn btn-info m-1",
        },
        confirmButtonText: "Yes, delete!",
        html: false,
        preConfirm: () => {
          return kioskService.delete(id)
        },
      })
      .then((result) => {
        if (result.value && !result.value?.error) {
          toast.fire(
              "Deleted!",
              "Kiosk has been deleted.",
              "success"
          );
          onFetchList()
        } else if (result.dismiss === "cancel") {
          toast.fire("Cancelled", "Kiosk is safe", "error");
        }
      });
}
</script>

<template>
  <BasePageHeading :title="t('pages.kiosk.name')" :subtitle="t('pages.kiosk.labels.label_head_list')">
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{ t('pages.kiosk.labels.manages') }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">{{ t('pages.kiosk.titles.list') }}</li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <BaseBlock title="Kiosks">
      <template #options>
        <EButton
          type="info"
          size="sm"
          @click="() => router.pushByName({ name: 'merchant-kiosks-create' })"
          ><i class="fa fa-plus opacity-50 me-1"></i> {{  }}</EButton
        >
      </template>
      <Dataset
        v-slot="{ ds }"
        :ds-data="listKiosks"
        :ds-sortby="sortBy"
        :ds-search-in="['id', 'name']"
      >
        <div class="row" :data-page-count="ds.dsPagecount">
          <div id="datasetLength" class="col-md-8 py-2">
            <DatasetShow />
          </div>
          <div class="col-md-4 py-2">
            <DatasetSearch :ds-search-placeholder="t('pages.placeholder.search')" />
          </div>
        </div>
        <hr />
        <div class="row" v-if="listKiosks?.length">
          <div class="col-md-12">
            <div class="table-responsive">
              <table class="table mb-0">
                <thead>
                  <tr>
                    <th
                      v-for="(th) in cols"
                      :key="th.field"
                    >
                      {{ th.name }}
                    </th>
                    <th class="text-end" scope="col">{{ t('pages.kiosk.fields.action') }}</th>
                  </tr>
                </thead>
                <DatasetItem tag="tbody" class="fs-sm">
                  <template #default="{ row }">
                    <tr>
                      <td style="min-width: 50px">
                        <RouterLink :to="`kiosks/${row.id}`">
                          {{ row.id }}
                        </RouterLink>
                      </td>
                      <td style="min-width: 150px">{{ row.kiosk_id }}</td>
                      <td style="min-width: 150px">{{ row.name }}</td>
                      <td style="min-width: 150px">{{ row.printer?.name }}</td>
                      <td class="text-end">
                        <div class="btn-group">
                          <button  @click="() => handleNavigate(row?.store?.slug, row?.store?.id)" type="button" class="btn btn-sm btn-alt-secondary">
                            <i
                              class="fa fa-fw fa-link"
                            ></i>
                          </button>
                          <button
                            type="button"
                            class="btn btn-sm btn-alt-secondary"
                            @click="() => router.pushByPath(`/kiosks/${row.id}/update`)"
                          >
                            <i class="fa fa-fw fa-pencil-alt"></i>
                          </button>
                          <button
                            type="button"
                            class="btn btn-sm btn-alt-secondary"
                            @click="onOpenDeleteConfirm(row.id)"
                          >
                            <i class="fa fa-fw fa-times"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </template>
                </DatasetItem>
              </table>
            </div>
          </div>
        </div>
        <EListEmpty v-else />
        <div class="d-flex flex-md-row flex-column justify-content-between align-items-center">
          <DatasetInfo class="py-3 fs-sm" />
          <DatasetPager class="flex-wrap py-3 fs-sm" />
        </div>
      </Dataset>
    </BaseBlock>
  </div>
</template>

<style lang="scss" scoped>
.gg-select {
  box-sizing: border-box;
  position: relative;
  display: block;
  transform: scale(1);
  width: 22px;
  height: 22px;
}
.gg-select::after,
.gg-select::before {
  content: '';
  display: block;
  box-sizing: border-box;
  position: absolute;
  width: 8px;
  height: 8px;
  left: 7px;
  transform: rotate(-45deg);
}
.gg-select::before {
  border-left: 2px solid;
  border-bottom: 2px solid;
  bottom: 4px;
  opacity: 0.3;
}
.gg-select::after {
  border-right: 2px solid;
  border-top: 2px solid;
  top: 4px;
  opacity: 0.3;
}
th.sort {
  cursor: pointer;
  user-select: none;
  &.asc {
    .gg-select::after {
      opacity: 1;
    }
  }
  &.desc {
    .gg-select::before {
      opacity: 1;
    }
  }
}
</style>
