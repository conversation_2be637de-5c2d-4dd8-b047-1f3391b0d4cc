import { defineStore } from "pinia";
import { ref } from "vue";

export const storeData = defineStore("storeData", () => {
  const storeData = ref({});
  const total = ref({});
  const sumDailySumary = ref({});
  const setData = (data, note) => {
    if (!storeData.value) {
      storeData.value = {}; // Khởi tạo storeData.value nếu chưa có
    }
    storeData.value[note] = data;
  };
  const setTotal = (totalData, note) => {
    if (!total.value) {
      total.value = {}; // Khởi tạo total.value nếu chưa có
    }
    total.value[note] = totalData;
  };
  const setSumDailySummary = (totalData, note) => {
    if (!sumDailySumary.value) {
      sumDailySumary.value = {}; // Khởi tạo total.value nếu chưa có
    }
    sumDailySumary.value[note] = totalData;
  };
  const resetStore = () => {
    storeData.value = {};
    total.value = {};
    sumDailySumary.value = {};
  };
  return {
    storeData,
    setData,
    total,
    sumDailySumary,
    setTotal,
    setSumDailySummary,
    resetStore,
  };
});
