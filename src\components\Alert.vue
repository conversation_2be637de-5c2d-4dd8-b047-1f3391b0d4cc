<!-- eslint-disable vue/multi-word-component-names -->
<script setup>
import { alertService } from '../services/alert.service'
import { onMounted, ref } from "vue";

const alerts = ref([])

onMounted(async () => {
    const res = await alertService.get();
    alerts.value = res.data;
})
  
</script>
<template>
    <div v-for="(item, index) in alerts" :key="index" class="alert alert-warning new p-4 mb-2 py-2" :class="{'mt-2' : index === 0}">
        <p class="mb-0 _text-dark" >{{ item }}</p>
    </div>
</template>

<style scoped>
.alert-warning.new {
  background: none;
  border: none;
  border-left: 4px solid #cd0808;
  border-radius: 0;
  background: #fff;
  box-shadow: 1px 1px 4px rgba(0, 0, 0, .2);
}
._text-dark {
  color: #000;
}
</style>
