import { http } from "./Base/base.service";

export const reportSaleService = {
  async getList(query) {
    return await http.get("/report/sale", {
      params: query,
    });
  },
  
  async export(query) {
    return await http.get("/report/export-csv-sale", {
      params: query,
    });
  },

  async exportPDF(query) {
    return await http.get("/report/export-pdf-sale", {
      params: query,
      responseType: "blob"
    });
  }
};
