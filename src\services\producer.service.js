import { http } from './Base/base.service'

export const producerService = {
  async getList(query) {
    return await http.get('/producers', {
      params: query
    })
  },

  async get(id) {
    return await http.get('/producers/' + id)
  },

  async create(data) {
    return await http.post('/producers', data)
  },

  async update(id, data) {
    return await http.patch('/producers/' + id, data)
  },

  async delete(id) {
    return await http.delete('/producers/' + id)
  }
}
