//
// Ribbon
// --------------------------------------------------

.ribbon {
  position: relative;
  min-height: 3.25rem;
}

.ribbon-box {
  position: absolute;
  top: 0.75rem;
  right: 0;
  padding: 0 0.75rem;
  height: 2.25rem;
  line-height: 2.25rem;
  z-index: 5;
  font-weight: $font-weight-bold;
  border-top-left-radius: $border-radius;
  border-bottom-left-radius: $border-radius;

  &::before {
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    content: "";
  }
}

// Bookmark variation
.ribbon-bookmark {
  .ribbon-box {
    padding-left: 0.625rem;
    border-radius: 0 !important;
  }

  .ribbon-box::before {
    top: 0;
    right: 100%;
    height: 2.25rem;
    border: 1rem solid;
    border-left-width: 0.625rem;
    border-right-width: 0;
  }
}

// Modern variation
.ribbon-modern {
  .ribbon-box {
    top: 0;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    border-radius: 0 !important;
  }

  .ribbon-box::before {
    right: 100%;
    border: 1.125rem solid;
  }
}

// Position variations
.ribbon-left {
  .ribbon-box {
    right: auto;
    left: 0;
    border-radius: 0;
    border-top-right-radius: $border-radius;
    border-bottom-right-radius: $border-radius;
  }

  &.ribbon-bookmark {
    .ribbon-box {
      padding-left: 0.75rem;
      padding-right: 0.625rem;
    }

    .ribbon-box::before {
      right: auto;
      left: 100%;
      border-left-width: 0;
      border-right-width: 0.625rem;
    }
  }

  &.ribbon-modern {
    .ribbon-box::before {
      left: 100%;
      right: auto;
    }
  }
}

.ribbon-bottom {
  .ribbon-box {
    top: auto;
    bottom: 0.75rem;
  }

  &.ribbon-modern {
    .ribbon-box {
      bottom: 0;
    }
  }
}

// Color variation
.ribbon-light {
  @include ribbon-variation($body-bg-dark, $body-color);
}

.ribbon-dark {
  @include ribbon-variation($dark, $white);
}

.ribbon-primary {
  @include ribbon-variation($primary, $white);
}

.ribbon-success {
  @include ribbon-variation($success, $white);
}

.ribbon-info {
  @include ribbon-variation($info, $white);
}

.ribbon-warning {
  @include ribbon-variation($warning, $white);
}

.ribbon-danger {
  @include ribbon-variation($danger, $white);
}

.ribbon-glass {
  .ribbon-box {
    color: $white;
    background-color: rgba(255, 255, 255, 0.4);
  }

  &.ribbon-bookmark {
    .ribbon-box::before {
      border-color: rgba(255, 255, 255, 0.4);
      border-left-color: transparent;
    }

    &.ribbon-left .ribbon-box::before {
      border-color: rgba(255, 255, 255, 0.4);
      border-right-color: transparent;
    }
  }

  &.ribbon-modern {
    .ribbon-box::before {
      border-color: rgba(255, 255, 255, 0.4);
      border-left-color: transparent;
      border-bottom-color: transparent;
    }

    &.ribbon-bottom .ribbon-box::before {
      border-color: rgba(255, 255, 255, 0.4);
      border-top-color: transparent;
      border-left-color: transparent;
    }

    &.ribbon-left .ribbon-box::before {
      border-color: rgba(255, 255, 255, 0.4);
      border-right-color: transparent;
      border-bottom-color: transparent;
    }

    &.ribbon-left.ribbon-bottom .ribbon-box::before {
      border-color: rgba(255, 255, 255, 0.4);
      border-top-color: transparent;
      border-right-color: transparent;
    }
  }
}
