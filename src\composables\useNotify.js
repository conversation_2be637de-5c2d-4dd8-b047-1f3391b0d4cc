import { ref } from 'vue'

const Title = ref(null)
const Message = ref(null)
const Type = ref('warning')
const NotifyElement = ref()
export default function useNotify() {
  const setNotifyElement = (ref) => {
    NotifyElement.value = ref
  }
  const setNotify = ({
      title, message, type = 'warning'
                     }) => {
    Title.value = title
    Message.value = message
    Type.value = type
    NotifyElement.value.show()
  }

  return {
    Title,
    Message,
    Type,
    setNotify,
    setNotifyElement
  }
}
