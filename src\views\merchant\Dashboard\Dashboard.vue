<script setup>
import EButton from "@/components/Elements/EButton.vue";
import EIcon from "@/components/Elements/EIcon.vue";
import { dashboardService } from "@/services/dashboard.service";
import { useTemplateStore } from "@/stores/template";
import { computed, onMounted, reactive, ref, watch } from "vue";
import { Chart, registerables } from "chart.js";
import { useMultiTenant } from "@/stores/multiTenant";
import { Dataset, DatasetItem } from "vue-dataset";
import EListEmpty from "@/components/Elements/EListEmpty.vue";
import { Line } from "vue-chartjs";
import { useI18n } from "vue-i18n";
const multiTenant = useMultiTenant();

const { t, locale } = useI18n();
Chart.register(...registerables);
const colsTransactions = reactive([
  {
    name: t(`pages.dashboard.fields.id`),
    field: "id",
  },
  {
    name: t(`pages.dashboard.fields.amount`),
    field: "amount",
  },
  {
    name: t(`pages.dashboard.fields.order`),
    field: "order",
  },
  {
    name: t(`pages.dashboard.fields.status`),
    field: "status",
  },
]);
const store = useTemplateStore();
const colsOrder = reactive([
  {
    name: t(`pages.dashboard.fields.id`),
    field: "id",
  },
  {
    name: t(`pages.dashboard.fields.order`),
    field: "order",
  },
  {
    name: t(`pages.dashboard.fields.status`),
    field: "status",
  },
]);
const chartSales = ref({
  labels: ["MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"],
  datasets: [
    {
      label: "This Week",
      fill: true,
      backgroundColor: "#88c5e4",
      borderColor: "#0784c1",
      pointBackgroundColor: "#0C84C3",
      pointBorderColor: "#fff",
      pointHoverBackgroundColor: "#0C84C3",
      pointHoverBorderColor: "rgba(0, 0, 0, .3)",
      data: [30, 32, 40, 45, 43, 38, 55],
    },
  ],
});
const chartEarning = ref({
  labels: ["MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"],
  datasets: [
    {
      label: "This Week",
      fill: true,
      backgroundColor: "#b5d28f",
      borderColor: "#64a502",
      pointBackgroundColor: "#0C84C3",
      pointBorderColor: "#fff",
      pointHoverBackgroundColor: "#0C84C3",
      pointHoverBorderColor: "rgba(0, 0, 0, .3)",
      data: [30, 32, 40, 45, 43, 38, 55],
    },
  ],
});
const time = ref("weekly");
const updateCols = () => {
  colsTransactions[0].name = t("pages.dashboard.fields.id");
  colsTransactions[1].name = t("pages.dashboard.fields.amount");
  colsTransactions[2].name = t("pages.dashboard.fields.order");
  colsTransactions[3].name = t("pages.dashboard.fields.status");
  colsOrder[0].name = t("pages.dashboard.fields.id");
  colsOrder[1].name = t("pages.dashboard.fields.order");
  colsOrder[2].name = t("pages.dashboard.fields.status");
};

// Watch for language changes
watch(locale, () => {
  updateCols();
});
const listNoti = ref([]);

const data = ref();
const fetchList = async () => {
  try {
    store.pageLoader({ mode: "on" });
    const response = await dashboardService.getList();
    const response1 = await dashboardService.getNoti();
    store.pageLoader({ mode: "off" });
    listNoti.value = response1.data;
    data.value = response.data || [];
  } catch (error) {
    store.pageLoader({ mode: "off" });
  }
};

const orderData = computed(() => {
  return data?.value?.recent_orders;
});

const transactionData = computed(() => {
  const dataList = ref([]);
  dataList.value = data.value?.recent_transactions.filter(
    (item) => item.status === "completed"
  );
  const newList = ref();
  if (dataList.value?.length > 0) {
    newList.value = dataList.value.map((item) => ({
      ...item,
      bgColor: item?.order ? "#F6F7F9" : "#FFBDB8",
    }));
  } else {
    newList.value = [];
  }
  return newList.value;
});
const dataChart = ref();
const avgSale = ref(0);
const avgSaleGrowth = ref(0);
const avgEarn = ref(0);
const avgEarnGrowth = ref(0);

const onGetChart = async () => {
  try {
    store.pageLoader({ mode: "on" });
    const response = await dashboardService.getChart({
      time: time.value,
    });
    store.pageLoader({ mode: "off" });
    const labels = response.data.chart.map((item) => item.date);
    const dataSales = response.data.chart.map((item) => item.total_orders);
    const dataEarning = response.data.chart.map((item) => item.total_income);
    dataChart.value = response.data;
    avgSale.value =
      time.value === "weekly"
        ? dataChart.value.sale_avg_week
        : dataChart.value.sale_avg_month;
    avgSaleGrowth.value =
      time.value === "weekly"
        ? dataChart.value.sale_avg_week_growth
        : dataChart.value.sale_avg_month_growth;
    avgEarn.value =
      time.value === "weekly"
        ? dataChart.value.income_avg_week
        : dataChart.value.income_avg_month;
    avgEarnGrowth.value =
      time.value === "weekly"
        ? dataChart.value.income_avg_week_growth
        : dataChart.value.income_avg_month_growth;
    chartEarning.value = {
      labels: labels,
      datasets: [
        {
          label:
            time.value === "weekly"
              ? t("pages.dashboard.titles.this_week")
              : t("pages.dashboard.titles.this_month"),
          fill: true,
          backgroundColor: "#b5d28f",
          borderColor: "#64a502",
          pointBackgroundColor: "#64a502",
          pointBorderColor: "#fff",
          pointHoverBackgroundColor: "#0C84C3",
          pointHoverBorderColor: "rgba(0, 0, 0, .3)",
          data: dataEarning,
        },
      ],
    };
    chartSales.value = {
      labels: labels,
      datasets: [
        {
          label:
            time.value === "weekly"
              ? t("pages.dashboard.titles.this_week")
              : t("pages.dashboard.titles.this_month"),
          fill: true,
          backgroundColor: "#88c5e4",
          borderColor: "#0784c1",
          pointBackgroundColor: "#0C84C3",
          pointBorderColor: "#fff",
          pointHoverBackgroundColor: "#0C84C3",
          pointHoverBorderColor: "rgba(0, 0, 0, .3)",
          data: dataSales,
        },
      ],
    };
  } catch (error) {
    store.pageLoader({ mode: "off" });
  }
};
watch(time, async () => {
  await onGetChart();
});
onMounted(async () => {
  await fetchList();
  await onGetChart();
});
watch(
  () => multiTenant.watchChange,
  async () => {
    await fetchList();
    await onGetChart();
  }
);
</script>

<template>
  <BasePageHeading :title="t('pages.dashboard.name')"> </BasePageHeading>
  <div class="content">
    <div v-if="listNoti.length">
      <div
        v-for="(item, index) in listNoti"
        :key="index"
        class="col-12 noti-container"
      >
        <div class="a" v-html="item.content"></div>
      </div>
    </div>
    <div style="width: 180px; padding-bottom: 30px" class="form-inline">
      <select class="form-select" v-model="time">
        <option value="weekly">{{ t("pages.dashboard.titles.weekly") }}</option>
        <option value="monthly">
          {{ t("pages.dashboard.titles.monthly") }}
        </option>
      </select>
    </div>
    <div class="row">
      <div class="col-12 col-md-6">
        <BaseBlock :title="t('pages.dashboard.titles.sales')">
          <template #subtitle>
            <div>
              <small>{{
                time === "weekly"
                  ? t("pages.dashboard.titles.this_week")
                  : t("pages.dashboard.titles.this_month")
              }}</small>
            </div>
          </template>
          <div class="py-3">
            <Line
              :data="chartSales"
              :options="{
                tension: 0.4,
                maintainAspectRatio: false,
                scales: {
                  y: {
                    beginAtZero: true,
                    min: 0,
                    suggestedMax: 5,
                  },
                },
              }"
              style="height: 350px"
            />
          </div>
          <div
            style="
              display: flex;
              justify-content: space-evenly;
              padding-bottom: 25px;
            "
          >
            <div>
              <div class="text-1">
                {{ t("pages.dashboard.titles.this_month").toLocaleUpperCase() }}
              </div>
              <div class="text-2">{{ parseInt(dataChart?.sale_month) }}</div>
              <div
                :style="
                  parseInt(dataChart?.sale_month_growth) < 0
                    ? 'color: #c5474e;'
                    : 'color: #75992d;'
                "
                class="text-1"
              >
                <i
                  :class="
                    parseInt(dataChart?.sale_month_growth) < 0
                      ? 'fa-caret-down'
                      : 'fa-caret-up'
                  "
                  class="fa-solid"
                ></i>
                {{ parseInt(dataChart?.sale_month_growth) }}%
              </div>
            </div>
            <div>
              <div class="text-1">
                {{ t("pages.dashboard.titles.this_week").toLocaleUpperCase() }}
              </div>
              <div class="text-2">{{ parseInt(dataChart?.sale_week) }}</div>
              <div
                :style="
                  parseInt(dataChart?.sale_week_growth) < 0
                    ? 'color: #c5474e;'
                    : 'color: #75992d;'
                "
                class="text-1"
              >
                <i
                  :class="
                    parseInt(dataChart?.sale_week_growth) < 0
                      ? 'fa-caret-down'
                      : 'fa-caret-up'
                  "
                  class="fa-solid"
                ></i>
                {{ parseInt(dataChart?.sale_week_growth) }}%
              </div>
            </div>
            <div>
              <div class="text-1">
                {{ t("pages.dashboard.titles.average").toLocaleUpperCase() }}
              </div>
              <div class="text-2">{{ parseInt(avgSale) }}</div>
              <div
                :style="
                  parseInt(avgSaleGrowth) < 0
                    ? 'color: #c5474e;'
                    : 'color: #75992d;'
                "
                class="text-1"
              >
                <i
                  :class="
                    parseInt(avgSaleGrowth) < 0
                      ? 'fa-caret-down'
                      : 'fa-caret-up'
                  "
                  class="fa-solid"
                ></i>
                {{ parseInt(avgSaleGrowth) }}%
              </div>
            </div>
          </div>
        </BaseBlock>
      </div>
      <div class="col-12 col-md-6">
        <BaseBlock :title="t('pages.dashboard.titles.earnings')">
          <template #subtitle>
            <div>
              <small>{{
                time === "weekly"
                  ? t("pages.dashboard.titles.this_week")
                  : t("pages.dashboard.titles.this_month")
              }}</small>
            </div>
          </template>
          <div class="py-3">
            <Line
              :data="chartEarning"
              :options="{
                tension: 0.4,
                maintainAspectRatio: false,
                scales: {
                  y: {
                    beginAtZero: true,
                    min: 0,
                    suggestedMax: 5,
                  },
                },
              }"
              style="height: 350px"
            />
          </div>
          <div
            style="
              display: flex;
              justify-content: space-evenly;
              padding-bottom: 25px;
            "
          >
            <div>
              <div class="text-1">
                {{ t("pages.dashboard.titles.this_month").toLocaleUpperCase() }}
              </div>
              <div class="text-2">{{ parseInt(dataChart?.income_month) }}</div>
              <div
                :style="
                  parseInt(dataChart?.income_month_growth) < 0
                    ? 'color: #c5474e;'
                    : 'color: #75992d;'
                "
                class="text-1"
              >
                <i
                  :class="
                    parseInt(dataChart?.income_month_growth) < 0
                      ? 'fa-caret-down'
                      : 'fa-caret-up'
                  "
                  class="fa-solid"
                ></i>
                {{ parseInt(dataChart?.income_month_growth) }}%
              </div>
            </div>
            <div>
              <div class="text-1">
                {{ t("pages.dashboard.titles.this_week").toLocaleUpperCase() }}
              </div>
              <div class="text-2">{{ parseInt(dataChart?.income_week) }}</div>
              <div
                :style="
                  parseInt(dataChart?.income_week_growth) < 0
                    ? 'color: #c5474e;'
                    : 'color: #75992d;'
                "
                class="text-1"
              >
                <i
                  :class="
                    parseInt(dataChart?.income_week_growth) < 0
                      ? 'fa-caret-down'
                      : 'fa-caret-up'
                  "
                  class="fa-solid"
                ></i>
                {{ parseInt(dataChart?.income_week_growth) }}%
              </div>
            </div>
            <div>
              <div class="text-1">
                {{ t("pages.dashboard.titles.average").toLocaleUpperCase() }}
              </div>
              <div class="text-2">{{ parseInt(avgEarn) }}</div>
              <div
                :style="
                  parseInt(avgEarnGrowth) < 0
                    ? 'color: #c5474e;'
                    : 'color: #75992d;'
                "
                class="text-1"
              >
                <i
                  :class="
                    parseInt(avgEarnGrowth) < 0
                      ? 'fa-caret-down'
                      : 'fa-caret-up'
                  "
                  class="fa-solid"
                ></i>
                {{ parseInt(avgEarnGrowth) }}%
              </div>
            </div>
          </div>
        </BaseBlock>
      </div>
    </div>

    <div class="row">
      <div class="col-12 col-md-6">
        <BaseBlock :title="t('pages.dashboard.titles.dagens_salg')">
          <div
            class="d-flex align-items-start justify-content-start space-x-3 mb-3"
          >
            <e-button type="warning" size="lg" class="mt-1">
              <e-icon name="circle-dollar-to-slot" />
            </e-button>
            <div>
              <div class="fw-bold fs-3">{{ data?.total_income }},-kr</div>
              <div class="text-muted fs-sm">
                {{ data?.total_orders }}
                {{ t("pages.dashboard.labels.totale_bestillinger") }}
              </div>
            </div>
          </div>
        </BaseBlock>
      </div>
      <div class="col-12 col-md-6">
        <BaseBlock :title="t('pages.dashboard.titles.sms')">
          <div
            class="d-flex align-items-start justify-content-start space-x-3 mb-3"
          >
            <e-button type="warning" size="lg" class="mt-1">
              <e-icon name="comment-sms" />
            </e-button>
            <div>
              <div class="fw-bold fs-3">{{ data?.total_sms }}</div>
              <div class="text-muted fs-sm">
                {{ t("pages.dashboard.labels.sent") }}
              </div>
            </div>
          </div>
        </BaseBlock>
      </div>
      <div class="col-12 col-md-6">
        <BaseBlock :title="t('pages.dashboard.titles.recent_transactions')">
          <template #subtitle>
            <div>
              <small>{{ t("pages.dashboard.labels.update") }}</small>
            </div>
          </template>
          <Dataset :ds-data="transactionData">
            <div class="row" v-if="data && data?.recent_transactions?.length">
              <div class="col-md-12">
                <div class="table-responsive">
                  <table class="table mb-0">
                    <thead>
                      <tr>
                        <th
                          v-for="th in colsTransactions"
                          :key="th.field"
                          :class="['sort']"
                        >
                          {{ th.name }} <i class="gg-select float-end"></i>
                        </th>
                      </tr>
                    </thead>
                    <DatasetItem tag="tbody" class="fs-sm">
                      <template #default="{ row, rowIndex }">
                        <tr>
                          <th
                            :style="{
                              backgroundColor: row.bgColor,
                            }"
                            scope="row"
                          >
                            {{ rowIndex + 1 }}
                          </th>
                          <td
                            v-if="row?.refund"
                            style="min-width: 150px"
                            :style="{
                              backgroundColor: row?.bgColor,
                            }"
                          >
                            - {{ row?.refund?.amount_total }}
                          </td>
                          <td
                            v-else-if="row?.order"
                            style="min-width: 150px"
                            :style="{
                              backgroundColor: row.bgColor,
                            }"
                          >
                            {{ row?.order?.amount_total }}
                          </td>
                          <td
                            v-if="row?.refund"
                            style="min-width: 80px"
                            :style="{
                              backgroundColor: row.bgColor,
                            }"
                          >
                            {{ row?.refund?.code_show }}
                          </td>
                          <td
                            v-else-if="row?.order"
                            style="min-width: 80px"
                            :style="{
                              backgroundColor: row.bgColor,
                            }"
                          >
                            {{ row?.order?.code_show }}
                          </td>
                          <td
                            style="max-width: 80px"
                            :style="{
                              backgroundColor: row.bgColor,
                            }"
                          >
                            {{ row?.status }}
                          </td>
                        </tr>
                      </template>
                    </DatasetItem>
                  </table>
                </div>
              </div>
            </div>
            <EListEmpty
              v-else
              :message="t('pages.dashboard.labels.no_data_transaction')"
            />
          </Dataset>
        </BaseBlock>
      </div>
      <div class="col-12 col-md-6">
        <BaseBlock :title="t('pages.dashboard.titles.recent_orders')">
          <template #subtitle>
            <div>
              <small>
                {{
                  t("pages.dashboard.labels.show", {
                    limit:
                      Number(data?.total_orders) > 10 ? 10 : data?.total_orders,
                    order: data?.total_orders,
                  })
                }}
              </small>
            </div>
          </template>
          <Dataset :ds-data="orderData">
            <div class="row" v-if="data && data?.recent_orders?.length">
              <div class="col-md-12">
                <div class="table-responsive">
                  <table class="table table-striped mb-0">
                    <thead>
                      <tr>
                        <th
                          v-for="th in colsOrder"
                          :key="th.field"
                          :class="['sort']"
                        >
                          {{ th.name }}
                          <i class="gg-select float-end"></i>
                        </th>
                      </tr>
                    </thead>
                    <DatasetItem tag="tbody" class="fs-sm">
                      <template #default="{ row, rowIndex }">
                        <tr>
                          <th scope="row">{{ rowIndex + 1 }}</th>
                          <td style="min-width: 150px; color: #f59e0b">
                            {{ row?.code_show }}
                          </td>
                          <td style="max-width: 80px">{{ row.status }}</td>
                        </tr>
                      </template>
                    </DatasetItem>
                  </table>
                </div>
              </div>
            </div>
            <EListEmpty
              v-else
              :message="t('pages.dashboard.labels.no_data_order')"
            />
          </Dataset>
        </BaseBlock>
      </div>
    </div>
  </div>
</template>

<style scoped>
.noti-container {
  background-color: #eed3da;
  color: #966a74;
  padding: 15px 30px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
}

.a p {
  margin-bottom: 0 !important;
}

.a h2 {
  margin-bottom: 0 !important;
}

.a h3 {
  margin-bottom: 0 !important;
}

.text-1 {
  color: #9e9fa1;
  font-size: 13px;
  font-weight: 600;
}

.text-2 {
  font-weight: 600;
}
</style>
