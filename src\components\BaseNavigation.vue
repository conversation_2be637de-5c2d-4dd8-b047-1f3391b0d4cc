<script setup>
import { computed, ref, watch } from "vue";
import { useRoute } from "vue-router";
import { useTemplateStore } from "@/stores/template";
import Trans from "@/i18n/i18nUtils";
import { productService } from "@/services/product.service";
import { categoryService } from "@/services/category.service";
import { toppingService } from "@/services/topping.service";
import { toppingGroupService } from "@/services/toppingGroup.service";
import { favToppingGroupService } from "@/services/favToppingGroup.service";
import { tableService } from "@/services/table.service";
import { customerService } from "@/services/customer.service";
import { unitService } from "@/services/unit.service";
import { producerService } from "@/services/producer.service";
import { supplierService } from "@/services/supplier.service";
import { orderService } from "@/services/order.service";
import { printerLogService } from "@/services/printerLog.service";
import { userService } from "@/services/user.service";
import { transactionService } from "@/services/transaction.service";
import { reportGoodService } from "@/services/report_good.service";
import { dailySummaryService } from "@/services/dailySummary.service";
import { reportOrderService } from "@/services/report_order.service";
import { storeData } from "@/stores/storeData";
import { searchValue } from "@/stores/searchData";
import _ from "lodash";
import { scrollTo } from "@/stores/scollItemInlist";
import moment from "moment";
// Main store and Route
const store = useTemplateStore();
const route = useRoute();
const dataFetch = storeData();
const scrollStore = scrollTo();
const searchStore = searchValue();
// Component properties
const props = defineProps({
  nodes: {
    type: Array,
    description: "The nodes of the navigation",
  },
  subMenu: {
    type: Boolean,
    default: false,
    description: "If true, a submenu will be rendered",
  },
  dark: {
    type: Boolean,
    default: false,
    description: "Dark mode for menu",
  },
  horizontal: {
    type: Boolean,
    default: false,
    description: "Horizontal menu in large screen width",
  },
  horizontalHover: {
    type: Boolean,
    default: false,
    description: "Hover mode for horizontal menu",
  },
  horizontalCenter: {
    type: Boolean,
    default: false,
    description: "Center mode for horizontal menu",
  },
  horizontalJustify: {
    type: Boolean,
    default: false,
    description: "Justify mode for horizontal menu",
  },
  disableClick: {
    type: Boolean,
    default: false,
    description:
      "Disables submenu click on 2+ level when we are in horizontal and hover mode",
  },
});
// Set CSS classes accordingly
const classContainer = computed(() => {
  return {
    "nav-main": !props.subMenu,
    "nav-main-submenu": props.subMenu,
    "nav-main-dark": props.dark,
    "nav-main-horizontal": props.horizontal,
    "nav-main-hover": props.horizontalHover,
    "nav-main-horizontal-center": props.horizontalCenter,
    "nav-main-horizontal-justify": props.horizontalJustify,
  };
});

// Checks if a submenu path is part of the URL path
function subIsActive(paths) {
  const activePaths = Array.isArray(paths) ? paths : [paths];

  return activePaths.some((path) => {
    return route.path.indexOf(path) === 0; // current path starts with this path string
  });
}

// Main menu toggling and mobile functionality
function linkClicked(e, submenu) {
  if (submenu) {
    // Get closest li element
    let el = e.target.closest("li");

    // Check if we are in a large screen, have horizontal navigation and hover is enabled
    if (
      !(
        window.innerWidth > 991 &&
        ((props.horizontal && props.horizontalHover) || props.disableClick)
      )
    ) {
      if (el.classList.contains("open")) {
        // If submenu is open, close it..
        el.classList.remove("open");
      } else {
        // .. else if submenu is closed, close all other (same level) submenus first before open it
        Array.from(el.closest("ul").children).forEach((element) => {
          element.classList.remove("open");
        });

        el.classList.add("open");
      }
    }
  } else {
    // If we are in mobile, close the sidebar
    let el = e.target.closest("li");
    const siblings = el.closest("ul").querySelectorAll("li");
    siblings.forEach((sibling) => {
      // if (sibling !== el && sibling.classList.contains("open")) {
      sibling.classList.remove("open");
      // }
    });
    if (window.innerWidth < 992) {
      store.sidebar({ mode: "close" });
    }
  }
}

const activeModule = (toModule) => {
  const module = route.meta?.module;
  return toModule === module;
};

const nodeOld = ref();
const indexMenu = ref();

watch([nodeOld], () => {
  getData();
});

function defaultDateRange() {
  const startDate = moment().startOf("isoWeek").format("DD-MM-YYYY");
  const endDate = moment().endOf("isoWeek").format("DD-MM-YYYY");
  return `${startDate} to ${endDate}`;
}

const dateRange = ref(defaultDateRange());

const getData = async () => {
  if (nodeOld.value === "merchant-products-list") {
    try {
      const response = await productService.getList({
        limit: 10,
        page: 1,
      });
      dataFetch.setData(response?.data?.data, nodeOld.value);
      dataFetch.setTotal(Number(response.data.meta.total), nodeOld.value);
    } catch (e) {
      console.log(e);
    }
  } else if (nodeOld.value === "merchant-categories-list") {
    const response = await categoryService.getList({
      limit: 10,
      page: 1,
    });
    dataFetch.setData(response?.data?.data, nodeOld.value);
    dataFetch.setTotal(Number(response.data.meta.total), nodeOld.value);
  } else if (nodeOld.value === "merchant-toppings-list") {
    const response = await toppingService.getList({
      limit: 10,
      page: 1,
    });
    dataFetch.setData(response?.data?.data, nodeOld.value);
    dataFetch.setTotal(Number(response.data.meta.total), nodeOld.value);
  } else if (nodeOld.value === "merchant-topping-groups-list") {
    const response = await toppingGroupService.getList({
      limit: 10,
      page: 1,
    });
    dataFetch.setData(response?.data?.data, nodeOld.value);
    dataFetch.setTotal(Number(response.data.total), nodeOld.value);
  } else if (nodeOld.value === "merchant-fav-topping-groups-list") {
    const response = await favToppingGroupService.getList({
      limit: 10,
      page: 1,
    });
    dataFetch.setData(response?.data?.data, nodeOld.value);
    dataFetch.setTotal(Number(response.data.total), nodeOld.value);
  } else if (nodeOld.value === "merchant-tables-list") {
    const response = await tableService.getList({
      limit: 10,
      page: 1,
    });
    dataFetch.setData(response?.data?.data, nodeOld.value);
    dataFetch.setTotal(Number(response.data.meta.total), nodeOld.value);
  } else if (nodeOld.value === "merchant-customers-list") {
    const response = await customerService.getList({
      limit: 10,
      page: 1,
    });
    dataFetch.setData(response?.data?.data, nodeOld.value);
    dataFetch.setTotal(Number(response.data.total), nodeOld.value);
  } else if (nodeOld.value === "merchant-units-list") {
    const response = await unitService.getList({
      limit: 10,
      page: 1,
    });
    dataFetch.setData(response?.data?.data, nodeOld.value);
    dataFetch.setTotal(Number(response.data.total), nodeOld.value);
  } else if (nodeOld.value === "merchant-producers-list") {
    const response = await producerService.getList({
      limit: 10,
      page: 1,
    });
    dataFetch.setData(response?.data?.data, nodeOld.value);
    dataFetch.setTotal(Number(response.data.total), nodeOld.value);
  } else if (nodeOld.value === "merchant-suppliers-list") {
    const response = await supplierService.getList({
      limit: 10,
      page: 1,
    });
    dataFetch.setData(response?.data?.data, nodeOld.value);
    dataFetch.setTotal(Number(response.data.total), nodeOld.value);
  } else if (nodeOld.value === "merchant-orders-list") {
    let start_date;
    let end_date;
    const dates = [];
    const [startDateString, endDateString] = dateRange.value.split(" to ");
    start_date = moment(startDateString, "DD-MM-YYYY").toISOString();
    end_date = endDateString
      ? moment(endDateString, "DD-MM-YYYY").endOf("day").toISOString()
      : moment(startDateString, "DD-MM-YYYY").endOf("day").toISOString();
    let currentDate = moment(startDateString, "DD-MM-YYYY").clone();
    while (
      currentDate.isSameOrBefore(
        moment(endDateString || startDateString, "DD-MM-YYYY"),
        "day"
      )
    ) {
      dates.push(currentDate.format("DD-MM-YYYY"));
      currentDate.add(1, "day");
    }
    const response = await orderService.getList({
      start_date: start_date,
      end_date: end_date,
      limit: 10,
      page: 1,
    });
    dataFetch.setData(response?.data?.data, nodeOld.value);
    dataFetch.setTotal(Number(response.data.total), nodeOld.value);
  } else if (nodeOld.value === "merchant-printer-logs-list") {
    const response = await printerLogService.getList({
      limit: 10,
      page: 1,
    });
    dataFetch.setData(response?.data?.data, nodeOld.value);
    dataFetch.setTotal(Number(response.data.total), nodeOld.value);
  } else if (nodeOld.value === "merchant-users-list") {
    const response = await userService.getList({
      limit: 10,
      page: 1,
    });
    dataFetch.setData(response?.data?.data, nodeOld.value);
    dataFetch.setTotal(Number(response.data.total), nodeOld.value);
  } else if (nodeOld.value === "merchant-transactions-list") {
    const response = await transactionService.getList({
      limit: 10,
      page: 1,
    });
    dataFetch.setData(response?.data?.data, nodeOld.value);
    dataFetch.setTotal(Number(response.data.total), nodeOld.value);
  } else if (nodeOld.value === "merchant-report-goods") {
    let start_date;
    let end_date;
    const dates = [];
    const [startDateString, endDateString] = dateRange.value.split(" to ");
    start_date = moment(startDateString, "DD-MM-YYYY").toISOString();
    end_date = endDateString
      ? moment(endDateString, "DD-MM-YYYY").endOf("day").toISOString()
      : moment(startDateString, "DD-MM-YYYY").endOf("day").toISOString();
    let currentDate = moment(startDateString, "DD-MM-YYYY").clone();
    while (
      currentDate.isSameOrBefore(
        moment(endDateString || startDateString, "DD-MM-YYYY"),
        "day"
      )
    ) {
      dates.push(currentDate.format("DD-MM-YYYY"));
      currentDate.add(1, "day");
    }
    const response = await reportGoodService.getList({
      start_date: start_date,
      end_date: end_date,
      limit: 10,
      page: 1,
    });
    dataFetch.setData(response?.data?.data, nodeOld.value);
    dataFetch.setTotal(Number(response.data.total), nodeOld.value);
  } else if (nodeOld.value === "merchant-daily-summary-list") {
    let start_date;
    let end_date;
    const dates = [];
    const [startDateString, endDateString] = dateRange.value.split(" to ");
    start_date = moment(startDateString, "DD-MM-YYYY").toISOString();
    end_date = endDateString
      ? moment(endDateString, "DD-MM-YYYY").endOf("day").toISOString()
      : moment(startDateString, "DD-MM-YYYY").endOf("day").toISOString();
    let currentDate = moment(startDateString, "DD-MM-YYYY").clone();
    while (
      currentDate.isSameOrBefore(
        moment(endDateString || startDateString, "DD-MM-YYYY"),
        "day"
      )
    ) {
      dates.push(currentDate.format("DD-MM-YYYY"));
      currentDate.add(1, "day");
    }
    const response = await dailySummaryService.getList({
      start_date: start_date,
      end_date: end_date,
      limit: 10,
      page: 1,
    });
    dataFetch.setData(response?.data?.data, nodeOld.value);
    dataFetch.setTotal(Number(response.data.total), nodeOld.value);
    dataFetch.setSumDailySummary(response?.options, nodeOld.value);
  } else if (nodeOld.value === "merchant-report-order") {
    let start_date;
    let end_date;
    const dates = [];
    const [startDateString, endDateString] = dateRange.value.split(" to ");
    start_date = moment(startDateString, "DD-MM-YYYY").toISOString();
    end_date = endDateString
      ? moment(endDateString, "DD-MM-YYYY").endOf("day").toISOString()
      : moment(startDateString, "DD-MM-YYYY").endOf("day").toISOString();
    let currentDate = moment(startDateString, "DD-MM-YYYY").clone();
    while (
      currentDate.isSameOrBefore(
        moment(endDateString || startDateString, "DD-MM-YYYY"),
        "day"
      )
    ) {
      dates.push(currentDate.format("DD-MM-YYYY"));
      currentDate.add(1, "day");
    }
    const response = await reportOrderService.getList({
      start_date: start_date,
      end_date: end_date,
      limit: 10,
      page: 1,
    });
    dataFetch.setData(response?.data, nodeOld.value);
    dataFetch.setTotal(Number(response.data.orders.total), nodeOld.value);
  }
  // console.log(dataFetch.storeData);
};

const prefetchData = _.debounce((node, index) => {
  // console.log(node);
  nodeOld.value = node.to;
  indexMenu.value = index;
}, 300);

const resetStore = () => {
  scrollStore.setPage(1);
  searchStore.setValueSearch(null);
};
</script>

<template>
  <ul :class="classContainer">
    <li
      v-for="(node, index) in nodes?.filter((item) => !item?.hidden)"
      :key="`node-${index}`"
      :class="{
        'nav-main-heading': node.heading,
        'nav-main-item': !node.heading,
        open:
          node.sub && node.subActivePaths
            ? subIsActive(node.subActivePaths)
            : false,
      }"
      @mouseenter="prefetchData(node, index)"
    >
      <!-- Heading -->
      {{ node.heading ? node.name : "" }}
      <!-- Normal Link -->
      <div v-if="!node.heading && !node.sub" @click="linkClicked($event)">
        <RouterLink
          v-if="
            !node.to.startsWith('http://') && !node.to.startsWith('https://')
          "
          :to="
            node.to && node.to !== '#'
              ? Trans.i18nRoute({ name: node.to })
              : '#'
          "
          class="nav-main-link"
          :class="activeModule(node.module) ? 'active' : ''"
          :active-class="node.to && node.to !== '#' ? 'active' : ''"
          @click="resetStore"
        >
          <i v-if="node.icon" :class="`nav-main-link-icon ${node.icon}`"></i>
          <span v-if="node.name" class="nav-main-link-name">
            {{ node.name }}
          </span>
          <span
            v-if="node.badge"
            class="nav-main-link-badge badge rounded-pill"
            :class="
              node['badge-variant']
                ? `bg-${node['badge-variant']}`
                : 'bg-primary'
            "
            >{{ node.badge }}</span
          >
        </RouterLink>
        <a
          v-else
          :href="node.to"
          class="nav-main-link"
          :target="node.target || null"
        >
          <i v-if="node.icon" :class="`nav-main-link-icon ${node.icon}`"></i>
          <span v-if="node.name" class="nav-main-link-name">
            {{ node.name }}
          </span>
          <span
            v-if="node.badge"
            class="nav-main-link-badge badge rounded-pill"
            :class="
              node['badge-variant']
                ? `bg-${node['badge-variant']}`
                : 'bg-primary'
            "
            >{{ node.badge }}</span
          >
        </a>
      </div>
      <!-- END Normal Link -->

      <!-- Submenu Link -->
      <a
        v-else-if="!node.heading && node.sub"
        href="#"
        class="nav-main-link nav-main-link-submenu"
        @click.prevent="linkClicked($event, true)"
      >
        <i v-if="node.icon" :class="`nav-main-link-icon ${node.icon}`"></i>
        <span v-if="node.name" class="nav-main-link-name">{{ node.name }}</span>
        <span
          v-if="node.badge"
          class="nav-main-link-badge badge rounded-pill"
          :class="
            node['badge-variant'] ? `bg-${node['badge-variant']}` : 'bg-primary'
          "
          >{{ node.badge }}</span
        >
      </a>
      <!-- END Submenu Link -->

      <BaseNavigation
        v-if="node.sub"
        :nodes="node.sub"
        sub-menu
        :disable-click="props.horizontal && props.horizontalHover"
      />
    </li>
  </ul>
</template>
