import { defineStore } from "pinia";
import { http } from "@/services/Base/multiTenant.service";
import { ref } from "vue";
import { storeData } from "@/stores/storeData";

export const useMultiTenant = defineStore("muiltiTenant", () => {
  const watchChange = ref(false);
  const arrayStores = ref([]);
  const dataFetch = storeData();
  const getListStores = async () => {
    const res = await http.get("/list-stores");
    arrayStores.value = res.data;
    localStorage.setItem("arrayStores", JSON.stringify(arrayStores.value));
  };
  const loginIn = async (id, cookies, router, Trans, store) => {
    try {
      const response = await http.post(`/store-login/${id}`);
      if (response?.data?.user && response?.data?.access_token) {
        const {
          store,
          created_at,
          email,
          email_verified_at,
          id,
          is_top_manager,
          name,
          store_id,
          updated_at,
        } = response?.data?.user || {};
        const _tmpUser = {
          store,
          created_at,
          email,
          email_verified_at,
          id,
          is_top_manager,
          name,
          store_id,
          updated_at,
          // ...response?.data?.user,
          plans: response?.data?.store?.plans || [],
        };
        cookies.set("current_store", response?.data?.store?.id);
        localStorage.setItem("user_merchant", JSON.stringify(_tmpUser));
        cookies.set("token_merchant", response?.data?.access_token);
        cookies.set("role_merchant", response?.data?.user?.roles[0]?.name);
        cookies.set(
          "plans",
          !!response?.data?.store?.plans?.find((itm) => itm?.name === "Tip")
            ?.active
        );
        if (response?.data?.user?.store?.language) {
          await Trans.switchLanguage(response?.data?.user?.store?.language);
        }
        dataFetch.resetStore();
        await router.pushByName({ name: "merchant-dashboard" });
      }
    } catch (error) {
      cookies.remove("token_merchant");
      localStorage.removeItem("user_merchant");
      cookies.remove("role_merchant");
      cookies.remove("isMultiTenant");
      cookies.remove("token_multi_tenant");
      cookies.remove("current_store");
      localStorage.removeItem("arrayStores");
      router.push({ name: "merchant-auth-signin" });
      store.pageLoader({ mode: "off" });
    }
  };
  return {
    getListStores,
    arrayStores,
    loginIn,
    watchChange,
  };
});
