import { http } from "./Base/base.service";

export const favToppingGroupService = {
  async getList(query) {
    return await http.get("/global_customs", {
      params: query,
    });
  },

  async getDetail(id) {
    return await http.get("/global_customs/" + id);
  },

  async create(data) {
    return await http.post("/global_customs", data, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  async update(id, data) {
    return await http.post("/global_customs/" + id, data, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  async delete(id) {
    return await http.delete("/global_customs/" + id);
  },
};
