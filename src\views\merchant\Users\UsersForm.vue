<script setup>
import EIcon from "@/components/Elements/EIcon.vue";
import { useRoute } from "vue-router";
import { reactive, computed, ref, onMounted } from "vue";
import useVuelidate from "@vuelidate/core";
import { required, minLength, email, sameAs } from "@vuelidate/validators";
import { userService } from "@/services/user.service";
import { useTemplateStore } from "@/stores/template";
import useNotify from "@/composables/useNotify";
import { roleService } from "@/services/role.service";
import EButton from "@/components/Elements/EButton.vue";
import EModal from "@/components/Elements/EModal.vue";
import VueSelect from "vue-select";
import ChangePassword from "./ChangePassword.vue";
import useAppRouter from "@/composables/useRouter";
import { useI18n } from "vue-i18n";
import useAuth from "@/composables/useAuth";
import { storeData } from "@/stores/storeData";
import { scrollTo } from "@/stores/scollItemInlist";

const store = useTemplateStore();
const scrollStore = scrollTo();
const dataFetch = storeData();
const { setNotify } = useNotify();
const route = useRoute();
const router = useAppRouter();
const props = route.params;
const id = props?.id;
const user = ref();
const { t } = useI18n();
const { userInfo } = useAuth();
const typeSubmit = ref();

let state = reactive({
  name: null,
  roles: null,
  email: null,
  password: null,
});

const rules = computed(() => {
  let output = {
    name: {
      required,
      minLength: minLength(3),
    },
    roles: {
      required,
    },
    email: {
      required,
      email,
    },
    password: {
      required,
    },
  };
  if (id) {
    delete output.password;
  }
  return output;
});

let stateChangePassword = reactive({
  newPassword: null,
  confirmPassword: null,
});

let rulesChangePassword = computed(() => {
  let output = {
    newPassword: {
      required,
      minLengthValue: minLength(6),
    },
    confirmPassword: {
      required,
      sameAs: sameAs(computed(() => stateChangePassword.newPassword)),
    },
  };
  return output;
});

let v$ = useVuelidate(rules, state);
let vChangePassword$ = useVuelidate(rulesChangePassword, stateChangePassword);

async function onSubmit() {
  try {
    const result = await v$.value.$validate();

    if (!result) return;
    let payload = {
      name: state.name,
      email: state.email,
      password: state.password,
      roles: state.roles,
      store_id: userInfo.value.store_id,
    };
    if (id) {
      delete payload.password;
    }
    store.pageLoader({ mode: "on" });

    const response = id
      ? await userService.update(id, payload)
      : await userService.create(payload);
    if (!response?.error) {
      if (typeSubmit.value === "confirm") {
        scrollStore.getElement(id);
        dataFetch.setData([], "merchant-users-list");
        dataFetch.setTotal(0, "merchant-users-list");
        return await router.pushByName({ name: "merchant-users-list" });
      } else {
        state.name = null;
        state.roles = null;
        state.email = null;
        state.password = null;
        v$.value.$reset();
      }
    }
    store.pageLoader({ mode: "off" });
    return setNotify({
      title: "Success",
      message: response?.message,
      type: "success",
    });
  } catch (e) {
    return setNotify({
      title: "Error",
      message: e?.message,
    });
  } finally {
    store.pageLoader({ mode: "off" });
  }
}

async function onSubmitPassword() {
  try {
    const result = await vChangePassword$.value.$validate();
    if (!result) return;
    let payload = {
      old_password: stateChangePassword.password,
      new_password: stateChangePassword.newPassword,
      new_password_confirmation: stateChangePassword.confirmPassword,
    };

    const response = await userService.changePassword(id, payload);
    if (!response?.error) {
      return setNotify({
        title: "Success",
        message: response?.message,
        type: "success",
      });
    }
    return setNotify({
      title: "Error",
      message: response?.message,
    });
  } catch (e) {
    return setNotify({
      title: "Error",
      message: e?.message,
    });
  } finally {
    store.pageLoader({ mode: "off" });
  }
}

const apiGetItem = async () => {
  try {
    store.pageLoader({ mode: "on" });
    const response = await userService.get(id);
    if (!response?.error) {
      user.value = response.data;

      state = reactive({
        name: response.data?.user?.name,
        role: response.data.roles,
        email: response.data?.user?.email,
        roles: response.data?.roles || [],
      });

      v$ = useVuelidate(rules, state);
    }
  } catch (error) {
    console.log(error);
    store.pageLoader({ mode: "off" });
  }
};

const listRoles = ref([]);
const onFetchListRoles = async () => {
  try {
    const response = await roleService.getList();
    if (!response?.error) {
      listRoles.value = response.data?.map((item) => item.name) || [];
    }
    store.pageLoader({ mode: "off" });
  } catch (error) {
    console.log(error);
    store.pageLoader({ mode: "off" });
  }
};

onMounted(async () => {
  try {
    if (id) await apiGetItem();
    await onFetchListRoles();
  } catch (error) {
    console.error("Error fetching data:", error);
  }
});

const handleSubmit = (type) => {
  typeSubmit.value = type;
};
</script>

<template>
  <BasePageHeading
    :title="id ? t('pages.user.titles.update') : t('pages.user.titles.create')"
    :go-back="true"
    :subtitle="t('pages.user.labels.label_head_form')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.user.labels.manage")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <router-link to="/merchant/users">{{
              t("pages.user.titles.list")
            }}</router-link>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <span>{{
              id ? t("pages.user.titles.update") : t("pages.user.titles.create")
            }}</span>
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <div class="row justify-content-center">
      <div v-if="!!id" class="col-sm-12 col-md-12 text-end mb-4">
        <EButton
          type="info"
          size="sm"
          data-bs-toggle="modal"
          data-bs-target="#modal-change-password"
        >
          {{ t("pages.user.labels.btn_change_password") }}
        </EButton>
      </div>
      <div class="col-sm-12 col-md-8">
        <form @submit.prevent="onSubmit">
          <BaseBlock
            :title="
              id
                ? `${t('pages.user.titles.update')} ${user?.user?.name}`
                : t('pages.user.titles.add')
            "
          >
            <template #options>
              <e-icon
                @click="
                  () => {
                    router.back();
                    dataFetch.setData([], 'merchant-users-list');
                    dataFetch.setTotal(0, 'merchant-users-list');
                  }
                "
                name="arrow-left"
                role="button"
                class="icon_arrow_left"
              />
            </template>

            <div class="row justify-content-center py-sm-3 py-md-5">
              <div class="col-sm-10 col-md-8">
                <div class="mb-4">
                  <label class="form-label" for="form-name"
                    >{{ t("pages.user.fields.user_name")
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    type="text"
                    class="form-control"
                    id="form-name"
                    name="form-name"
                    :placeholder="t('pages.user.placeholder.name')"
                    :class="{
                      'is-invalid': v$.name.$errors.length,
                    }"
                    v-model="state.name"
                    @blur="v$.name.$touch"
                  />
                  <div
                    v-if="v$.name.$errors.length"
                    class="invalid-feedback animated fadeIn"
                  >
                    {{ t("pages.user.validate.require_name") }}
                  </div>
                </div>

                <div class="mb-4">
                  <label class="form-label" for="form-role"
                    >{{ t("pages.user.fields.role")
                    }}<span class="text-danger">*</span></label
                  >
                  <VueSelect
                    multiple
                    v-model="state.roles"
                    :options="listRoles"
                    :placeholder="t('pages.user.placeholder.select')"
                    id="form-role"
                    @blur="v$.roles.$touch"
                    :class="{
                      'is-invalid': v$.roles.$errors.length,
                    }"
                  />
                  <div
                    v-if="v$.roles.$errors.length"
                    class="invalid-feedback animated fadeIn"
                  >
                    {{ t("pages.user.validate.require_role") }}
                  </div>
                </div>

                <div class="mb-4">
                  <label class="form-label" for="form-id"
                    >{{ t("pages.user.fields.email")
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    type="email"
                    class="form-control"
                    id="form-email"
                    name="form-email"
                    :placeholder="t('pages.user.placeholder.email')"
                    :class="{
                      'is-invalid': v$.email.$errors.length,
                    }"
                    v-model="state.email"
                    @blur="v$.email.$touch"
                  />
                  <div
                    v-if="v$.email.$errors.length"
                    class="invalid-feedback animated fadeIn"
                  >
                    {{ t("pages.user.validate.require_email") }}
                  </div>
                </div>

                <div class="mb-4" v-if="!id">
                  <label class="form-label" for="form-id"
                    >{{ t("pages.user.fields.password")
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    type="password"
                    class="form-control"
                    id="form-password"
                    name="form-password"
                    :placeholder="t('pages.user.placeholder.password')"
                    :class="{
                      'is-invalid': v$.password.$errors.length,
                    }"
                    v-model="state.password"
                    @blur="v$.password.$touch"
                  />
                  <div
                    v-if="v$.password.$errors.length"
                    class="invalid-feedback animated fadeIn"
                  >
                    {{ t("pages.user.validate.require_password") }}
                  </div>
                </div>

                <div
                  class="my-4"
                  :style="{
                    textAlign: 'end',
                    display: 'flex',
                    gap: '5px',
                    justifyContent: 'end',
                  }"
                >
                  <button
                    type="submit"
                    class="btn btn-sm btn-primary"
                    @click="handleSubmit('confirm')"
                    :style="{ color: '#fff' }"
                  >
                    {{ t("buttons.confirm") }}
                  </button>
                  <button
                    v-if="!id"
                    type="submit"
                    class="btn btn-sm btn-primary"
                    @click="handleSubmit('confirm_add')"
                    :style="{ color: '#fff' }"
                  >
                    {{ t("pages.products.buttons.confirm_add") }}
                  </button>
                </div>
              </div>
            </div>
          </BaseBlock>
        </form>
      </div>
    </div>
  </div>
  <form v-if="!!id" @submit.prevent="onSubmitPassword">
    <EModal
      id="modal-change-password"
      title="Change Password"
      ok-text="Change Password"
      ok-type="submit"
      :close-on-submit="false"
      @confirm="() => onSubmitPassword()"
    >
      <template v-slot:childrenComponent>
        <ChangePassword :v$="vChangePassword$" :state="stateChangePassword" />
      </template>
    </EModal>
  </form>
</template>

<style lang="scss">
@import "vue-select/dist/vue-select.css";
@import "@/assets/scss/vendor/vue-select";
.icon_arrow_left {
  background-color: rgb(243, 235, 235);
  border-radius: 100%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.is-invalid > div {
  border-color: #d61f47;
}
</style>
