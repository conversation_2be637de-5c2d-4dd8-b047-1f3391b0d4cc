<script setup>
import EIcon from "@/components/Elements/EIcon.vue";
import { useRoute } from "vue-router";
import { reactive, computed, ref, onMounted } from "vue";
import useVuelidate from "@vuelidate/core";
import { required, email } from "@vuelidate/validators";
import { supplierService } from "@/services/supplier.service";
import { useTemplateStore } from "@/stores/template";
import useNotify from "@/composables/useNotify";
import useAppRouter from "@/composables/useRouter";
import { useI18n } from "vue-i18n";
import { storeData } from "@/stores/storeData";
import { scrollTo } from "@/stores/scollItemInlist";

const store = useTemplateStore();
const scrollStore = scrollTo();
const dataFetch = storeData();
const { t } = useI18n();
const route = useRoute();
const router = useAppRouter();
const { setNotify } = useNotify();
const typeSubmit = ref();
const props = route.params;
const id = props?.id;

const supplier = ref();

let state = reactive({
  name: null,
  address: null,
  customer_number: null,
  account: null,
  email: null,
  telephone: null,
  postal_code: null,
  place: null,
  country: null,
  website: null,
  payment_info: null,
  description: null,
});

const rules = computed(() => {
  return {
    name: { required },
    address: { required },
    customer_number: { required },
    account: { required },
    email: { required, email },
    telephone: { required },
    postal_code: { required },
    place: { required },
    country: { required },
    website: { required },
    payment_info: { required },
    description: { required },
  };
});

let v$ = useVuelidate(rules, state);

async function onSubmit() {
  try {
    const result = await v$.value.$validate();

    if (!result) return;
    store.pageLoader({ mode: "on" });

    if (id) {
      await supplierService.update(id, {
        ...state,
        postal_code: state.postal_code.toString(),
      });
    } else {
      await supplierService.create(state);
    }
    if (typeSubmit.value === "confirm") {
      scrollStore.getElement(id);
      dataFetch.setData([], "merchant-suppliers-list");
      dataFetch.setTotal(0, "merchant-suppliers-list");
      router.pushByName({ name: "merchant-suppliers-list" });
    } else {
      state.name = null;
      state.account = null;
      state.address = null;
      state.country = null;
      state.place = null;
      state.customer_number = null;
      state.description = null;
      state.email = null;
      state.payment_info = null;
      state.postal_code = null;
      state.telephone = null;
      state.website = null;
      v$.value.$reset();
    }
    store.pageLoader({ mode: "off" });
    setNotify({
      title: "Success",
      message: id ? "Supplier update success" : "Supplier create success",
      type: "success",
    });
  } catch (e) {
    store.pageLoader({ mode: "off" });
    return setNotify({
      title: "Error",
      message: e?.message,
    });
  }
}

const apiGetItem = async () => {
  try {
    store.pageLoader({ mode: "on" });
    const response = await supplierService.get(id);
    supplier.value = response.data;

    state = reactive({
      name: response.data.name,
      address: response.data.address,
      customer_number: response.data.customer_number,
      account: response.data.account,
      email: response.data.email,
      telephone: response.data.telephone,
      postal_code: response.data.postal_code,
      place: response.data.place,
      country: response.data.country,
      website: response.data.website,
      payment_info: response.data.payment_info,
      description: response.data.description,
    });

    v$ = useVuelidate(rules, state);
    store.pageLoader({ mode: "off" });
  } catch (error) {
    console.log(error);
    store.pageLoader({ mode: "off" });
  }
};

onMounted(async () => {
  try {
    if (id) apiGetItem();
  } catch (error) {
    console.error("Error fetching data:", error);
  }
});

const handleSubmit = (type) => {
  typeSubmit.value = type;
};
</script>

<template>
  <BasePageHeading
    :title="
      id
        ? t('pages.suppliers.titles.update')
        : t('pages.suppliers.titles.create')
    "
    :go-back="true"
    :subtitle="t('pages.suppliers.labels.label_head_list')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.report.labels.manages")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <router-link to="/merchant/suppliers">{{
              t("pages.report.labels.list_suppliers")
            }}</router-link>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <span>{{
              id
                ? t("pages.suppliers.titles.update")
                : t("pages.suppliers.titles.create")
            }}</span>
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <div class="row justify-content-start">
      <div>
        <form @submit.prevent="onSubmit">
          <BaseBlock
            :title="
              id
                ? `${t('pages.suppliers.titles.update')} ${supplier?.id}`
                : t('pages.suppliers.titles.create')
            "
          >
            <template #options>
              <e-icon
                @click="
                  () => {
                    router.back();
                    dataFetch.setData([], 'merchant-suppliers-list');
                    dataFetch.setTotal(0, 'merchant-suppliers-list');
                  }
                "
                name="arrow-left"
                role="button"
                class="icon_arrow_left"
              />
            </template>

            <div class="row justify-content-center py-sm-1 py-md-2">
              <div>
                <div class="row mb-2">
                  <div class="col-md-3 mb-4">
                    <label class="form-label" for="form-name"
                      >{{ t("pages.suppliers.fields.name_field")
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      type="text"
                      class="form-control"
                      id="form-name"
                      name="form-name"
                      :placeholder="t('pages.placeholder.enter')"
                      :class="{
                        'is-invalid': v$.name.$errors.length,
                      }"
                      v-model="state.name"
                      @blur="v$.name.$touch"
                    />
                    <div
                      v-if="v$.name.$errors.length"
                      class="invalid-feedback animated fadeIn"
                    >
                      {{ t("pages.suppliers.validate.require_name") }}
                    </div>
                  </div>

                  <div class="col-md-5 mb-4">
                    <label class="form-label" for="form-id"
                      >{{ t("pages.suppliers.fields.address_field")
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      type="text"
                      class="form-control"
                      id="form-address"
                      name="form-address"
                      :placeholder="t('pages.suppliers.fields.address_field')"
                      :class="{
                        'is-invalid': v$.address.$errors.length,
                      }"
                      v-model="state.address"
                      @blur="v$.address.$touch"
                    />
                    <div
                      v-if="v$.address.$errors.length"
                      class="invalid-feedback animated fadeIn"
                    >
                      {{ t("pages.suppliers.validate.require_address") }}
                    </div>
                  </div>

                  <div class="col-md-4 mb-4">
                    <label class="form-label" for="form-id"
                      >{{ t("pages.suppliers.fields.web_address_field")
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      type="text"
                      class="form-control"
                      id="form-web-address"
                      name="form-web-address"
                      :placeholder="
                        t('pages.suppliers.fields.web_address_field')
                      "
                      :class="{
                        'is-invalid': v$.website.$errors.length,
                      }"
                      v-model="state.website"
                      @blur="v$.website.$touch"
                    />
                    <div
                      v-if="v$.website.$errors.length"
                      class="invalid-feedback animated fadeIn"
                    >
                      {{ t("pages.suppliers.validate.require_web_address") }}
                    </div>
                  </div>
                </div>

                <div class="row mb-2">
                  <div class="col-md-3 mb-4">
                    <label class="form-label" for="form-name"
                      >{{ t("pages.suppliers.fields.telephone_field")
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      type="number"
                      class="form-control no-spinner"
                      id="form-telephone"
                      name="form-telephone"
                      :placeholder="t('pages.suppliers.placeholder.telephone')"
                      :class="{
                        'is-invalid': v$.telephone.$errors.length,
                      }"
                      v-model="state.telephone"
                      @blur="v$.telephone.$touch"
                    />
                    <div
                      v-if="v$.telephone.$errors.length"
                      class="invalid-feedback animated fadeIn"
                    >
                      {{ t("pages.suppliers.validate.require_telephone") }}
                    </div>
                  </div>

                  <div class="col-md-3 mb-4">
                    <label class="form-label" for="form-id"
                      >{{ t("pages.suppliers.fields.email_field")
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      type="text"
                      class="form-control"
                      id="form-email"
                      name="form-email"
                      placeholder="email"
                      :class="{
                        'is-invalid': v$.email.$errors.length,
                      }"
                      v-model="state.email"
                      @blur="v$.email.$touch"
                    />
                    <div
                      v-if="v$.email.$errors.length"
                      class="invalid-feedback animated fadeIn"
                    >
                      {{ t("pages.suppliers.validate.require_email") }}
                    </div>
                  </div>

                  <div class="col-md-3 mb-4">
                    <label class="form-label" for="form-id"
                      >{{ t("pages.suppliers.fields.customer_number")
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      type="text"
                      class="form-control"
                      id="form-customer-number"
                      name="form-customer-number"
                      :placeholder="t('pages.suppliers.fields.customer_number')"
                      :class="{
                        'is-invalid': v$.customer_number.$errors.length,
                      }"
                      v-model="state.customer_number"
                      @blur="v$.customer_number.$touch"
                    />
                    <div
                      v-if="v$.customer_number.$errors.length"
                      class="invalid-feedback animated fadeIn"
                    >
                      {{
                        t("pages.suppliers.validate.require_customer_number")
                      }}
                    </div>
                  </div>

                  <div class="col-md-3 mb-4">
                    <label class="form-label" for="form-id"
                      >{{ t("pages.suppliers.fields.account")
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      type="text"
                      class="form-control"
                      id="form-account"
                      name="form-account"
                      :placeholder="t('pages.suppliers.fields.account')"
                      :class="{
                        'is-invalid': v$.account.$errors.length,
                      }"
                      v-model="state.account"
                      @blur="v$.account.$touch"
                    />
                    <div
                      v-if="v$.account.$errors.length"
                      class="invalid-feedback animated fadeIn"
                    >
                      {{ t("pages.suppliers.validate.require_account") }}
                    </div>
                  </div>
                </div>

                <div class="row mb-2">
                  <div class="col-md-3 mb-4">
                    <label class="form-label" for="form-id"
                      >{{ t("pages.suppliers.fields.postal") }}
                      <noframes></noframes
                      ><span class="text-danger">*</span></label
                    >
                    <input
                      type="text"
                      class="form-control"
                      id="form-postal-no"
                      name="form-postal-no"
                      :placeholder="t('pages.suppliers.placeholder.postal')"
                      :class="{
                        'is-invalid': v$.postal_code.$errors.length,
                      }"
                      v-model="state.postal_code"
                      @blur="v$.postal_code.$touch"
                    />
                    <div
                      v-if="v$.postal_code.$errors.length"
                      class="invalid-feedback animated fadeIn"
                    >
                      {{ t("pages.suppliers.validate.require_postal") }}
                    </div>
                  </div>

                  <div class="col-md-4 mb-4">
                    <label class="form-label" for="form-id"
                      >{{ t("pages.suppliers.fields.place_field")
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      type="text"
                      class="form-control"
                      id="form-place"
                      name="form-place"
                      :placeholder="t('pages.suppliers.placeholder.place')"
                      :class="{
                        'is-invalid': v$.place.$errors.length,
                      }"
                      v-model="state.place"
                      @blur="v$.place.$touch"
                    />
                    <div
                      v-if="v$.place.$errors.length"
                      class="invalid-feedback animated fadeIn"
                    >
                      {{ t("pages.suppliers.validate.require_place") }}
                    </div>
                  </div>

                  <div class="col-md-2 mb-4">
                    <label class="form-label" for="form-id"
                      >{{ t("pages.suppliers.fields.country_field")
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      type="text"
                      class="form-control"
                      id="form-country"
                      name="form-country"
                      :placeholder="t('pages.suppliers.placeholder.country')"
                      :class="{
                        'is-invalid': v$.country.$errors.length,
                      }"
                      v-model="state.country"
                      @blur="v$.country.$touch"
                    />
                    <div
                      v-if="v$.country.$errors.length"
                      class="invalid-feedback animated fadeIn"
                    >
                      {{ t("pages.suppliers.validate.require_country") }}
                    </div>
                  </div>
                </div>

                <div class="row mb-2">
                  <div class="col-md-7 mb-4">
                    <label class="form-label" for="form-description"
                      >{{ t("pages.suppliers.fields.description_field")
                      }}<span class="text-danger">*</span></label
                    >
                    <textarea
                      class="form-control"
                      id="form-description"
                      name="form-description"
                      :class="{
                        'is-invalid': v$.description.$errors.length,
                      }"
                      v-model="state.description"
                      @blur="v$.description.$touch"
                      rows="5"
                      :placeholder="
                        t('pages.suppliers.placeholder.description')
                      "
                    ></textarea>
                    <div
                      v-if="v$.description.$errors.length"
                      class="invalid-feedback animated fadeIn"
                    >
                      {{ t("pages.suppliers.validate.require_description") }}
                    </div>
                  </div>

                  <div class="col-md-5 mb-4">
                    <label class="form-label" for="form-id"
                      >{{ t("pages.suppliers.fields.payment_field")
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      type="text"
                      class="form-control"
                      id="form-payment-information"
                      name="form-payment-information"
                      :placeholder="
                        t('pages.suppliers.placeholder.payment_info')
                      "
                      :class="{
                        'is-invalid': v$.payment_info.$errors.length,
                      }"
                      v-model="state.payment_info"
                      @blur="v$.payment_info.$touch"
                    />
                    <div
                      v-if="v$.payment_info.$errors.length"
                      class="invalid-feedback animated fadeIn"
                    >
                      {{ t("pages.suppliers.validate.require_payment_info") }}
                    </div>
                  </div>
                </div>

                <div
                  class="my-4"
                  :style="{
                    textAlign: 'end',
                    display: 'flex',
                    gap: '5px',
                    justifyContent: 'end',
                  }"
                >
                  <button
                    type="submit"
                    @click="handleSubmit('confirm')"
                    class="btn btn-sm btn-primary"
                    :style="{ color: '#fff' }"
                  >
                    {{ t("buttons.confirm") }}
                  </button>
                  <button
                    type="submit"
                    v-if="!id"
                    class="btn btn-sm btn-primary"
                    @click="handleSubmit('confirm_add')"
                    :style="{ color: '#fff' }"
                  >
                    {{ t("pages.products.buttons.confirm_add") }}
                  </button>
                </div>
              </div>
            </div>
          </BaseBlock>
        </form>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.icon_arrow_left {
  background-color: rgb(243, 235, 235);
  border-radius: 100%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.no-spinner::-webkit-inner-spin-button,
.no-spinner::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.no-spinner {
  -moz-appearance: textfield;
}
</style>
