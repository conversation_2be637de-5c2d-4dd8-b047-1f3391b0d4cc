//
// Vue Select
//
// Overwrite/Extend styles
// --------------------------------------------------

// Include base files
@import "base";

// Custom styles
.vs__dropdown-option--highlight {
  background: $primary;
}

.vs__dropdown-toggle {
  padding: 2px 4px 6px;
}

.vs__dropdown-toggle,
.vs__dropdown-menu {
  border-color: $input-border-color;
}

.vs__selected {
  background: $light;
  border-color: $light;
}

.vs__search::placeholder {
  color: $input-placeholder-color;
  opacity: 1;
}

.vs__clear {
  margin-right: 10px;
  margin-top: -4px;
}

.vs__clear,
.vs__deselect {
  fill: $danger;
}

.vs__deselect {
  margin-left: 6px;
}

.vs__open-indicator {
  fill: $gray-500;
}

.vs--open .vs__open-indicator {
  transform: scaleY(-1);
}

.vs__dropdown-option--selected {
  opacity: 0.5;
}
