import { http } from './Base/base.service'

export const supplierService = {
  async getList(query) {
    return await http.get('/suppliers', {
      params: query
    })
  },

  async get(id) {
    return await http.get('/suppliers/' + id)
  },

  async create(data) {
    return await http.post('/suppliers', data)
  },

  async update(id, data) {
    return await http.patch('/suppliers/' + id, data)
  },

  async delete(id) {
    return await http.delete('/suppliers/' + id)
  }
}
