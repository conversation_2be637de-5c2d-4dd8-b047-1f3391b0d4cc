<script setup>
import EIcon from '@/components/Elements/EIcon.vue'
import useNotify from "@/composables/useNotify"
import useAppRouter from '@/composables/useRouter'
import { printerService } from '@/services/printer.service'
import { useTemplateStore } from '@/stores/template'
import useVuelidate from '@vuelidate/core'
import { required } from '@vuelidate/validators'
import { computed, onMounted, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'

const store = useTemplateStore()
const {setNotify}= useNotify()
const route = useRoute()
const router = useAppRouter()
const {t} = useI18n();

const props = route.params
const id = props?.id

const printer = ref()
const optionsType = reactive([
  { value: null, text: 'Please select' },
  { value: 1, text: 'Counter' },
  { value: 2, text: 'Kitchen' }
])

let state = reactive({
  name: null,
  printer_id: null,
  username: null,
  access_key: null,
  type: null
})

const rules = computed(() => {
  return {
    name: { required },
    printer_id: { required },
    username: { required },
    access_key: { required },
    type: { required }
  }
})

let v$ = useVuelidate(rules, state)

async function onSubmit() {
  try {
    const result = await v$.value.$validate()

    if (!result) return
    store.pageLoader({ mode: 'on' })

    if (id) {
      await printerService.update(id, state)
    } else {
      await printerService.create(state)
    }

    router.pushByName({ name: 'merchant-printers-list' })
  } catch (e) {
    console.log(e)
    store.pageLoader({ mode: 'off' })
    setNotify({
      title: 'Error',
      message: e?.message
    })
  }
}

const apiGetItem = async () => {
  try {
    store.pageLoader({ mode: 'on' })
    const response = await printerService.get(id)
    printer.value = response.data

    state = reactive({
      name: response.data.name,
      printer_id: response.data.printer_id,
      username: response.data.username,
      access_key: response.data.access_key,
      type: response.data.type
    })

    v$ = useVuelidate(rules, state)
    store.pageLoader({ mode: 'off' })
  } catch (error) {
    console.log(error)
    store.pageLoader({ mode: 'off' })
  }
}

onMounted(async () => {
  try {
    if (id) await apiGetItem()
  } catch (error) {
    console.error('Error fetching data:', error)
  }
})
</script>

<template>
  <BasePageHeading
    :title="id ? t('pages.printer.titles.update') : t('pages.printer.titles.create')"
    :go-back="true"
    :subtitle="t('pages.printer.labels.label_head_list')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{ t('pages.printer.labels.manages') }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <router-link to="/merchant/printers">{{ t('pages.printer.titles.list') }}</router-link>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <span>{{ t('pages.printer.titles.create') }}</span>
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <div class="row justify-content-start">
      <div class="col-sm-12 col-md-8">
        <form @submit.prevent="onSubmit">
          <BaseBlock :title="id ? t('pages.printer.titles.detail', {item: printer?.id}) : t('pages.printer.titles.create')">
            <template #options>
              <e-icon
                @click="() => router.back()"
                name="arrow-left"
                role="button"
                class="icon_arrow_left"
              />
            </template>

            <div class="row justify-content-center py-sm-3 py-md-5">
              <div class="col-sm-10 col-md-8">
                <div class="mb-4">
                  <label class="form-label" for="form-printer-name"
                    >{{ t('pages.printer.fields.printer_name') }}<span class="text-danger">*</span></label
                  >
                  <input
                    type="text"
                    class="form-control"
                    id="form-printer-name"
                    name="form-printer-name"
                    :placeholder="t('pages.printer.placeholder.enter_print_name')"
                    :class="{
                      'is-invalid': v$.name.$errors.length
                    }"
                    v-model="state.name"
                    @blur="v$.name.$touch"
                  />
                  <div v-if="v$.name.$errors.length" class="invalid-feedback animated fadeIn">
                    Please enter name printer
                  </div>
                </div>

                <div class="mb-4">
                  <label class="form-label" for="form-printer-id"
                    >{{ t('pages.printer.fields.printer_id_field') }}<span class="text-danger">*</span></label
                  >
                  <input
                    type="text"
                    class="form-control"
                    id="form-printer-id"
                    name="form-printer-id"
                    :placeholder="t('pages.printer.placeholder.enter_print_id')"
                    :class="{
                      'is-invalid': v$.printer_id.$errors.length
                    }"
                    v-model="state.printer_id"
                    @blur="v$.printer_id.$touch"
                  />
                  <div v-if="v$.printer_id.$errors.length" class="invalid-feedback animated fadeIn">
                    Please enter id printer
                  </div>
                </div>

                <div class="mb-4">
                  <label class="form-label" for="form-username"
                    >{{ t('pages.printer.fields.user_name_field') }}<span class="text-danger">*</span></label
                  >
                  <input
                    type="text"
                    class="form-control"
                    id="form-username"
                    name="form-username"
                    :placeholder="t('pages.printer.placeholder.enter_print_username')"
                    :class="{
                      'is-invalid': v$.username.$errors.length
                    }"
                    v-model="state.username"
                    @blur="v$.username.$touch"
                  />
                  <div v-if="v$.username.$errors.length" class="invalid-feedback animated fadeIn">
                    Please enter username
                  </div>
                </div>

                <div class="mb-4">
                  <label class="form-label" for="form-access-key"
                    >{{ t('pages.printer.fields.access_key') }}<span class="text-danger">*</span></label
                  >
                  <input
                    type="text"
                    class="form-control"
                    id="form-access-key"
                    name="form-access-key"
                    :placeholder="t('pages.printer.placeholder.enter_access_key')"
                    :class="{
                      'is-invalid': v$.access_key.$errors.length
                    }"
                    v-model="state.access_key"
                    @blur="v$.access_key.$touch"
                  />
                  <div v-if="v$.access_key.$errors.length" class="invalid-feedback animated fadeIn">
                    Please enter access key
                  </div>
                </div>

                <div class="mb-4">
                  <label class="form-label" for="val-type-id"
                    >{{ t('pages.printer.fields.type_field') }}<span class="text-danger">*</span></label
                  >
                  <select
                    id="val-type-id"
                    class="form-select"
                    :class="{
                      'is-invalid': v$.type.$errors.length
                    }"
                    v-model="state.type"
                    @blur="v$.type.$touch"
                  >
                    <option
                      v-for="(item, index) in optionsType"
                      :value="item.value"
                      :key="`item-${index}`"
                    >
                      {{ item.text }}
                    </option>
                  </select>
                  <div v-if="v$.type.$errors.length" class="invalid-feedback animated fadeIn">
                    Please select a type!
                  </div>
                </div>

                <div class="mb-4" :style="{ textAlign: 'end' }">
                  <button type="submit" class="btn btn-sm btn-primary" :style="{ color: '#fff' }">
                    {{ t('buttons.confirm') }}
                  </button>
                </div>
              </div>
            </div>
          </BaseBlock>
        </form>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.icon_arrow_left {
  background-color: rgb(243, 235, 235);
  border-radius: 100%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
