<script setup>
import BasePageHeading from "@/components/BasePageHeading.vue";
import EButton from "@/components/Elements/EButton.vue";
import { reportTimeService } from "@/services/report_time.service";
import { range } from "lodash";
import moment from "moment";
import { onMounted, ref } from "vue";
import { Dataset } from "vue-dataset";
import FlatPickr from "vue-flatpickr-component";
import { useI18n } from "vue-i18n";
const dateRange = ref(defaultDateRange());
const { t } = useI18n();
const handleChange = async (val) => {
  if (val?.length === 2) {
    await onFetchList();
  }
};
const configRange = ref({
  mode: "range",
  defaultDate: [
    moment().startOf("isoWeek").format("YYYY-MM-DD"),
    moment().endOf("isoWeek").format("YYYY-MM-DD"),
  ],
  dateFormat: "d-m-Y",
  onChange: handleChange,
  locale: { firstDayOfWeek: 1 },
  minDate: null,
  maxDate: null,
});
const listTimeReports = ref();
const cols = ref([
  {
    name: "",
    field: "",
    sort: "",
  },
]);

const total = ref();

const onFetchList = async () => {
  let start_date;
  let end_date;
  const dateArray = dateRange.value.split(" to ");
  if (dateArray?.length === 2) {
    start_date = moment(dateArray[0], "DD-MM-YYYY");
    end_date = moment(dateArray[1], "DD-MM-YYYY");
  } else {
    start_date = moment(dateArray[0], "DD-MM-YYYY");
    end_date = moment(dateArray[0], "DD-MM-YYYY");
  }
  const daysOfWeek = [];
  for (let m = start_date; m <= end_date; m.add(1, "days")) {
    const dayOfWeek = m.format("dddd");
    daysOfWeek.push({
      name: dayOfWeek,
      field: dayOfWeek,
      sort: "",
    });
  }

  cols.value = [
    {
      name: "",
      field: "",
      sort: "",
    },
    ...daysOfWeek,
  ];
  const hoursInDay = range(0, 24);
  const response = await reportTimeService.getList({
    start_date: moment(start_date?._i, "DD-MM-YYYY").toISOString(),
    end_date: moment(end_date?._i, "DD-MM-YYYY").endOf("day").toISOString(),
  });
  const data = response.data.data || [];
  const list = hoursInDay?.map((itm) => {
    const val = data?.find((i) => Number(i.hour) === itm);
    const item = [
      {
        day_of_week: t("pages.report.fields.monday"),
        value: 0,
      },
      {
        day_of_week: t("pages.report.fields.tuesday"),
        value: 0,
      },
      {
        day_of_week: t("pages.report.fields.wednesday"),
        value: 0,
      },
      {
        day_of_week: t("pages.report.fields.thursday"),
        value: 0,
      },
      {
        day_of_week: t("pages.report.fields.friday"),
        value: 0,
      },
      {
        day_of_week: t("pages.report.fields.saturday"),
        value: 0,
      },
      {
        day_of_week: t("pages.report.fields.sunday"),
        value: 0,
      },
    ]?.map((itm) => {
      if (itm.day_of_week === val?.day_of_week?.trim()) {
        return {
          day_of_week: val.day_of_week,
          value: val.order_count,
        };
      }
      return itm;
    });
    return {
      hour: itm,
      order_count: 0,
      item,
    };
  });
  listTimeReports.value = list;
  total.value = response.data.total;
};
onMounted(async () => {
  await onFetchList();
  document.querySelectorAll("#datasetLength label").forEach((el) => {
    el.remove();
  });
  let selectLength = document.querySelector("#datasetLength select");
  if (selectLength) {
    selectLength.classList = "";
    selectLength.classList.add("form-select");
    selectLength.style.width = "80px";
  }
});

function formatTime(hour) {
  if (hour < 0 || hour > 24) {
    return "Invalid hour";
  }

  const startOfDay = moment().startOf("day");
  const targetTime = startOfDay.add(hour, "hours");

  const startTime = targetTime.format("HH:mm:ss");
  return `${startTime}`;
}
function handleDateChange(selectedDates) {
  const minDate = moment(selectedDates[0]);
  const maxDate = moment(selectedDates[0]).add(6, "days");
  configRange.value.minDate = minDate.format("DD-MM-YYYY");
  configRange.value.maxDate = maxDate.format("DD-MM-YYYY");
}
function handleFlatpickrOpen() {
  configRange.value.minDate = null;
  configRange.value.maxDate = null;
}

function defaultDateRange() {
  const startDate = moment().startOf("isoWeek").format("DD-MM-YYYY");
  const endDate = moment().endOf("isoWeek").format("DD-MM-YYYY");
  return `${startDate} to ${endDate}`;
}
async function handleExportCS() {
  let start_date;
  let end_date;
  const [startDateString, endDateString] = dateRange.value.split(" to ");
  start_date = moment(startDateString, "YYYY-MM-DD");
  end_date = moment(endDateString, "YYYY-MM-DD");
  const response = await reportTimeService.export({
    start_date: start_date?._i,
    end_date: end_date?._i,
  });
  const blod = new Blob([response], { type: "text/csv;charset=utf-8" });
  const url = URL.createObjectURL(blod);
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", "export_data.csv");
  link.click();
}

async function handleExportPDF() {
  let start_date;
  let end_date;
  const [startDateString, endDateString] = dateRange.value.split(" to ");
  start_date = moment(startDateString, "YYYY-MM-DD");
  end_date = moment(endDateString, "YYYY-MM-DD");
  const response = await reportTimeService.exportPDF({
    start_date: start_date?._i,
    end_date: end_date?._i,
  });
  const blod = new Blob([response], { type: "application/pdf" });
  const url = URL.createObjectURL(blod);
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", "export_data_time.pdf");
  link.click();
}
</script>

<template>
  <BasePageHeading
    :title="t('pages.report.titles.time_report')"
    :subtitle="t('pages.report.labels.label_head_list_time')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.report.labels.manages")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            {{ t("pages.report.titles.time_report") }}
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>
  <div class="content">
    <div class="row gap-2">
      <div class="col-12 d-flex justify-content-between">
        <FlatPickr
          class="form-control w-25"
          id="example-flatpickr-range"
          :placeholder="t('pages.report.placeholder.date_range')"
          @onChange="handleDateChange"
          @onOpen="handleFlatpickrOpen"
          v-model="dateRange"
          :config="configRange"
        />
        <div class="d-flex justify-content-end items-center gap-1">
          <e-button type="info" size="sm" @click="() => handleExportCS()">
            {{ t("pages.report.labels.btn_export") }}
          </e-button>
          <e-button type="info" size="sm" @click="() => handleExportPDF()">
            {{ t("pages.report.labels.btn_pdf") }}
          </e-button>
        </div>
      </div>
      <hr class="my-4" />
    </div>
    <BaseBlock :title="t('pages.report.titles.sale_report')">
      <Dataset :ds-data="listTimeReports">
        <div class="row">
          <div class="col container">
            <div class="table-responsive table-style">
              <table class="table table-striped mb-0">
                <thead>
                  <tr>
                    <th
                      v-for="th in cols"
                      :key="th.field"
                      :class="['sort', th.sort]"
                    >
                      {{ th.name }} <i class="gg-select float-end"></i>
                    </th>
                  </tr>
                </thead>
                <tbody style="height: 300px; overflow: auto" class="fs-sm">
                  <tr v-for="row in listTimeReports" :key="row.hour">
                    <td style="min-width: 50px">
                      {{ formatTime(row.hour) }}
                    </td>
                    <td v-if="cols.length > 1">
                      {{ row.item?.length ? row.item?.[0]?.value : 0 }}
                    </td>
                    <td v-if="cols.length > 2">
                      {{ row.item?.length ? row.item?.[1]?.value : 0 }}
                    </td>
                    <td v-if="cols.length > 3">
                      {{ row.item?.length ? row.item?.[2]?.value : 0 }}
                    </td>
                    <td v-if="cols.length > 4">
                      {{ row.item?.length ? row.item?.[3]?.value : 0 }}
                    </td>
                    <td v-if="cols.length > 5">
                      {{ row.item?.length ? row.item?.[4]?.value : 0 }}
                    </td>
                    <td v-if="cols.length > 6">
                      {{ row.item?.length ? row.item?.[5]?.value : 1 }}
                    </td>
                    <td v-if="cols.length > 7">
                      {{ row.item?.length ? row.item?.[6]?.value : 0 }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <!-- <EListEmpty v-else /> -->
      </Dataset>
    </BaseBlock>
  </div>
</template>

<style lang="scss">
@import "flatpickr/dist/flatpickr.css";
@import "@/assets/scss/vendor/flatpickr";

thead tr th {
  position: sticky;
  top: 0;
  width: 150px;
}

.table-style {
  //height: 400px;
  //overflow: auto;
}
.container {
  padding: 0;
}
</style>
