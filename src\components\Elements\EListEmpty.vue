<script setup>
import { useI18n } from "vue-i18n";
import EIcon from "@/components/Elements/EIcon.vue";
const props = defineProps({
      message: String,
    }
)
const { t } = useI18n();

</script>

<template>
  <div class="d-flex align-items-center justify-content-center text-gray py-7 flex-column">
    <e-icon name="inbox" class="fs-1 mb-2" />
    <div>{{props?.message || t('pages.placeholder.empty-list')}}</div>
  </div>
</template>

<style scoped>

</style>