import { useRouter } from "vue-router";
import Trans from "@/i18n/i18nUtils";

export default function useAppRouter() {
  const router = useRouter();
  const push = (to) => {
    return router.push(to);
  };
  const replace = (to) => {
    return router.replace(to);
  };
  const go = (to) => {
    return router.go(to);
  };
  const back = () => {
    return router.back();
  };
  const pushByName = (to) => {
    return router.push(Trans.i18nRouteByName(to));
  };
  const pushByPath = (to) => {
    return router.push(Trans.i18nRouteByPath(to));
  };
  const replaceByPath = (to) => {
    return router.replace(Trans.i18nRouteByPath(to));
  };
  const replaceByName = (to) => {
    return router.replace(Trans.i18nRouteByPath(to));
  };
  return {
    router,
    pushByName,
    pushByPath,
    replaceByP<PERSON>,
    replaceByName,
    push,
    go,
    back,
    replace,
  };
}
