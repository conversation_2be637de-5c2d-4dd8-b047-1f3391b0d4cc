<script setup>
import EIcon from '@/components/Elements/EIcon.vue'
import useAppRouter from '@/composables/useRouter'
import { tableService } from '@/services/table.service'
import { useTemplateStore } from '@/stores/template'
import { onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'

const store = useTemplateStore()

const router = useAppRouter()
const route = useRoute()
const {t} = useI18n()
const { id } = route.params

const table = ref()
const title = ref(t('pages.tables.titles.view'))

const apiGetItem = async () => {
  try {
    store.pageLoader({ mode: 'on' })
    const response = await tableService.get(id)
    table.value = response.data
    store.pageLoader({ mode: 'off' })
  } catch (error) {
    console.log(error)
    store.pageLoader({ mode: 'off' })
  }
}

onMounted(async () => {
  try {
    await apiGetItem()
  } catch (error) {
    console.error('Error fetching data:', error)
  }
})
</script>

<template>
  <BasePageHeading
    :title="title"
    :go-back="true"
    :subtitle="t('pages.tables.labels.label_head_list')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{ t('pages.tables.labels.manages') }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <router-link to="/merchant/tables">{{ t('pages.tables.titles.list') }}</router-link>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <span>{{ t('pages.tables.titles.view') }}</span>
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="container">
    <div class="row justify-content-center py-sm-4 py-md-6">
      <div class="col-sm-10 col-md-8">
        <form @submit.prevent="onSubmit">
          <BaseBlock title="View Table">
            <template #options>
              <e-icon
                @click="() => router.back()"
                name="arrow-left"
                role="button"
                class="icon_arrow_left"
              />
            </template>

            <div class="row justify-content-center py-sm-3 py-md-5">
              <div class="col-sm-10 col-md-8">
                <div class="mb-4">
                  <label class="form-label" for="form-table-name">{{ t('pages.tables.fields.table_name') }}</label>
                  <div class="text_table">
                    {{ table?.name }}
                  </div>
                </div>

                <div class="mb-4">
                  <label class="form-label" for="form-table-id">{{ t('pages.tables.fields.table_id') }}</label>
                  <div class="text_table">
                    {{ table?.table_id }}
                  </div>
                </div>

                <div class="mb-4">
                  <label class="form-label" for="form-table-id">{{ t('pages.tables.fields.store_id') }}</label>
                  <div class="text_table">
                    {{ table?.store_id }}
                  </div>
                </div>

                <div class="mb-4" :style="{ textAlign: 'end' }">
                  <button
                    class="btn btn-sm btn-primary"
                    :style="{ color: '#fff' }"
                    @click="() => router.pushByPath(`/tables/${id}/update`)"
                  >
                    {{ t('buttons.update') }}
                  </button>
                </div>
              </div>
            </div>
          </BaseBlock>
        </form>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.text_table {
  background-color: rgb(244, 244, 244);
  border: 1px solid #e9dddd;
  border-radius: 4px;
  padding: 10px;
  min-height: 48px;
  align-items: center;
  display: flex;
}

.icon_arrow_left {
  background-color: rgb(243, 235, 235);
  border-radius: 100%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
