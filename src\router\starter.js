import { createRouter, create<PERSON>eb<PERSON><PERSON><PERSON>, RouterView } from "vue-router";
import { useCookies } from "vue3-cookies";
import NProgress from "nprogress/nprogress.js";
import Tr from "@/i18n/i18nUtils";

// Main layouts
import LayoutMerchant from "@/layouts/variations/Merchant.vue";
import AuthSignIn from "@/views/merchant/Auth/AuthSignIn.vue";
import CheckAuthSignIn from "@/views/merchant/Auth/CheckAuthSignIn.vue";

import useAuth from "@/composables/useAuth";
const { resetUser, setUser, userInfo, userPlanId } = useAuth();

// Set all routes
const routes = [
  {
    path: "/:locale?",
    component: RouterView,
    beforeEnter: Tr.routeMiddleware,
    children: [
      {
        path: "",
        component: LayoutMerchant,
        children: [
          {
            path: "",
            name: "merchant-dashboard",
            component: () => import("@/views/merchant/Dashboard/Dashboard.vue"),
            meta: {
              module: "dashboard",
            },
          },
          {
            path: "toppings",
            name: "merchant-toppings-list",
            component: () =>
              import("@/views/merchant/Toppings/ToppingsList.vue"),
            meta: {
              module: "toppings",
            },
          },
          {
            path: "toppings/:id/update",
            name: "merchant-toppings-update",
            component: () =>
              import("@/views/merchant/Toppings/ToppingsForm.vue"),
            meta: {
              module: "toppings",
            },
          },
          {
            path: "toppings/create",
            name: "merchant-toppings-create",
            component: () =>
              import("@/views/merchant/Toppings/ToppingsForm.vue"),
            meta: {
              module: "toppings",
            },
          },
          {
            path: "topping-groups",
            name: "merchant-topping-groups-list",
            component: () =>
              import("@/views/merchant/ToppingGroups/ToppingGroupsList.vue"),
            meta: {
              module: "topping-groups",
            },
          },
          {
            path: "topping-groups/:id/update",
            name: "merchant-topping-groups-update",
            component: () =>
              import("@/views/merchant/ToppingGroups/ToppingGroupsForm.vue"),
            meta: {
              module: "topping-groups",
            },
          },
          {
            path: "topping-groups/create",
            name: "merchant-topping-groups-create",
            component: () =>
              import("@/views/merchant/ToppingGroups/ToppingGroupsForm.vue"),
            meta: {
              module: "topping-groups",
            },
          },
          {
            path: "topping-groups/:id",
            name: "merchant-topping-groups-view",
            component: () =>
              import("@/views/merchant/ToppingGroups/ToppingGroupView.vue"),
            meta: {
              module: "topping-groups",
            },
          },
          {
            path: "fav-topping-groups",
            name: "merchant-fav-topping-groups-list",
            component: () =>
              import(
                "@/views/merchant/FavToppingGroups/FavToppingGroupsList.vue"
              ),
            meta: {
              module: "fav-topping-groups",
            },
          },
          {
            path: "fav-topping-groups/:id/update",
            name: "merchant-fav-topping-groups-update",
            component: () =>
              import(
                "@/views/merchant/FavToppingGroups/FavToppingGroupsForm.vue"
              ),
            meta: {
              module: "topping-groups",
            },
          },
          {
            path: "fav-topping-groups/create",
            name: "merchant-fav-topping-groups-create",
            component: () =>
              import(
                "@/views/merchant/FavToppingGroups/FavToppingGroupsForm.vue"
              ),
            meta: {
              module: "fav-topping-groups",
            },
          },
          {
            path: "units",
            name: "merchant-units-list",
            component: () => import("@/views/merchant/Units/UnitsList.vue"),
            meta: {
              module: "units",
            },
          },
          {
            path: "units/:id/update",
            name: "merchant-units-update",
            component: () => import("@/views/merchant/Units/UnitsForm.vue"),
            meta: {
              module: "units",
            },
          },
          {
            path: "units/create",
            name: "merchant-units-create",
            component: () => import("@/views/merchant/Units/UnitsForm.vue"),
            meta: {
              module: "units",
            },
          },
          {
            path: "units/:id",
            name: "merchant-units-view",
            component: () => import("@/views/merchant/Units/UnitView.vue"),
            meta: {
              module: "units",
            },
          },
          {
            path: "producers",
            name: "merchant-producers-list",
            component: () =>
              import("@/views/merchant/Producers/ProducersList.vue"),
            meta: {
              module: "producers",
            },
          },
          {
            path: "producers/:id/update",
            name: "merchant-producers-update",
            component: () =>
              import("@/views/merchant/Producers/ProducersForm.vue"),
            meta: {
              module: "producers",
            },
          },
          {
            path: "producers/create",
            name: "merchant-producers-create",
            component: () =>
              import("@/views/merchant/Producers/ProducersForm.vue"),
            meta: {
              module: "producers",
            },
          },
          {
            path: "producers/:id",
            name: "merchant-producers-view",
            component: () =>
              import("@/views/merchant/Producers/ProducerView.vue"),
            meta: {
              module: "producers",
            },
          },
          {
            path: "suppliers",
            name: "merchant-suppliers-list",
            component: () =>
              import("@/views/merchant/Suppliers/SuppliersList.vue"),
            meta: {
              module: "suppliers",
            },
          },
          {
            path: "suppliers/:id/update",
            name: "merchant-suppliers-update",
            component: () =>
              import("@/views/merchant/Suppliers/SuppliersForm.vue"),
            meta: {
              module: "suppliers",
            },
          },
          {
            path: "suppliers/create",
            name: "merchant-suppliers-create",
            component: () =>
              import("@/views/merchant/Suppliers/SuppliersForm.vue"),
            meta: {
              module: "suppliers",
            },
          },
          {
            path: "suppliers/:id",
            name: "merchant-suppliers-view",
            component: () =>
              import("@/views/merchant/Suppliers/SupplierView.vue"),
            meta: {
              module: "suppliers",
            },
          },
          {
            path: "products",
            name: "merchant-products-list",
            component: () =>
              import("@/views/merchant/Products/ProductsList.vue"),
            meta: {
              module: "products",
            },
          },
          {
            path: "products/form/:id?",
            name: "merchant-products-form",
            component: () =>
              import("@/views/merchant/Products/ProductsForm.vue"),
            meta: {
              module: "products",
            },
          },
          {
            path: "categories",
            name: "merchant-categories-list",
            component: () =>
              import("@/views/merchant/Categories/CategoriesList.vue"),
            meta: {
              module: "categories",
            },
          },
          {
            path: "categories/form/:id?",
            name: "merchant-categories-form",
            component: () =>
              import("@/views/merchant/Categories/CategoriesForm.vue"),
            meta: {
              module: "categories",
            },
          },
          {
            path: "orders",
            name: "merchant-orders-list",
            component: () => import("@/views/merchant/Orders/OrdersList.vue"),
            meta: {
              module: "orders",
            },
          },
          {
            path: "orders/:id",
            name: "merchant-orders-detail",
            component: () => import("@/views/merchant/Orders/OrdersDetail.vue"),
            meta: {
              module: "orders",
            },
          },
          {
            path: "tables",
            name: "merchant-tables-list",
            component: () => import("@/views/merchant/Tables/TablesList.vue"),
            meta: {
              module: "tables",
              checkByPlan: true,
            },
          },
          {
            path: "tables/:id",
            name: "merchant-tables-view",
            component: () => import("@/views/merchant/Tables/TableView.vue"),
            meta: {
              module: "tables",
              checkByPlan: true,
            },
          },
          {
            path: "tables/:id/update",
            name: "merchant-tables-update",
            component: () => import("@/views/merchant/Tables/TablesForm.vue"),
            meta: {
              module: "tables",
              checkByPlan: true,
            },
          },
          {
            path: "tables/create",
            name: "merchant-tables-create",
            component: () => import("@/views/merchant/Tables/TablesForm.vue"),
            meta: {
              module: "tables",
              checkByPlan: true,
            },
          },
          {
            path: "kiosks",
            name: "merchant-kiosks-list",
            component: () => import("@/views/merchant/Kiosks/KiosksList.vue"),
            meta: {
              module: "kiosks",
              checkByPlan: true,
            },
          },
          {
            path: "kiosks/:id",
            name: "merchant-kiosks-view",
            component: () => import("@/views/merchant/Kiosks/KioskView.vue"),
            meta: {
              module: "kiosks",
              checkByPlan: true,
            },
          },
          {
            path: "kiosks/:id/update",
            name: "merchant-kiosks-update",
            component: () => import("@/views/merchant/Kiosks/KiosksForm.vue"),
            meta: {
              module: "kiosks",
              checkByPlan: true,
            },
          },
          {
            path: "kiosks/create",
            name: "merchant-kiosks-create",
            component: () => import("@/views/merchant/Kiosks/KiosksForm.vue"),
            meta: {
              module: "kiosks",
              checkByPlan: true,
            },
          },
          {
            path: "customers",
            name: "merchant-customers-list",
            component: () =>
              import("@/views/merchant/Customers/CustomersList.vue"),
            meta: {
              module: "customers",
            },
          },
          {
            path: "printers",
            name: "merchant-printers-list",
            component: () =>
              import("@/views/merchant/Printers/PrintersList.vue"),
            meta: {
              module: "printers",
            },
          },
          {
            path: "printers/:id",
            name: "merchant-printers-view",
            component: () =>
              import("@/views/merchant/Printers/PrinterView.vue"),
            meta: {
              module: "printers",
            },
          },
          {
            path: "printers/:id/update",
            name: "merchant-printers-update",
            component: () =>
              import("@/views/merchant/Printers/PrintersForm.vue"),
            meta: {
              module: "printers",
            },
          },
          {
            path: "printers/create",
            name: "merchant-printers-create",
            component: () =>
              import("@/views/merchant/Printers/PrintersForm.vue"),
            meta: {
              module: "printers",
            },
          },
          {
            path: "roles",
            name: "merchant-roles-list",
            component: () => import("@/views/merchant/Roles/RolesList.vue"),
            meta: {
              module: "roles",
            },
          },
          {
            path: "roles/form/:id?",
            name: "merchant-roles-form",
            component: () => import("@/views/merchant/Roles/RolesForm.vue"),
            meta: {
              module: "roles",
            },
          },
          {
            path: "users",
            name: "merchant-users-list",
            component: () => import("@/views/merchant/Users/<USER>"),
            meta: {
              module: "users",
            },
          },
          {
            path: "users/:id",
            name: "merchant-users-view",
            component: () => import("@/views/merchant/Users/<USER>"),
            meta: {
              module: "users",
            },
          },
          {
            path: "users/:id/update",
            name: "merchant-users-update",
            component: () => import("@/views/merchant/Users/<USER>"),
            meta: {
              module: "users",
            },
          },
          {
            path: "users/create",
            name: "merchant-users-create",
            component: () => import("@/views/merchant/Users/<USER>"),
            meta: {
              module: "users",
            },
          },
          {
            path: "printer-logs",
            name: "merchant-printer-logs-list",
            component: () =>
              import("@/views/merchant/PrinterLogs/PrinterLogsList.vue"),
            meta: {
              module: "printer-logs",
            },
          },
          {
            path: "transactions",
            name: "merchant-transactions-list",
            component: () =>
              import("@/views/merchant/Transactions/TransactionsList.vue"),
            meta: {
              module: "transactions",
            },
          },
          {
            path: "daily-summary",
            name: "merchant-daily-summary-list",
            component: () =>
              import("@/views/merchant/DailySummary/DailySummaryList.vue"),
            meta: {
              module: "daily-summary",
            },
          },
          {
            path: "openings-hours",
            name: "merchant-opening-hours-list",
            component: () =>
              import("@/views/merchant/OpeningHours/OpeningHoursList.vue"),
            meta: {
              module: "openings-hours",
            },
          },
          {
            path: "report/order",
            name: "merchant-report-order",
            component: () => import("@/views/merchant/Reports/ReportOrder.vue"),
            meta: {
              module: "report",
            },
          },
          {
            path: "report/sales",
            name: "merchant-report-sales",
            component: () => import("@/views/merchant/Reports/ReportSales.vue"),
            meta: {
              module: "report",
            },
          },
          {
            path: "report/goods",
            name: "merchant-report-goods",
            component: () => import("@/views/merchant/Reports/ReportGoods.vue"),
            meta: {
              module: "report",
            },
          },
          {
            path: "report/time",
            name: "merchant-report-time",
            component: () => import("@/views/merchant/Reports/ReportTime.vue"),
            meta: {
              module: "report",
            },
          },
          {
            path: "report/sms",
            name: "merchant-report-sms",
            component: () => import("@/views/merchant/Reports/ReportSms.vue"),
            meta: {
              module: "report",
            },
          },
          {
            path: "report/tips",
            name: "merchant-report-tip",
            component: () => import("@/views/merchant/Reports/ReportTips.vue"),
            meta: {
              module: "report",
            },
          },
        ],
      },
      {
        path: "auth/check/sign-in",
        name: "merchant-auth-check-signin",
        component: CheckAuthSignIn,
      },
      {
        path: "auth/sign-in",
        name: "merchant-auth-signin",
        component: AuthSignIn,
      },
    ],
  },
];

// Create Router
const router = createRouter({
  history: createWebHistory("/"),
  linkActiveClass: "active",
  linkExactActiveClass: "active",
  scrollBehavior() {
    return { left: 0, top: 0 };
  },
  routes,
});

// NProgress
/*eslint-disable no-unused-vars*/
NProgress.configure({ showSpinner: false });

router.afterEach(() => {
  NProgress.done();
});

const PageNoAuth = ["merchant-auth-signin", "merchant-auth-check-signin"];

const { cookies } = useCookies();
router.beforeEach(async (to, from, next) => {
  NProgress.start();
  const toPage = to.name;

  const cookieUserInfo = JSON.parse(localStorage.getItem("user_merchant"));
  const cookieToken = cookies.get("token_merchant");
  if (!PageNoAuth.includes(toPage)) {
    if (toPage?.includes("merchant-auth-check-signin")) {
      cookies.remove("token_merchant");
      localStorage.removeItem("user_merchant");
      cookies.remove("isMultiTenant");
      cookies.remove("token_multi_tenant");
      cookies.remove("current_store");
      localStorage.removeItem("arrayStores");
      resetUser();
      return next(Tr.i18nRoute({ name: "merchant-auth-check-signin" }));
    } else if (!cookieUserInfo || !cookieToken) {
      console.log("toPage1", cookieUserInfo);
      cookies.remove("token_merchant");
      localStorage.removeItem("user_merchant");
      cookies.remove("isMultiTenant");
      cookies.remove("current_store");
      cookies.remove("token_multi_tenant");
      localStorage.removeItem("arrayStores");
      resetUser();
      return next(Tr.i18nRoute({ name: "merchant-auth-signin" }));
    } else {
      setUser(cookieUserInfo);
      if (to.matched.some((record) => record.meta.checkByPlan)) {
        if (
          (to.meta.module === "tables" &&
            !userInfo?.value?.plans?.find(
              (plan) => plan?.id === userPlanId.TABLE
            )?.active) ||
          (to.meta.module === "kiosks" &&
            !userInfo?.value?.plans?.find(
              (plan) => plan?.id === userPlanId.KIOSK
            )?.active)
        ) {
          return next(Tr.i18nRoute({ name: "merchant-dashboard" }));
        }
        return next();
      }
      return next();
    }
  } else {
    return next();
  }
});

/*eslint-enable no-unused-vars*/

export default router;
