import { http } from './Base/base.service'

export const toppingGroupService = {
  async getList(query) {
    return await http.get('/topping_groups', {
      params: query
    })
  },

  async get(id) {
    return await http.get('/topping_groups/' + id)
  },

  async create(data) {
    return await http.post('/topping_groups', data)
  },

  async update(id, data) {
    return await http.patch('/topping_groups/' + id, data)
  },

  async delete(id) {
    return await http.delete('/topping_groups/' + id)
  }
}
