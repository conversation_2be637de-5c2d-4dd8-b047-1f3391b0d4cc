import { http } from "./Base/base.service";

export const productService = {
  async getList(query) {
    return await http.get("/products", {
      params: query,
    });
  },
  async getDetail(id) {
    return await http.get(`/products/${id}`);
  },
  async create(data) {
    return await http.post("/products", data, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  async update(id, data) {
    return await http.post("/products/" + id, data, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  async updateFavor(data) {
    return await http.post("/global_customs/attach", data, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  async patch(id, data) {
    return await http.patch("/products/" + id, data);
  },

  async getListConstituents(query) {
    return await http.get("/constituents/search", {
      params: query,
    });
  },
  async updatePriority(payload) {
    return await http.patch(`/products/priority`, payload);
  },
  async suggest(id, val) {
    return await http.patch(`/products/toogle_suggest/${id}`, val);
  },

  async delete(id) {
    return await http.delete("/products/" + id);
  },

  async getListFavor(query) {
    return await http.get("/global_customs", {
      params: query,
    });
  },

  async updateStock(id, data) {
    return await http.post(`/products/toogleStock/${id}`, data);
  },
};
