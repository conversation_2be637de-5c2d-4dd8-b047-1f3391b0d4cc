<script setup>
import { reactive, onMounted, ref, watch } from "vue";
import { Dataset, DatasetItem, DatasetInfo, DatasetShow } from "vue-dataset";
import { useDebounceFn } from "@vueuse/core";
import { dailySummaryService } from "@/services/dailySummary.service";
import EListEmpty from "@/components/Elements/EListEmpty.vue";
import { useTemplateStore } from "@/stores/template";
import { useI18n } from "vue-i18n";
import { storeData } from "@/stores/storeData";
import moment from "moment";
import FlatPickr from "vue-flatpickr-component";
import EButton from "@/components/Elements/EButton.vue";

const currentPage = ref(1);
const totalPage = ref(1);
const limit = ref(10);
const total = ref();
const search = ref();
const store = useTemplateStore();
const sumTotal = ref();
const dataFetch = storeData();
const { t, locale } = useI18n();
const handleChange = async (val) => {
  if (val?.length === 2) {
    await onFetchList();
  }
};
// Helper variables
const cols = reactive([
  {
    name: t("pages.daily_summary.fields.date"),
    field: "date",
  },
  {
    name: t("pages.daily_summary.fields.payment_method"),
    field: "payment_method",
  },
  {
    name: t("pages.daily_summary.fields.total_order"),
    field: "total_order",
  },
  {
    name: t("pages.daily_summary.fields.no_of_items"),
    field: "no_of_items",
  },
  {
    name: t("pages.daily_summary.fields.net_sales"),
    field: "net_sales",
  },
  {
    name: t("pages.daily_summary.fields.vat_15%"),
    field: "vat_15%",
  },
  {
    name: t("pages.daily_summary.fields.vat_25%"),
    field: "vat_25%",
  },
  {
    name: t("pages.daily_summary.fields.sales_vat"),
    field: "sales_vat",
  },
]);
const updateCols = () => {
  cols[0].name = t("pages.daily_summary.fields.date");
  cols[1].name = t("pages.daily_summary.fields.payment_method");
  cols[2].name = t("pages.daily_summary.fields.total_order");
  cols[3].name = t("pages.daily_summary.fields.no_of_items");
  cols[4].name = t("pages.daily_summary.fields.net_sales");
  cols[5].name = t("pages.daily_summary.fields.vat_15%");
  cols[6].name = t("pages.daily_summary.fields.vat_25%");
  cols[6].name = t("pages.daily_summary.fields.sales_vat");
};

// Watch for language changes
watch(locale, () => {
  updateCols();
});

const listDailySummary = ref([]);

const onFetchList = async () => {
  try {
    let start_date;
    let end_date;
    const dates = [];
    const [startDateString, endDateString] = dateRange.value.split(" to ");
    start_date = moment(startDateString, "DD-MM-YYYY").toISOString();
    end_date = endDateString
      ? moment(endDateString, "DD-MM-YYYY").endOf("day").toISOString()
      : moment(startDateString, "DD-MM-YYYY").endOf("day").toISOString();
    let currentDate = moment(startDateString, "DD-MM-YYYY").clone();
    while (
      currentDate.isSameOrBefore(
        moment(endDateString || startDateString, "DD-MM-YYYY"),
        "day"
      )
    ) {
      dates.push(currentDate.format("DD-MM-YYYY"));
      currentDate.add(1, "day");
    }

    store.pageLoader({ mode: "on" });
    const response = await dailySummaryService.getList({
      limit: limit.value,
      search: search?.value,
      page: currentPage.value,
      start_date: start_date,
      end_date: end_date,
    });

    if (!response?.error) {
      total.value = response.data.total;
      listDailySummary.value = response.data.data || [];
      totalPage.value = response.data.last_page;
      sumTotal.value = response.options;
    }
    store.pageLoader({ mode: "off" });
  } catch (error) {
    console.log(error);
    store.pageLoader({ mode: "off" });
  }
};
const handleChangeSearch = useDebounceFn((e) => {
  search.value = e?.target?.value;
  onFetchList();
}, 500);
const visible = ref(true);
watch(limit, async () => {
  currentPage.value = 1;
  await onFetchList();
});

const dateRange = ref(defaultDateRange());
const handleClose = async (val) => {
  if (val?.length < 2) {
    const startDate = moment().startOf("isoWeek").format("DD-MM-YYYY");
    const endDate = moment().endOf("isoWeek").format("DD-MM-YYYY");
    dateRange.value = `${startDate} to ${endDate}`;
    await onFetchList();
    dateRange.value = [startDate, endDate];
  }
};
const configRange = ref({
  mode: "range",
  defaultDate: [
    moment().startOf("isoWeek").format("YYYY-MM-DD"),
    moment().endOf("isoWeek").format("YYYY-MM-DD"),
  ],
  dateFormat: "d-m-Y",
  onChange: handleChange,
  locale: { firstDayOfWeek: 1 },
  minDate: null,
  maxDate: null,
  onClose: handleClose,
});

function defaultDateRange() {
  const startDate = moment().startOf("isoWeek").format("DD-MM-YYYY");
  const endDate = moment().endOf("isoWeek").format("DD-MM-YYYY");
  return `${startDate} to ${endDate}`;
}

async function handleExportCS() {
  let start_date;
  let end_date;
  const [startDateString, endDateString] = dateRange.value.split(" to ");
  start_date = moment(startDateString, "DD-MM-YYYY");
  end_date = endDateString ? moment(endDateString, "DD-MM-YYYY") : start_date;
  const response = await dailySummaryService.export({
    start_date: start_date.format("YYYY-MM-DD"),
    end_date: end_date.format("YYYY-MM-DD"),
  });
  const blod = new Blob([response], { type: "text/csv;charset=utf-8" });
  const url = URL.createObjectURL(blod);
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", "export_data.csv");
  link.click();
}

async function handleExportPDF() {
  let start_date;
  let end_date;
  const [startDateString, endDateString] = dateRange.value.split(" to ");
  start_date = moment(startDateString, "DD-MM-YYYY");
  end_date = endDateString ? moment(endDateString, "DD-MM-YYYY") : start_date;
  const response = await dailySummaryService.exportPDF({
    start_date: start_date.format("YYYY-MM-DD"),
    end_date: end_date.format("YYYY-MM-DD"),
  });
  const blod = new Blob([response], { type: "application/pdf" });
  const url = URL.createObjectURL(blod);
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", "export_data_daily_summary.pdf");
  link.click();
}

// Apply a few Bootstrap 5 optimizations
onMounted(async () => {
  if (dataFetch.storeData?.["merchant-daily-summary-list"]?.length > 0) {
    const newTables = dataFetch.storeData?.["merchant-daily-summary-list"].map(
      (item) => ({
        ...item,
      })
    );
    await (listDailySummary.value = newTables);
    total.value = dataFetch.total?.["merchant-daily-summary-list"];
    sumTotal.value = dataFetch?.sumDailySumary?.["merchant-daily-summary-list"];
    store.pageLoader({ mode: "off" });
  } else {
    await onFetchList();
  }

  // Remove labels from
  document.querySelectorAll("#datasetLength label").forEach((el) => {
    el.remove();
  });

  // Replace select classes
  let selectLength = document.querySelector("#datasetLength select");

  if (selectLength) {
    selectLength.classList = "";
    selectLength.classList.add("form-select");
    selectLength.style.width = "80px";
  }
});
</script>

<template>
  <BasePageHeading
    :title="t('pages.daily_summary.name')"
    :subtitle="t('pages.daily_summary.label.label_head_list')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.daily_summary.label.manage")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            {{ t("pages.daily_summary.titles.list") }}
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <div class="row gap-2">
      <div class="col-12 d-flex justify-content-between">
        <FlatPickr
          class="form-control w-25"
          id="example-flatpickr-range"
          :placeholder="t('pages.report.placeholder.date_range')"
          v-model="dateRange"
          :config="configRange"
        />
        <div class="d-flex justify-content-end items-center gap-1">
          <e-button type="info" size="sm" @click="() => handleExportCS()">
            {{ t("pages.report.labels.btn_export") }}
          </e-button>
          <e-button type="info" size="sm" @click="() => handleExportPDF()">
            {{ t("pages.report.labels.btn_pdf") }}
          </e-button>
        </div>
      </div>
      <hr class="my-4" />
    </div>
    <BaseBlock :title="t('pages.daily_summary.name')">
      <Dataset v-slot="{ ds }" :ds-data="listDailySummary">
        <div class="row" :data-page-count="ds.dsPagecount">
          <div id="datasetLength" class="col-md-8 py-2">
            <DatasetShow v-show="false" :dsShowEntries="100" />
            <div class="form-inline">
              <select class="form-select" style="width: 80px" v-model="limit">
                <option :value="5">5</option>
                <option :value="10">10</option>
                <option :value="25">25</option>
                <option :value="50">50</option>
                <option :value="100">100</option>
              </select>
            </div>
          </div>
          <!-- <div class="col-md-4 py-2">
            <input
              type="text"
              class="form-control"
              id="form-name"
              :placeholder="t('pages.placeholder.enter')"
              @input="handleChangeSearch"
            />
          </div> -->
        </div>
        <hr />
        <div class="row" v-if="listDailySummary?.length">
          <div class="col-md-12">
            <div class="table-responsive">
              <table class="table mb-0">
                <thead>
                  <tr>
                    <th v-for="th in cols" :key="th.field">
                      {{ th.name }}
                    </th>
                  </tr>
                </thead>
                <DatasetItem tag="tbody" class="fs-sm">
                  <template #default="{ row }">
                    <tr>
                      <td
                        :style="{
                          minWidth: '100px',
                        }"
                      >
                        {{ row.day }}
                      </td>
                      <td
                        :style="{
                          minWidth: '100px',
                        }"
                      >
                        {{ row.payment_gateway }}
                      </td>
                      <td
                        :style="{
                          minWidth: '80px',
                        }"
                      >
                        {{ row.order_total }}
                      </td>
                      <td
                        :style="{
                          minWidth: '150px',
                        }"
                      >
                        {{ row.item_total }}
                      </td>

                      <td
                        :style="{
                          minWidth: '150px',
                        }"
                      >
                        {{ row.amount_without_vat }}
                      </td>

                      <td>
                        {{ row?.vat15 }}
                      </td>
                      <td
                        :style="{
                          minWidth: '120px',
                        }"
                      >
                        {{ row?.vat25 }}
                      </td>
                      <td
                        :style="{
                          minWidth: '120px',
                        }"
                      >
                        {{ row.amount_total }}
                      </td>
                    </tr>
                  </template>
                </DatasetItem>
                <tfoot>
                  <tr>
                    <td>{{ t("pages.report.fields.total") }}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td>{{ sumTotal?.sum?.amount_without_vat }}</td>
                    <td>{{ sumTotal?.sum?.vat15 }}</td>
                    <td>{{ sumTotal?.sum?.vat25 }}</td>
                    <td>{{ sumTotal?.sum?.amount_total }}</td>
                  </tr>
                  <tr
                    v-if="
                      sumTotal?.sum?.order_total?.vipps &&
                      sumTotal?.sum?.item_total?.vipps
                    "
                  >
                    <td>{{ t("pages.daily_summary.fields.total_vips") }}</td>
                    <td></td>
                    <td>{{ sumTotal?.sum?.order_total?.vipps }}</td>
                    <td>{{ sumTotal?.sum?.item_total?.vipps }}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr
                    v-if="
                      sumTotal?.sum?.order_total?.cash &&
                      sumTotal?.sum?.item_total?.cash
                    "
                  >
                    <td>{{ t("pages.daily_summary.fields.total_cash") }}</td>
                    <td></td>
                    <td>{{ sumTotal?.sum?.order_total?.cash }}</td>
                    <td>{{ sumTotal?.sum?.item_total?.cash }}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr
                    v-if="
                      sumTotal?.sum?.order_total?.stripe &&
                      sumTotal?.sum?.item_total?.stripe
                    "
                  >
                    <td>{{ t("pages.daily_summary.fields.total_stripe") }}</td>
                    <td></td>
                    <td>{{ sumTotal?.sum?.order_total?.stripe }}</td>
                    <td>{{ sumTotal?.sum?.item_total?.stripe }}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr
                    v-if="
                      sumTotal?.sum?.order_total?.terminal &&
                      sumTotal?.sum?.item_total?.terminal
                    "
                  >
                    <td>
                      {{ t("pages.daily_summary.fields.total_terminal") }}
                    </td>
                    <td></td>
                    <td>{{ sumTotal?.sum?.order_total?.terminal }}</td>
                    <td>{{ sumTotal?.sum?.item_total?.terminal }}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        </div>
        <EListEmpty v-else />
        <div
          class="d-flex flex-md-row flex-column justify-content-between align-items-center"
        >
          <DatasetInfo class="py-3 fs-sm" />
          <el-pagination
            v-if="visible"
            v-model:current-page="currentPage"
            @current-change="onFetchList"
            background
            v-model:page-size="limit"
            layout="prev, pager, next"
            :prev-text="t('pages.footer.previous')"
            :next-text="t('pages.footer.next')"
            :total="total"
          />
        </div>
      </Dataset>
    </BaseBlock>
  </div>
</template>

<style lang="scss">
@import "flatpickr/dist/flatpickr.css";
@import "@/assets/scss/vendor/flatpickr";
</style>
<style lang="scss" scoped>
tfoot > tr > td {
  font-weight: bold;
}
.gg-select {
  box-sizing: border-box;
  position: relative;
  display: block;
  transform: scale(1);
  width: 22px;
  height: 22px;
}
.gg-select::after,
.gg-select::before {
  content: "";
  display: block;
  box-sizing: border-box;
  position: absolute;
  width: 8px;
  height: 8px;
  left: 7px;
  transform: rotate(-45deg);
}
.gg-select::before {
  border-left: 2px solid;
  border-bottom: 2px solid;
  bottom: 4px;
  opacity: 0.3;
}
.gg-select::after {
  border-right: 2px solid;
  border-top: 2px solid;
  top: 4px;
  opacity: 0.3;
}
th.sort {
  cursor: pointer;
  user-select: none;
  &.asc {
    .gg-select::after {
      opacity: 1;
    }
  }
  &.desc {
    .gg-select::before {
      opacity: 1;
    }
  }
}
</style>
