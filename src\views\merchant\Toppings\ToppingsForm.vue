<script setup>
import EIcon from "@/components/Elements/EIcon.vue";
import { useRoute } from "vue-router";
import { computed, ref, onMounted, watch, onBeforeUnmount } from "vue";
import useVuelidate from "@vuelidate/core";
import { required, minLength } from "@vuelidate/validators";
import { toppingService } from "@/services/topping.service";
import { useTemplateStore } from "@/stores/template";
import { toppingGroupService } from "@/services/toppingGroup.service";
import { unitService } from "@/services/unit.service";
import { supplierService } from "@/services/supplier.service";
import { producerService } from "@/services/producer.service";
import useNotify from "@/composables/useNotify";
import useAppRouter from "@/composables/useRouter";
import { useI18n } from "vue-i18n";
import Dropzone from "dropzone";
import { scrollTo } from "@/stores/scollItemInlist";
import { storeData } from "@/stores/storeData";

const store = useTemplateStore();
const dataFetch = storeData();
const { setNotify } = useNotify();
const route = useRoute();
const { t } = useI18n();
const router = useAppRouter();
const dropzone = ref(null);
const props = route.params;
const id = props?.id;
const typeSubmit = ref();
const topping = ref();
const scrollStore = scrollTo();

const optionsSupplier = ref([]);
const optionsProducer = ref([]);
const optionsUnit = ref([]);
const optionsTopingGroup = ref([]);

const onFetchListToppingGroup = async () => {
  const response = await toppingGroupService.getList({
    limit: -1,
  });
  if (!response?.error) {
    const newArray = response.data?.data.map((item) => ({
      value: item.id,
      text: item.name,
    }));
    optionsTopingGroup.value = [
      { value: null, text: "Please select" },
      ...newArray,
    ];
  }
};

const onFetchListUnit = async () => {
  const response = await unitService.getList();
  if (!response?.error) {
    const newArray = response.data?.data.map((item) => ({
      value: item.id,
      text: item.name,
    }));
    optionsUnit.value = [
      { value: null, text: t("common.select_default") },
      ...newArray,
    ];
  }
};

const onFetchListProducer = async () => {
  const response = await producerService.getList();
  if (!response?.error) {
    const newArray = response.data?.data.map((item) => ({
      value: item.id,
      text: item.name,
    }));
    optionsProducer.value = [
      { value: null, text: t("common.select_default") },
      ...newArray,
    ];
  }
};

const onFetchListSupplier = async () => {
  const response = await supplierService.getList();
  if (!response?.error) {
    const newArray = response.data?.data.map((item) => ({
      value: item.id,
      text: item.name,
    }));
    optionsSupplier.value = [
      { value: null, text: t("common.select_default") },
      ...newArray,
    ];
  }
};
const initialState = {
  name: null,
  topping_group_id: null,
  unit_id: null,
  in_stock: true,
  in_kiosk: true,
  in_table: true,
  in_takeaway: true,
  supplier_id: null,
  producer_id: null,
  price: 0,
  cost_price: 0,
  description: null,
  vat_rate: 25,
  purchase_price: 0, // = price
  ordinary_profit: 0,
  ordinary_price_ex_vat: 0,
  ordinary_price: 0, // = cost_price
  offer_profit: 0,
  offer_price_ex_vat: null,
  offer_price: null,
  active_offer_price: false,
  is_takeaway_price: false,
  takeaway_purchase_price: null,
  takeaway_vat_rate: 15,
  takeaway_ordinary_profit: null,
  takeaway_offer_profit: null,
  takeaway_active_offer_price: false,
  takeaway_ordinary_price: null,
  takeaway_price_ex_vat: null,
  takeaway_offer_price: null,
  takeaway_offer_ex_vat: null,
  thumbnail: null,
};

let state = ref({ ...initialState });

function resetState() {
  state.value = { ...initialState };
  dropzone.value.destroy();
  v$.value.$reset();
}
const mockFile = ref(null);

const rules = computed(() => {
  const allRules = {
    name: { required, minLength: minLength(3) },
    topping_group_id: { required },
    in_stock: {},
    in_kiosk: {},
    in_table: {},
    in_takeaway: {},
    vat_rate: { required },
    ordinary_profit: {},
    takeaway_ordinary_profit: {},
  };

  if (state.value.is_takeaway_price) {
    return allRules;
  } else {
    let _tmp = Object.assign({}, allRules);
    delete _tmp.takeaway_ordinary_profit;
    return _tmp;
  }
});

let v$ = useVuelidate(rules, state.value);

async function onSubmit() {
  try {
    const result = await v$.value.$validate();
    if (!result) return;
    store.pageLoader({ mode: "on" });
    const payload = new FormData();
    const data = {
      ...state.value,
      in_stock: state.value.in_stock ? 1 : 0,
      in_kiosk: state.value.in_kiosk ? 1 : 0,
      price: state.value.purchase_price,
      cost_price: state.value.ordinary_price || 0,
      offer_price: state.value.offer_price || 0,
      takeaway_ordinary_price: state.value.takeaway_ordinary_price
        ? state.value.takeaway_ordinary_price
        : 0,
      takeaway_offer_price: state.value.takeaway_offer_price
        ? state.value.takeaway_offer_price
        : 0,
      ordinary_price_ex_vat: state.value.ordinary_price_ex_vat
        ? Number(state.value.ordinary_price_ex_vat)
          ? Number(state.value.ordinary_price_ex_vat)
          : 0
        : 0,
      ordinary_profit: state.value.ordinary_profit
        ? Number(state.value.ordinary_profit)
          ? Number(state.value.ordinary_profit)
          : 0
        : 0,
      purchase_price: state.value.purchase_price
        ? Number(state.value.purchase_price)
          ? Number(state.value.purchase_price)
          : 0
        : 0,
      takeaway_ordinary_profit: state.value.takeaway_ordinary_profit
        ? Number(state.value.takeaway_ordinary_profit)
          ? Number(state.value.takeaway_ordinary_profit)
          : 0
        : 0,
      takeaway_price_ex_vat: state.value.takeaway_price_ex_vat
        ? Number(state.value.takeaway_price_ex_vat)
          ? Number(state.value.takeaway_price_ex_vat)
          : 0
        : 0,
      offer_price_ex_vat: state.value.offer_price_ex_vat
        ? Number(state.value.offer_price_ex_vat)
          ? Number(state.value.offer_price_ex_vat)
          : 0
        : 0,
      takeaway_offer_ex_vat: state.value.takeaway_offer_ex_vat
        ? Number(state.value.takeaway_offer_ex_vat)
          ? Number(state.value.takeaway_offer_ex_vat)
          : 0
        : 0,
      takeaway_offer_profit: state.value.takeaway_offer_profit
        ? Number(state.value.takeaway_offer_profit)
          ? Number(state.value.takeaway_offer_profit)
          : 0
        : 0,
      offer_profit: state.value.offer_profit
        ? Number(state.value.offer_profit)
          ? Number(state.value.offer_profit)
          : 0
        : 0,
    };

    if (!data.is_takeaway_price) {
      delete data.takeaway_purchase_price;
      delete data.takeaway_ordinary_profit;
      delete data.takeaway_offer_profit;
      delete data.takeaway_active_offer_price;
      delete data.takeaway_ordinary_price;
      delete data.takeaway_price_ex_vat;
      delete data.takeaway_offer_price;
      delete data.takeaway_offer_ex_vat;
    } else {
      payload.append(
        "takeaway_purchase_price",
        state.value.takeaway_purchase_price || 0
      );
    }
    payload.append("price", state.value.purchase_price || 0);

    const propertiesToCheck = [
      "price",
      "takeaway_purchase_price",
      "supplier_id",
      "producer_id",
    ];
    propertiesToCheck.forEach((prop) => {
      if (!data[prop]) delete data[prop];
    });
    for (let key in data) {
      const value = data[key];
      let valueAppend = null;
      if (value === true) {
        valueAppend = 1;
      } else if (value === false) {
        valueAppend = 0;
      } else if (value === null) {
        valueAppend = "";
      } else {
        valueAppend = value;
      }
      payload.append(key, valueAppend);
    }
    let response;
    if (id) {
      payload.append("_method", "PATCH");
      response = await toppingService.update(id, payload);
    } else {
      response = await toppingService.create(payload);
    }
    if (!response?.error) {
      if (typeSubmit.value === "confirm") {
        scrollStore.getElement(id);
        dataFetch.setData([], "merchant-toppings-list");
        dataFetch.setTotal(0, "merchant-toppings-list");
        await router.pushByName({ name: "merchant-toppings-list" });
      } else {
        resetState();
      }
      setNotify({
        title: "Success",
        message: id ? "Topping Update Success" : "Topping Create success",
        type: "success",
      });
      store.pageLoader({ mode: "off" });
    }
  } catch (e) {
    console.log(e);
    store.pageLoader({ mode: "off" });
    setNotify({
      title: "Error",
      message: e?.message,
    });
  }
}

const apiGetTopping = async () => {
  const res = await toppingService.get(id);
  topping.value = res.data;
  state.value = {
    name: res.data.name,
    topping_group_id: res.data.topping_group_id,
    unit_id: res.data.unit_id,
    in_stock: res.data.in_stock,
    in_kiosk: res.data.in_kiosk,
    in_table: res.data.in_table,
    in_takeaway: res.data.in_takeaway,
    supplier_id: res.data.supplier_id,
    producer_id: res.data.producer_id,
    thumbnail: res.data.thumbnail || null,
    price: res.data.price ? Number(res.data.price) : null,
    vat_rate: res.data.vat_rate
      ? Number(res.data.vat_rate)
        ? Number(res.data.vat_rate)
        : 25
      : 25,
    description: res.data.description,
    purchase_price: res.data.price
      ? Number(res.data.price)
        ? Number(res.data.price)
        : 0
      : 0, // = price
    ordinary_profit: res.data.ordinary_profit
      ? Number(res.data.ordinary_profit)
        ? Number(res.data.ordinary_profit)
        : null
      : null,
    ordinary_price_ex_vat: res.data.ordinary_price_ex_vat
      ? Number(res.data.ordinary_price_ex_vat)
        ? Number(res.data.ordinary_price_ex_vat)
        : null
      : null,
    ordinary_price: res.data.cost_price
      ? Number(res.data.cost_price)
        ? Number(res.data.cost_price)
        : null
      : null, // = cost_price
    offer_profit: res.data.offer_profit
      ? Number(res.data.offer_profit)
        ? Number(res.data.offer_profit)
        : null
      : null,
    offer_price_ex_vat: res.data.offer_price_ex_vat
      ? Number(res.data.offer_price_ex_vat)
        ? Number(res.data.offer_price_ex_vat)
        : null
      : null,
    offer_price: res.data.offer_price
      ? Number(res.data.offer_price)
        ? Number(res.data.offer_price)
        : null
      : null,
    active_offer_price: res.data.active_offer_price,
    is_takeaway_price: res.data.is_takeaway_price,
    takeaway_purchase_price: res.data.takeaway_purchase_price
      ? Number(res.data.takeaway_purchase_price)
        ? Number(res.data.takeaway_purchase_price)
        : 0
      : 0,
    takeaway_vat_rate: res.data.takeaway_vat_rate
      ? Number(res.data.takeaway_vat_rate)
        ? Number(res.data.takeaway_vat_rate)
        : 15
      : 15,
    takeaway_ordinary_profit: res.data.takeaway_ordinary_profit
      ? Number(res.data.takeaway_ordinary_profit)
        ? Number(res.data.takeaway_ordinary_profit)
        : null
      : null,
    takeaway_offer_profit: res.data.takeaway_offer_profit
      ? Number(res.data.takeaway_offer_profit)
        ? Number(res.data.takeaway_offer_profit)
        : null
      : null,
    takeaway_active_offer_price: res.data.takeaway_active_offer_price,
    takeaway_ordinary_price: res.data.takeaway_ordinary_price
      ? Number(res.data.takeaway_ordinary_price)
        ? Number(res.data.takeaway_ordinary_price)
        : null
      : null,
    takeaway_price_ex_vat: res.data.takeaway_price_ex_vat
      ? Number(res.data.takeaway_price_ex_vat)
        ? Number(res.data.takeaway_price_ex_vat)
        : null
      : null,
    takeaway_offer_price: res.data.takeaway_offer_price
      ? Number(res.data.takeaway_offer_price)
        ? Number(res.data.takeaway_offer_price)
        : null
      : null,
    takeaway_offer_ex_vat: res.data.takeaway_offer_ex_vat
      ? Number(res.data.takeaway_offer_ex_vat)
        ? Number(res.data.takeaway_offer_ex_vat)
        : null
      : null,
  };

  v$ = useVuelidate(rules, state.value);
};

// const roundToTwoDecimalPlaces = (number) => {
//   if (Number.isFinite(number) && !Number.isInteger(number)) {
//     return Number(number.toFixed(2));
//   } else {
//     return number;
//   }
// }

watch(
  () => state.value.price,
  (val) => {
    console.log(val);
  },
  { immediate: true, deep: true }
);

// const imageUrl = ref();
// const handleFileChange = (event) => {
//   const file = event.target.files[0];

//   if (file) {
//     imageUrl.value = URL.createObjectURL(file);
//   } else imageUrl.value = null;
// };

onMounted(async () => {
  try {
    store.pageLoader({ mode: "on" });
    if (id) await apiGetTopping();
    // await onFetchListToppingGroup()
    // await onFetchListUnit()
    // await onFetchListProducer()
    // await onFetchListSupplier()
    await Promise.all([
      onFetchListToppingGroup(),
      onFetchListUnit(),
      onFetchListProducer(),
      onFetchListSupplier(),
    ]);
    store.pageLoader({ mode: "off" });
    // Dropzone
    dropzone.value = new Dropzone("#dropzoneForm", {
      url: "https://httpbin.org/post",
      maxFiles: 1,
      acceptedFiles: "image/*",
      addRemoveLinks: true,
      init: function () {
        let myDropzone = this;

        if (state.value.thumbnail) {
          mockFile.value = { name: state.value.name, size: 12345 };
          let callback = null; // Optional callback when it's done
          let crossOrigin = null; // Added to the `img` tag for crossOrigin handling
          let resizeThumbnail = false; // Tells Dropzone whether it should resize the image first
          myDropzone.displayExistingFile(
            mockFile.value,
            state.value.thumbnail,
            callback,
            crossOrigin,
            resizeThumbnail
          );
        }
      },
    });
    dropzone.value.on("addedfile", (file) => {
      if (mockFile.value) {
        dropzone.value.removeFile(mockFile.value);
        mockFile.value = null;
      }
      if (
        typeof state.value.thumbnail === "object" &&
        state.value.thumbnail !== null
      ) {
        dropzone.value.removeFile(state.value.thumbnail);
      }
      state.value.thumbnail = file;
    });
    dropzone.value.on("removedfile", () => {
      state.value.thumbnail = null;
    });
  } catch (error) {
    store.pageLoader({ mode: "off" });
    console.error("Error fetching data:", error);
  }
});

const listVatRate = [
  {
    label: "0%",
    value: 0,
  },
  {
    label: "15%",
    value: 15,
  },
  {
    label: "25%",
    value: 25,
  },
];

const purchasePriceOrdinary = computed(
  () =>
    (state?.value?.purchase_price || 0) + (state?.value?.ordinary_profit || 0)
);
const profitRateOrdinary = computed(() =>
  (
    (state?.value?.ordinary_profit / (purchasePriceOrdinary.value || 1)) *
    100
  ).toFixed(0)
);
watch(
  () => profitRateOrdinary,
  () => {
    console.log("profitRateOrdinary", profitRateOrdinary.value);
  }
);
const vatOrdinary = computed(
  () => (purchasePriceOrdinary.value * state?.value?.vat_rate) / 100
);
const purchasePriceVatOrdinary = computed(
  () => purchasePriceOrdinary?.value + vatOrdinary?.value
);

const purchasePriceOffer = computed(
  () => (state?.value?.purchase_price || 0) + (state?.value?.offer_profit || 0)
);
const profitRateOffer = computed(() =>
  (
    (state?.value?.offer_profit / (purchasePriceOffer.value || 1)) *
    100
  ).toFixed(0)
);
const vatOffer = computed(
  () => (purchasePriceOffer.value * state?.value?.vat_rate) / 100
);
const purchasePriceVatOffer = computed(
  () => purchasePriceOffer?.value + vatOffer?.value
);

const purchasePriceOrdinaryTakeaway = computed(
  () =>
    (state?.value?.takeaway_purchase_price || 0) +
    (state?.value?.takeaway_ordinary_profit || 0)
);
const profitRateOrdinaryTakeaway = computed(() =>
  (
    (state?.value?.takeaway_ordinary_profit /
      (purchasePriceOrdinaryTakeaway.value || 1)) *
    100
  ).toFixed(0)
);
const vatOrdinaryTakeaway = computed(
  () =>
    (purchasePriceOrdinaryTakeaway.value * state?.value?.takeaway_vat_rate) /
    100
);
const purchasePriceVatOrdinaryTakeaway = computed(
  () => purchasePriceOrdinaryTakeaway?.value + vatOrdinaryTakeaway?.value
);

const purchasePriceOfferTakeaway = computed(
  () =>
    (state?.value?.takeaway_purchase_price || 0) +
    (state?.value?.takeaway_offer_profit || 0)
);
const profitRateOfferTakeaway = computed(() =>
  (
    (state?.value?.takeaway_offer_profit /
      (purchasePriceOfferTakeaway.value || 1)) *
    100
  ).toFixed(0)
);
const vatOfferTakeaway = computed(
  () =>
    (purchasePriceOfferTakeaway.value * state?.value?.takeaway_vat_rate) / 100
);
const purchasePriceVatOfferTakeaway = computed(
  () => purchasePriceOfferTakeaway?.value + vatOfferTakeaway?.value
);

onBeforeUnmount(() => {
  dropzone.value.destroy();
});

function isDecimal(num) {
  return num % 1 !== 0;
}
const parseNumber = (input) => {
  if (!input || Number(input) === 0) return null;
  return isDecimal
    ? parseFloat(((input * 100) / 100).toFixed(2))
    : Number(input);
};

const handleSubmit = (type) => {
  typeSubmit.value = type;
};

const handleInput = (value, type) => {
  const parseVal = parseNumber(value);
  switch (type) {
    case "ordinary_profit": {
      const newPriceExVat = (state.value.purchase_price || 0) + (parseVal || 0);
      const vat = (newPriceExVat * state.value.vat_rate) / 100;
      const newPriceIncVat = newPriceExVat + vat;
      if (state.value.ordinary_price_ex_vat !== parseNumber(newPriceExVat)) {
        state.value.ordinary_price_ex_vat = parseNumber(newPriceExVat) || 0;
      }
      if (state.value.ordinary_price !== parseNumber(newPriceIncVat)) {
        state.value.ordinary_price = parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "ordinary_price_ex_vat": {
      const newProfit = (parseVal || 0) - (state.value?.purchase_price || 0);
      const vat = (parseVal * state?.value?.vat_rate) / 100;
      const newPriceIncVat = parseVal + vat;
      if (state.value.ordinary_profit !== newProfit) {
        state.value.ordinary_profit = newProfit >= 0 ? newProfit : 0;
      }
      if (state.value.ordinary_price !== parseNumber(newPriceIncVat)) {
        state.value.ordinary_price = parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "ordinary_price": {
      const vat = state?.value?.vat_rate
        ? parseVal - parseVal / (1 + state?.value?.vat_rate / 100)
        : 0;
      const newOrdinaryPriceExVat = parseVal - vat;
      const newOrdinaryProfit =
        newOrdinaryPriceExVat - (state.value.purchase_price || 0);
      if (
        state.value.ordinary_price_ex_vat !== parseNumber(newOrdinaryPriceExVat)
      ) {
        state.value.ordinary_price_ex_vat =
          parseNumber(newOrdinaryPriceExVat) || 0;
      }
      if (state.value.ordinary_profit !== parseNumber(newOrdinaryProfit)) {
        state.value.ordinary_profit =
          parseNumber(newOrdinaryProfit) >= 0
            ? parseNumber(newOrdinaryProfit)
            : 0;
      }
      break;
    }
    case "offer_profit": {
      const newPriceExVat = (state.value.purchase_price || 0) + (parseVal || 0);
      const vat = (newPriceExVat * state.value.vat_rate) / 100;
      const newPriceIncVat = newPriceExVat + vat;
      if (state.value.offer_price_ex_vat !== parseNumber(newPriceExVat)) {
        state.value.offer_price_ex_vat = parseNumber(newPriceExVat) || 0;
      }
      if (state.value.offer_price !== parseNumber(newPriceIncVat)) {
        state.value.offer_price = parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "offer_price_ex_vat": {
      const newProfit = (parseVal || 0) - (state.value?.purchase_price || 0);
      const vat = (parseVal * state?.value?.vat_rate) / 100;
      const newPriceIncVat = parseVal + vat;
      if (state.value.offer_profit !== parseNumber(newProfit)) {
        state.value.offer_profit =
          parseNumber(newProfit) >= 0 ? parseNumber(newProfit) : 0;
      }
      if (state.value.offer_price !== parseNumber(newPriceIncVat)) {
        state.value.offer_price = parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "offer_price": {
      const vat = state?.value?.vat_rate
        ? parseVal - parseVal / (1 + state?.value?.vat_rate / 100)
        : 0;
      const newOrdinaryPriceExVat = parseVal - vat;
      const newOrdinaryProfit =
        newOrdinaryPriceExVat - (state.value.purchase_price || 0);
      if (
        state.value.offer_price_ex_vat !== parseNumber(newOrdinaryPriceExVat)
      ) {
        state.value.offer_price_ex_vat =
          parseNumber(newOrdinaryPriceExVat) || 0;
      }
      if (state.value.offer_profit !== parseNumber(newOrdinaryProfit)) {
        state.value.offer_profit =
          parseNumber(newOrdinaryProfit) >= 0
            ? parseNumber(newOrdinaryProfit)
            : 0;
      }
      break;
    }
  }
};

const handleInputTakeAway = (value, type) => {
  const parseVal = parseNumber(value);
  switch (type) {
    case "takeaway_ordinary_profit": {
      // state.value.takeaway_ordinary_profit = parseVal;
      const newPriceExVat =
        (state.value.takeaway_purchase_price || 0) + (parseVal || 0);
      const vat = (newPriceExVat * state.value.takeaway_vat_rate) / 100;
      const newPriceIncVat = newPriceExVat + vat;
      if (state.value.takeaway_price_ex_vat !== parseNumber(newPriceExVat)) {
        state.value.takeaway_price_ex_vat = parseNumber(newPriceExVat) || 0;
      }
      if (state.value.takeaway_ordinary_price !== parseNumber(newPriceIncVat)) {
        state.value.takeaway_ordinary_price = parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "takeaway_price_ex_vat": {
      // state.value.takeaway_price_ex_vat = value;
      const newProfit =
        (parseVal || 0) - (state.value?.takeaway_purchase_price || 0);
      const vat = (value * state?.value?.takeaway_vat_rate) / 100;
      const newPriceIncVat = parseVal + vat;
      if (state.value.takeaway_ordinary_profit !== newProfit) {
        state.value.takeaway_ordinary_profit = parseNumber(newProfit) || 0;
      }
      if (state.value.takeaway_ordinary_price !== parseNumber(newPriceIncVat)) {
        state.value.takeaway_ordinary_price = parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "takeaway_ordinary_price": {
      // state.value.takeaway_ordinary_price = value;
      const vat = state?.value?.takeaway_vat_rate
        ? parseVal - parseVal / (1 + state?.value?.takeaway_vat_rate / 100)
        : 0;
      const newOrdinaryPriceExVat = parseVal - vat;
      const newOrdinaryProfit =
        newOrdinaryPriceExVat - (state.value.takeaway_purchase_price || 0);
      if (
        state.value.takeaway_price_ex_vat !== parseNumber(newOrdinaryPriceExVat)
      ) {
        state.value.takeaway_price_ex_vat =
          parseNumber(newOrdinaryPriceExVat) || 0;
      }
      if (
        state.value.takeaway_ordinary_profit !== parseNumber(newOrdinaryProfit)
      ) {
        state.value.takeaway_ordinary_profit =
          parseNumber(newOrdinaryProfit) >= 0
            ? parseNumber(newOrdinaryProfit)
            : 0;
      }
      break;
    }
    case "takeaway_offer_profit": {
      // state.value.takeaway_offer_profit = value
      const newPriceExVat =
        (state.value.takeaway_purchase_price || 0) + (parseVal || 0);
      const vat = (newPriceExVat * state.value.takeaway_vat_rate) / 100;
      const newPriceIncVat = newPriceExVat + vat;
      if (state.value.takeaway_offer_ex_vat !== parseNumber(newPriceExVat)) {
        state.value.takeaway_offer_ex_vat = parseNumber(newPriceExVat) || 0;
      }
      if (state.value.takeaway_offer_price !== parseNumber(newPriceIncVat)) {
        state.value.takeaway_offer_price = parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "takeaway_offer_ex_vat": {
      // state.value.takeaway_offer_ex_vat = value;
      const newProfit =
        (parseVal || 0) - (state.value?.takeaway_purchase_price || 0);
      const vat = (value * state?.value?.takeaway_vat_rate) / 100;
      const newPriceIncVat = parseVal + vat;
      if (state.value.takeaway_offer_profit !== newProfit) {
        state.value.takeaway_offer_profit =
          parseNumber(newProfit) >= 0 ? parseNumber(newProfit) : 0;
      }
      if (state.value.takeaway_offer_price !== parseNumber(newPriceIncVat)) {
        state.value.takeaway_offer_price = parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "takeaway_offer_price": {
      const vat = state?.value?.takeaway_vat_rate
        ? parseVal - parseVal / (1 + state?.value?.takeaway_vat_rate / 100)
        : 0;
      const newOrdinaryPriceExVat = parseVal - vat;
      const newOrdinaryProfit =
        newOrdinaryPriceExVat - (state.value.takeaway_purchase_price || 0);
      if (
        state.value.takeaway_offer_ex_vat !== parseNumber(newOrdinaryPriceExVat)
      ) {
        state.value.takeaway_offer_ex_vat =
          parseNumber(newOrdinaryPriceExVat) || 0;
      }
      if (
        state.value.takeaway_offer_profit !== parseNumber(newOrdinaryProfit)
      ) {
        state.value.takeaway_offer_profit =
          parseNumber(newOrdinaryProfit) >= 0
            ? parseNumber(newOrdinaryProfit)
            : 0;
      }
    }
  }
};
</script>

<template>
  <BasePageHeading
    :title="
      id ? t('pages.toppings.titles.update') : t('pages.toppings.titles.create')
    "
    :go-back="true"
    :subtitle="t('pages.toppings.labels.label_head_form')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.toppings.labels.manages")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <router-link to="/merchant/toppings">{{
              t("pages.toppings.titles.list")
            }}</router-link>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <span>{{
              id
                ? t("pages.toppings.titles.update")
                : t("pages.toppings.titles.create")
            }}</span>
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <div class="row justify-content-start">
      <div>
        <form @submit.prevent="onSubmit">
          <BaseBlock
            :title="
              id
                ? t('pages.toppings.titles.detail', { name: topping?.name })
                : t('pages.toppings.titles.add')
            "
          >
            <template #options>
              <e-icon
                @click="
                  () => {
                    router.back();
                    dataFetch.setData([], 'merchant-toppings-list');
                    dataFetch.setTotal(0, 'merchant-toppings-list');
                  }
                "
                name="arrow-left"
                role="button"
                class="icon_arrow_left"
              />
            </template>

            <div class="row justify-content-center py-sm-1 py-md-2">
              <div>
                <div class="row mb-2">
                  <div class="col-md-6 mb-4 me-3">
                    <label class="form-label" for="form-name"
                      >{{ t("pages.toppings.fields.topping_name")
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      type="text"
                      class="form-control"
                      id="form-name"
                      name="form-name"
                      :placeholder="t('common.enter')"
                      :class="{
                        'is-invalid': v$.name.$errors.length,
                      }"
                      v-model="state.name"
                      @blur="v$.name.$touch"
                    />
                    <div
                      v-if="v$.name.$errors.length"
                      class="invalid-feedback animated fadeIn"
                    >
                      Please enter name customer
                    </div>
                  </div>

                  <div class="col-sm-8 col-md-4 mb-4">
                    <label class="form-label" for="val-topping-group-id"
                      >{{ t("pages.toppings.fields.topping_group")
                      }}<span class="text-danger">*</span></label
                    >
                    <select
                      id="val-topping-group-id"
                      class="form-select"
                      :class="{
                        'is-invalid': v$.topping_group_id.$errors.length,
                      }"
                      v-model="state.topping_group_id"
                      @blur="v$.topping_group_id.$touch"
                    >
                      <option
                        v-for="(item, index) in optionsTopingGroup"
                        :value="item.value"
                        :key="`item-${index}`"
                      >
                        {{ item.text }}
                      </option>
                    </select>
                    <div
                      v-if="v$.topping_group_id.$errors.length"
                      class="invalid-feedback animated fadeIn"
                    >
                      Please select a topping_group_
                    </div>
                  </div>
                </div>

                <div class="row mb-2">
                  <div class="col-md-6 mb-4 me-3">
                    <label class="form-label" for="form-item-no">{{
                      t("pages.toppings.fields.item_no")
                    }}</label>
                    <input
                      type="text"
                      class="form-control"
                      id="form-item-no"
                      name="form-item-no"
                      placeholder="Enter your item-no.."
                      v-model="state.item_no"
                    />
                  </div>

                  <div class="col-sm-8 col-md-4 mb-4">
                    <label class="form-label" for="form-barcode">{{
                      t("pages.toppings.fields.barcode")
                    }}</label>
                    <input
                      type="text"
                      class="form-control"
                      id="form-barcode"
                      name="form-barcode"
                      placeholder="Barcode"
                      v-model="state.barcode"
                    />
                  </div>
                </div>

                <div class="row mb-2">
                  <div class="col-md-7 mb-4 me-3">
                    <label class="form-label" for="form-description">{{
                      t("pages.toppings.fields.description")
                    }}</label>
                    <textarea
                      class="form-control"
                      id="form-description"
                      name="form-description"
                      v-model="state.description"
                      rows="8"
                      placeholder="Textarea content.."
                    ></textarea>
                  </div>
                  <div class="col-12 col-md-4">
                    <div class="mb-4">
                      <label class="form-label" for="example-file-input">{{
                        t("pages.products.fields.upload")
                      }}</label>
                      <form id="dropzoneForm" class="dropzone"></form>
                    </div>
                  </div>
                  <!--                  <div class="col-sm-6 col-md-3 mb-4">-->
                  <!--                    <label class="form-label" for="form-picture">Picture</label>-->
                  <!--                    <input-->
                  <!--                      class="form-control"-->
                  <!--                      type="file"-->
                  <!--                      id="example-file-input"-->
                  <!--                      @change="handleFileChange"-->
                  <!--                    />-->
                  <!--                    <div v-if="imageUrl">-->
                  <!--                      <img-->
                  <!--                        :style="{-->
                  <!--                          width: '100%',-->
                  <!--                          marginTop: '0.5rem',-->
                  <!--                          maxHeight: '12vw',-->
                  <!--                          objectFit: 'cover',-->
                  <!--                        }"-->
                  <!--                        :src="imageUrl"-->
                  <!--                        alt="Uploaded Image"-->
                  <!--                      />-->
                  <!--                    </div>-->
                  <!--                  </div>-->
                </div>

                <div class="row mb-2">
                  <div class="col-sm-8 col-md-4 mb-4">
                    <label class="form-label" for="val-supplier-id">{{
                      t("pages.toppings.fields.supplier")
                    }}</label>
                    <select
                      id="val-supplier-id"
                      class="form-select"
                      v-model="state.supplier_id"
                    >
                      <option
                        v-for="(item, index) in optionsSupplier"
                        :value="item.value"
                        :key="`item-${index}`"
                      >
                        {{ item.text }}
                      </option>
                    </select>
                  </div>

                  <div class="col-sm-8 col-md-4 mb-4">
                    <label class="form-label" for="val-producer-id">{{
                      t("pages.toppings.fields.producer")
                    }}</label>
                    <select
                      id="val-producer-id"
                      class="form-select"
                      v-model="state.producer_id"
                    >
                      <option
                        v-for="(item, index) in optionsProducer"
                        :value="item.value"
                        :key="`item-${index}`"
                      >
                        {{ item.text }}
                      </option>
                    </select>
                  </div>

                  <div class="col-sm-8 col-md-4 mb-4">
                    <label class="form-label" for="val-unit-id">{{
                      t("pages.toppings.fields.unit")
                    }}</label>
                    <select
                      id="val-unit-id"
                      class="form-select"
                      v-model="state.unit_id"
                    >
                      <option
                        v-for="(item, index) in optionsUnit"
                        :value="item.value"
                        :key="`item-${index}`"
                      >
                        {{ item.text }}
                      </option>
                    </select>
                  </div>
                </div>

                <div class="row mb-2">
                  <div class="col-sm-5 col-md-3 mb-4">
                    <div class="form-check form-switch">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        value=""
                        id="example-switch-default1"
                        name="example-switch-default1"
                        :checked="state.in_stock"
                        v-model="state.in_stock"
                      />
                      <label
                        class="form-check-label"
                        for="example-switch-default1"
                        >{{ t("pages.toppings.fields.in_stock") }}</label
                      >
                    </div>
                  </div>

                  <div class="col-sm-5 col-md-3 mb-4">
                    <div class="form-check form-switch mb-4">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        value=""
                        id="form-in_kiosk"
                        name="example-switch-default1"
                        :checked="state.in_kiosk"
                        v-model="state.in_kiosk"
                      />
                      <label class="form-check-label" for="form-in_kiosk">{{
                        t("pages.toppings.fields.show_in_kiosk")
                      }}</label>
                    </div>
                    <div class="form-check form-switch mb-4">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        value=""
                        id="form-in_table"
                        name="example-switch-default1"
                        :checked="state.in_table"
                        v-model="state.in_table"
                      />
                      <label class="form-check-label" for="form-in_table">{{
                        t("pages.toppings.fields.show_in_table_kiosk")
                      }}</label>
                    </div>
                    <div class="form-check form-switch mb-4">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        value=""
                        id="form-in_takeaway"
                        :checked="state.in_takeaway"
                        v-model="state.in_takeaway"
                      />
                      <label class="form-check-label" for="form-in_takeaway">{{
                        t("pages.toppings.fields.show_in_takeaway")
                      }}</label>
                    </div>
                  </div>
                </div>

                <!-- Price -->
                <hr class="mb-5" />
                <div class="row bg-gray-lighter px-2 py-3 rounded-2">
                  <div class="col-12 col-md-10 overflow-x-auto">
                    <div class="row" style="min-width: 800px">
                      <div class="col-2 mb-2"></div>
                      <div class="col-2 mb-2"></div>
                      <div class="col-3 mb-2">
                        <div class="">
                          <label class="form-label" for="val-costPrice">{{
                            t("pages.products.fields.purchase_price")
                          }}</label>
                          <input
                            type="number"
                            id="val-costPrice"
                            class="form-control"
                            v-model="state.purchase_price"
                            :placeholder="t('common.enter')"
                            step="0.01"
                          />
                        </div>
                      </div>
                      <div class="col-3 mb-4">
                        <div class="">
                          <label class="form-label" for="val-costPrice">{{
                            t("pages.products.fields.cost_price")
                          }}</label>
                          <input
                            type="number"
                            id="val-costPrice"
                            class="form-control"
                            :value="state.purchase_price"
                            disabled
                            :placeholder="t('common.enter')"
                            step="0.01"
                          />
                        </div>
                      </div>
                      <div class="col-2 mb-4">
                        <div class="">
                          <label class="form-label" for="val-vatRate">{{
                            t("pages.products.fields.vat_rate")
                          }}</label>
                          <select
                            id="val-vatRate"
                            class="form-select"
                            v-model="state.vat_rate"
                          >
                            <option
                              v-for="vat in listVatRate"
                              :key="vat.label"
                              :value="vat.value"
                            >
                              {{ vat.label }}
                            </option>
                          </select>
                        </div>
                      </div>
                      <div class="col-2 mb-2">
                        {{ t("pages.products.fields.ordinary_price") }}
                      </div>
                      <div class="col-2 mb-2">
                        <div class="">
                          <label
                            class="form-label"
                            for="val-profitRateOrdinary"
                          >
                            {{ t("pages.products.fields.profit_rate") }}</label
                          >
                          <input
                            type="number"
                            id="val-profitRateOrdinary"
                            class="form-control"
                            :value="profitRateOrdinary"
                            disabled
                            step="0.01"
                          />
                        </div>
                      </div>
                      <div class="col-3 mb-2">
                        <div class="">
                          <label class="form-label" for="val-profitOrdinary2">{{
                            t("pages.products.fields.profit")
                          }}</label>
                          <input
                            type="number"
                            id="val-profitOrdinary2"
                            class="form-control"
                            placeholder="0"
                            v-model="state.ordinary_profit"
                            :class="{
                              'is-invalid': v$.ordinary_profit.$errors.length,
                            }"
                            @blur="v$.ordinary_profit.$touch"
                            @input="
                              (e) =>
                                handleInput(e.target.value, 'ordinary_profit')
                            "
                            step="0.01"
                          />
                        </div>
                      </div>
                      <div class="col-3 mb-2">
                        <div class="">
                          <label
                            class="form-label"
                            for="val-purchasePriceOrdinary"
                            >{{
                              t("pages.products.fields.price_ex_vat")
                            }}</label
                          >
                          <input
                            type="number"
                            id="val-purchasePriceOrdinary"
                            class="form-control"
                            placeholder="0"
                            v-model="state.ordinary_price_ex_vat"
                            @input="
                              (e) =>
                                handleInput(
                                  e.target.value,
                                  'ordinary_price_ex_vat'
                                )
                            "
                            step="0.01"
                          />
                        </div>
                      </div>
                      <div class="col-2 mb-2">
                        <div class="">
                          <label
                            class="form-label"
                            for="val-purchasePriceOrdinaryVat"
                            >{{
                              t("pages.products.fields.price_inc_vat")
                            }}</label
                          >
                          <input
                            type="number"
                            id="val-purchasePriceOrdinaryVat"
                            class="form-control"
                            placeholder="0"
                            v-model="state.ordinary_price"
                            @input="
                              (e) =>
                                handleInput(e.target.value, 'ordinary_price')
                            "
                            step="0.01"
                          />
                        </div>
                      </div>
                      <div class="col-2 mb-2">
                        {{ t("pages.products.fields.offer_price") }}
                      </div>
                      <div class="col-2 mb-2">
                        <div class="">
                          <input
                            type="number"
                            id="val-profitRateOffer"
                            class="form-control"
                            :value="profitRateOffer"
                            disabled
                            step="0.01"
                          />
                        </div>
                      </div>
                      <div class="col-3 mb-2">
                        <div class="">
                          <input
                            type="number"
                            id="val-profitOffer"
                            class="form-control"
                            v-model="state.offer_profit"
                            @input="
                              (e) => handleInput(e.target.value, 'offer_profit')
                            "
                            placeholder="0"
                            step="0.01"
                          />
                        </div>
                      </div>
                      <div class="col-3 mb-2">
                        <div class="">
                          <input
                            type="number"
                            id="val-purchasePriceOffer"
                            class="form-control"
                            placeholder="0"
                            v-model="state.offer_price_ex_vat"
                            @input="
                              (e) =>
                                handleInput(
                                  e.target.value,
                                  'offer_price_ex_vat'
                                )
                            "
                            step="0.01"
                          />
                        </div>
                      </div>
                      <div class="col-2 mb-2">
                        <div class="">
                          <input
                            type="number"
                            id="val-purchasePriceVatOffer"
                            class="form-control"
                            placeholder="0"
                            v-model="state.offer_price"
                            @input="
                              (e) => handleInput(e.target.value, 'offer_price')
                            "
                            step="0.01"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-md-2">
                    <div class="">
                      <label class="form-label" for="val-isActiveOfferPrice">
                        {{
                          t("pages.products.fields.active_offer_price")
                        }}</label
                      >
                      <div class="form-check form-switch">
                        <input
                          v-model="state.active_offer_price"
                          class="form-check-input"
                          type="checkbox"
                          id="val-isActiveOfferPrice"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-12 my-3">
                    <div class="form-check form-switch">
                      <input
                        v-model="state.is_takeaway_price"
                        class="form-check-input"
                        type="checkbox"
                        id="val-isTakeawayPrice"
                      />
                      <label
                        class="form-check-label"
                        for="val-isTakeawayPrice"
                        >{{
                          t("pages.products.fields.active_takeaway_price")
                        }}</label
                      >
                    </div>
                  </div>
                </div>
                <div
                  class="row bg-gray-lighter px-2 py-3 rounded-2"
                  v-if="state.is_takeaway_price"
                >
                  <div class="col-12 col-md-10 overflow-x-auto">
                    <div class="row" style="min-width: 800px">
                      <div class="col-2 mb-2"></div>
                      <div class="col-2 mb-2"></div>
                      <div class="col-3 mb-2">
                        <div class="">
                          <label class="form-label" for="val-costPrice1">{{
                            t("pages.products.fields.purchase_price")
                          }}</label>
                          <input
                            type="number"
                            id="val-costPrice1"
                            class="form-control"
                            v-model="state.takeaway_purchase_price"
                            :placeholder="t('common.enter')"
                            step="0.01"
                          />
                        </div>
                      </div>
                      <div class="col-3 mb-4">
                        <div class="">
                          <label class="form-label" for="val-costPrice2">{{
                            t("pages.products.fields.cost_price")
                          }}</label>
                          <input
                            type="number"
                            id="val-costPrice2"
                            class="form-control"
                            :value="state.takeaway_purchase_price"
                            disabled
                            :placeholder="t('common.enter')"
                            step="0.01"
                          />
                        </div>
                      </div>
                      <div class="col-2 mb-4">
                        <div class="">
                          <label class="form-label" for="val-vatRate">{{
                            t("pages.products.fields.vat_rate")
                          }}</label>
                          <select
                            id="val-vatRate"
                            class="form-select"
                            v-model="state.takeaway_vat_rate"
                          >
                            <option
                              v-for="vat in listVatRate"
                              :key="vat.label"
                              :value="vat.value"
                            >
                              {{ vat.label }}
                            </option>
                          </select>
                        </div>
                      </div>
                      <div class="col-2 mb-2">
                        {{ t("pages.products.fields.takeaway_price") }}
                      </div>
                      <div class="col-2 mb-2">
                        <div class="">
                          <label
                            class="form-label"
                            for="val-profitRateOrdinary"
                            >{{ t("pages.products.fields.profit_rate") }}</label
                          >
                          <input
                            type="number"
                            id="val-profitRateOrdinary"
                            class="form-control"
                            :value="profitRateOrdinaryTakeaway"
                            disabled
                            step="0.01"
                          />
                        </div>
                      </div>
                      <div class="col-3 mb-2">
                        <div class="">
                          <label class="form-label" for="val-profitOrdinary">{{
                            t("pages.products.fields.profit")
                          }}</label>
                          <input
                            type="number"
                            id="val-profitOrdinary"
                            class="form-control"
                            placeholder="0"
                            v-model="state.takeaway_ordinary_profit"
                            @input="
                              (e) =>
                                handleInputTakeAway(
                                  e?.target?.value,
                                  'takeaway_ordinary_profit'
                                )
                            "
                            :class="{
                              'is-invalid':
                                v$.takeaway_ordinary_profit &&
                                v$.takeaway_ordinary_profit.$errors.length,
                            }"
                            @blur="
                              v$.takeaway_ordinary_profit &&
                                v$.takeaway_ordinary_profit.$touch
                            "
                            step="0.01"
                          />
                        </div>
                      </div>
                      <div class="col-3 mb-2">
                        <div class="">
                          <label
                            class="form-label"
                            for="val-purchasePriceOrdinary1"
                            >{{
                              t("pages.products.fields.price_ex_vat")
                            }}</label
                          >
                          <input
                            type="number"
                            id="val-purchasePriceOrdinary1"
                            class="form-control"
                            placeholder="0"
                            @input="
                              (e) =>
                                handleInputTakeAway(
                                  e?.target?.value,
                                  'takeaway_price_ex_vat'
                                )
                            "
                            v-model="state.takeaway_price_ex_vat"
                            step="0.01"
                          />
                        </div>
                      </div>
                      <div class="col-2 mb-2">
                        <div class="">
                          <label
                            class="form-label"
                            for="val-purchasePriceOrdinaryVat2"
                            >{{
                              t("pages.products.fields.price_inc_vat")
                            }}</label
                          >
                          <input
                            type="number"
                            id="val-purchasePriceOrdinaryVat2"
                            class="form-control"
                            placeholder="0"
                            @input="
                              (e) =>
                                handleInputTakeAway(
                                  e?.target?.value,
                                  'takeaway_ordinary_price'
                                )
                            "
                            v-model="state.takeaway_ordinary_price"
                            step="0.01"
                          />
                        </div>
                      </div>
                      <div class="col-2 mb-2">
                        {{ t("pages.products.fields.offer_takeaway_price") }}
                      </div>
                      <div class="col-2 mb-2">
                        <div class="">
                          <input
                            type="number"
                            id="val-profitRateOffer"
                            class="form-control"
                            :value="profitRateOfferTakeaway"
                            disabled
                            step="0.01"
                          />
                        </div>
                      </div>
                      <div class="col-3 mb-2">
                        <div class="">
                          <input
                            type="number"
                            id="val-profitOffer"
                            class="form-control"
                            placeholder="0"
                            @input="
                              (e) =>
                                handleInputTakeAway(
                                  e?.target?.value,
                                  'takeaway_offer_profit'
                                )
                            "
                            v-model="state.takeaway_offer_profit"
                            step="0.01"
                          />
                        </div>
                      </div>
                      <div class="col-3 mb-2">
                        <div class="">
                          <input
                            type="number"
                            id="val-purchasePriceOffer"
                            class="form-control"
                            placeholder="0"
                            @input="
                              (e) =>
                                handleInputTakeAway(
                                  e?.target?.value,
                                  'takeaway_offer_ex_vat'
                                )
                            "
                            v-model="state.takeaway_offer_ex_vat"
                            step="0.01"
                          />
                        </div>
                      </div>
                      <div class="col-2 mb-2">
                        <div class="">
                          <input
                            type="number"
                            id="val-purchasePriceVatOffer"
                            class="form-control"
                            placeholder="0"
                            @input="
                              (e) =>
                                handleInputTakeAway(
                                  e.target.value,
                                  'takeaway_offer_price'
                                )
                            "
                            v-model="state.takeaway_offer_price"
                            step="0.01"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-md-2">
                    <div class="">
                      <label class="form-label" for="val-isActiveOfferPrice">
                        {{
                          t("pages.products.fields.active_offer_price")
                        }}</label
                      >
                      <div class="form-check form-switch">
                        <input
                          v-model="state.takeaway_active_offer_price"
                          class="form-check-input"
                          type="checkbox"
                          id="val-isActiveOfferPrice"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <!-- End Price -->

                <div
                  class="my-4"
                  :style="{
                    textAlign: 'end',
                    display: 'flex',
                    gap: '5px',
                    justifyContent: 'end',
                  }"
                >
                  <button
                    type="submit"
                    class="btn btn-sm btn-primary"
                    :style="{ color: '#fff' }"
                    @click="handleSubmit('confirm')"
                  >
                    {{ t("pages.toppings.fields.confirm") }}
                  </button>
                  <button
                    v-if="!id"
                    type="submit"
                    class="btn btn-sm btn-primary"
                    @click="handleSubmit('confirm_add')"
                    :style="{ color: '#fff' }"
                  >
                    {{ t("pages.products.buttons.confirm_add") }}
                  </button>
                </div>
              </div>
            </div>
          </BaseBlock>
        </form>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
@import "dropzone/dist/dropzone.css";
@import "@/assets/scss/vendor/dropzone";
.icon_arrow_left {
  background-color: rgb(243, 235, 235);
  border-radius: 100%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.block-content {
  padding-inline: 10px;
}
.dz-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>
