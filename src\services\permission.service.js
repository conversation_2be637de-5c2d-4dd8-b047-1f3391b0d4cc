import { http } from './Base/base.service'

export const permissionService = {
  async getList(query) {
    return await http.get('/permissions', {
      params: query
    })
  },

  async get(id) {
    return await http.get('/permissions/' + id)
  },

  async create(data) {
    return await http.post('/permissions', data)
  },

  async update(id, data) {
    return await http.patch('/permissions/' + id, data)
  },

  async delete(id) {
    return await http.delete('/permissions/' + id)
  }
}
