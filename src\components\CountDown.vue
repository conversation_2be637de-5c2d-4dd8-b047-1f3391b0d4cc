<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import moment from "moment-timezone";

const countdown = ref("");
const props = defineProps(["targetDate"]);
const emit = defineEmits(["load"]);

const updateCountdown = () => {
  const now = moment.tz("Europe/Oslo");
  const targetDate = moment.tz(props.targetDate, "Europe/Oslo");
  const duration = moment.duration(targetDate.diff(now));
  if (duration.asMilliseconds() >= 0) {
    countdown.value = `${Math.floor(duration.asHours())
      .toString()
      .padStart(2, "0")}:${duration
      .minutes()
      .toString()
      .padStart(2, "0")}:${duration.seconds().toString().padStart(2, "0")}`;
  } else {
    setTimeout(() => {
      emit("load");
      clearInterval(timer);
    }, 2000);
    countdown.value = "00:00:00";
  }
};
let timer = null;
onMounted(() => {
  updateCountdown();
  timer = setInterval(updateCountdown, 1000);
});
onUnmounted(() => {
  clearInterval(timer);
});
</script>
<template>
  <div class="text-danger">{{ countdown }}</div>
</template>
<style scoped>
.timeer {
  color: red;
}
</style>
