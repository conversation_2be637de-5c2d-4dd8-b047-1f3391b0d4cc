import { defineStore } from 'pinia';
import { ref } from 'vue';

export const scrollTo = defineStore('scroll', () => {
    const idItem = ref();
    const page = ref(1);
    const setPage = (number) => {
        page.value = number
    }
    const formUpdateSuccess = ref(false)
    const getElement = (item) => {
        formUpdateSuccess.value = true
        idItem.value = item;
    };
    const setUpdateSuccess = () => {
        formUpdateSuccess.value = false
    }
    return {
      idItem,
      getElement,
      formUpdateSuccess,
      setUpdateSuccess,
      setPage,
      page
    };
  });