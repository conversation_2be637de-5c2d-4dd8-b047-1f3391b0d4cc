<script setup>
import { reactive, onMounted, ref, watch } from "vue";
import { Dataset, DatasetItem, DatasetInfo, DatasetShow } from "vue-dataset";
import moment from "moment";
import { reportOrderService } from "@/services/report_order.service";
import EListEmpty from "@/components/Elements/EListEmpty.vue";
import { useTemplateStore } from "@/stores/template";
import FlatPickr from "vue-flatpickr-component";
import EButton from "@/components/Elements/EButton.vue";
import { useI18n } from "vue-i18n";
import Trans from "@/i18n/i18nUtils";
import { storeData } from "@/stores/storeData";

const store = useTemplateStore();
const dataFetch = storeData();
const dateRange = ref(defaultDateRange());

const handleChangeDate = async (selectedDates) => {
  if (selectedDates.length === 2) {
    await onFetchList();
  }
  document.querySelectorAll("#datasetLength label").forEach((el) => {
    el.remove();
  });
  let selectLength = document.querySelector("#datasetLength select");
  if (selectLength) {
    selectLength.classList = "";
    selectLength.classList.add("form-select");
    selectLength.style.width = "80px";
  }
};

const configRange = ref({
  mode: "range",
  dateFormat: "d-m-Y",
  onChange: handleChangeDate,
  locale: { firstDayOfWeek: 1 },
});
const { t, locale } = useI18n();

const cols = reactive([
  {
    name: t("pages.order.fields.id"),
    field: "id",
    sort: "",
  },
  {
    name: t("pages.order.fields.order"),
    field: "order",
    sort: "",
  },
  {
    name: t("pages.order.fields.status"),
    field: "status",
    sort: "",
  },
  {
    name: t("pages.report.fields.no_of_sales_ex_vat"),
    field: "vat_ex",
    sort: "",
  },
  {
    name: t("pages.report.fields.sales_inc_vat"),
    field: "vat_inc",
    sort: "",
  },
]);
const updateCols = () => {
  cols[0].name = t("pages.order.fields.id");
  cols[1].name = t("pages.order.fields.order");
  cols[2].name = t("pages.order.fields.status");
  cols[3].name = t("pages.report.fields.no_of_sales_ex_vat");
  cols[4].name = t("pages.report.fields.sales_inc_vat");
};

// Watch for language changes
watch(locale, () => {
  updateCols();
});

const orderReports = ref();

const limit = ref(10);
const currentPage = ref(1);
const total = ref();
const visible = ref(true);

const onFetchList = async () => {
  let start_date;
  let end_date;
  const dates = [];

  const [startDateString, endDateString] = dateRange.value.split(" to ");
  start_date = moment(startDateString, "DD-MM-YYYY").toISOString();
  end_date = endDateString
    ? moment(endDateString, "DD-MM-YYYY").endOf("day").toISOString()
    : moment(startDateString, "DD-MM-YYYY").endOf("day").toISOString();
  let currentDate = moment(startDateString, "DD-MM-YYYY").clone();
  while (
    currentDate.isSameOrBefore(
      moment(endDateString || startDateString, "DD-MM-YYYY"),
      "day"
    )
  ) {
    dates.push(currentDate.format("DD-MM-YYYY"));
    currentDate.add(1, "day");
  }

  store.pageLoader({ mode: "on" });
  const response = await reportOrderService.getList({
    start_date: start_date,
    end_date: end_date,
    page: currentPage.value,
    limit: limit.value,
  });
  if (!response?.error) {
    orderReports.value = response.data;
    total.value = response.data.orders.total;
  }
  store.pageLoader({ mode: "off" });
};

watch(limit, async () => {
  currentPage.value = 1;
  await onFetchList();
});

onMounted(async () => {
  if (
    dataFetch.storeData?.["merchant-report-order"]?.orders?.data?.length > 0
  ) {
    await (orderReports.value = dataFetch.storeData?.["merchant-report-order"]);
    total.value = dataFetch.total?.["merchant-report-order"];
    store.pageLoader({ mode: "off" });
  } else {
    await onFetchList();
  }
  document.querySelectorAll("#datasetLength label").forEach((el) => {
    el.remove();
  });
  let selectLength = document.querySelector("#datasetLength select");
  if (selectLength) {
    selectLength.classList = "";
    selectLength.classList.add("form-select");
    selectLength.style.width = "80px";
  }
});

async function handleExportCS() {
  let start_date;
  let end_date;
  const [startDateString, endDateString] = dateRange.value.split(" to ");
  start_date = moment(startDateString, "YYYY-MM-DD");
  end_date = moment(endDateString, "YYYY-MM-DD");
  const response = await reportOrderService.export({
    start_date: start_date?._i,
    end_date: end_date?._i,
  });
  const blod = new Blob([response], { type: "text/csv;charset=utf-8" });
  const url = URL.createObjectURL(blod);
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute(
    "download",
    `orders-report_${start_date?._i}_${end_date?._i}.csv`
  );
  link.click();
}

async function handleExportPDF() {
  let start_date;
  let end_date;
  const [startDateString, endDateString] = dateRange.value.split(" to ");
  start_date = moment(startDateString, "YYYY-MM-DD");
  end_date = moment(endDateString, "YYYY-MM-DD");
  const response = await reportOrderService.exportPDF({
    start_date: start_date?._i,
    end_date: end_date?._i,
  });
  const blod = new Blob([response], { type: "application/pdf" });
  const url = URL.createObjectURL(blod);
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", "export_pdf_orders.pdf");
  link.click();
}

function defaultDateRange() {
  const startDate = moment().startOf("isoWeek").format("DD-MM-YYYY");
  const endDate = moment().endOf("isoWeek").format("DD-MM-YYYY");
  return `${startDate} to ${endDate}`;
}
</script>

<template>
  <BasePageHeading
    :title="t('menu.order_report')"
    :subtitle="t('pages.report.labels.label_head_list_order')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.report.labels.manages")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            {{ t("menu.order_report") }}
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <div class="row gap-2">
      <div class="col-12 d-flex justify-content-between">
        <FlatPickr
          class="form-control w-25"
          id="example-flatpickr-range"
          :placeholder="t('pages.report.placeholder.date_range')"
          v-model="dateRange"
          :config="configRange"
        />
        <div class="d-flex justify-content-end items-center gap-1">
          <e-button type="info" size="sm" @click="() => handleExportCS()">
            {{ t("pages.report.labels.btn_export") }}
          </e-button>
          <e-button type="info" size="sm" @click="() => handleExportPDF()">
            {{ t("pages.report.labels.btn_pdf") }}
          </e-button>
        </div>
      </div>
      <hr class="my-4" />
    </div>
    <BaseBlock :title="t('menu.order_report')">
      <Dataset
        v-slot="{ ds }"
        :ds-data="orderReports.orders.data"
        :ds-search-in="['id', 'name']"
        v-if="orderReports?.orders.data.length > 0"
      >
        <div class="row" :data-page-count="ds.dsPagecount">
          <div id="datasetLength" class="col-md-8 py-2">
            <DatasetShow v-show="false" :dsShowEntries="100" />
            <div class="form-inline">
              <select class="form-select" style="width: 80px" v-model="limit">
                <option :value="5">5</option>
                <option :value="10">10</option>
                <option :value="25">25</option>
                <option :value="50">50</option>
                <option :value="100">100</option>
              </select>
            </div>
          </div>
        </div>

        <hr />
        <div class="row">
          <div class="col-md-12">
            <div class="table-responsive">
              <table class="table table-striped mb-0">
                <thead>
                  <tr>
                    <th
                      v-for="th in cols"
                      :key="th.field"
                      :class="['sort', th.sort]"
                    >
                      {{ th.name }} <i class="gg-select float-end"></i>
                    </th>
                  </tr>
                </thead>
                <DatasetItem tag="tbody" class="fs-sm">
                  <template #default="{ row }">
                    <tr>
                      <td style="min-width: 50px">
                        {{ row.id }}
                      </td>
                      <td style="color: #f59e0b">
                        <router-link
                          :to="{
                            path: Trans.i18nRouteByPath('/orders/' + row.id),
                            query: { transaction_id: row?.transaction_id },
                          }"
                        >
                          {{ row.code_show }}
                        </router-link>
                      </td>
                      <td>{{ row.status }}</td>
                      <td>
                        <span v-if="row?.transaction_id">-</span>
                        {{
                          row.amount_total_without_vat
                            ? row.status === "refunded"
                              ? "-" + row.amount_total_without_vat + ",-kr"
                              : row.amount_total_without_vat + ",-kr"
                            : row.amount_total_without_vat.toFixed(2) + ",-kr"
                        }}
                      </td>
                      <td>
                        <span v-if="row?.transaction_id">-</span>
                        {{
                          row.amount_total
                            ? row.status === "refunded"
                              ? "-" + row.amount_total + ",-kr"
                              : row.amount_total + ",-kr"
                            : row.amount_total.toFixed(2) + ",-kr"
                        }}
                      </td>
                    </tr>
                  </template>
                </DatasetItem>
                <tfoot>
                  <tr>
                    <td>{{ t("pages.report.fields.total") }}</td>
                    <td></td>
                    <td></td>
                    <td>
                      {{
                        orderReports?.total_price_ex_vat?.toFixed(2) + ",-kr"
                      }}
                    </td>
                    <td>
                      {{
                        orderReports?.total_price_inc_vat?.toFixed(2) + ",-kr"
                      }}
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        </div>
        <div
          class="d-flex flex-md-row flex-column justify-content-between align-items-center"
        >
          <DatasetInfo class="py-3 fs-sm" />
          <el-pagination
            v-if="visible"
            v-model:current-page="currentPage"
            @current-change="onFetchList"
            background
            v-model:page-size="limit"
            layout="prev, pager, next"
            :prev-text="t('pages.footer.previous')"
            :next-text="t('pages.footer.next')"
            :total="total"
          />
        </div>
      </Dataset>
      <EListEmpty v-else />
    </BaseBlock>
  </div>
</template>

<style lang="scss">
@import "flatpickr/dist/flatpickr.css";
@import "@/assets/scss/vendor/flatpickr";

tfoot > tr > td {
  font-weight: bold;
}
</style>
