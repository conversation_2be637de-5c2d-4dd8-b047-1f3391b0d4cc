import { http } from "./Base/base.service";
export const orderService = {
  async getList(query) {
    return await http.get("/orders", {
      params: query,
    });
  },

  async getDetail(id) {
    return await http.get(`/orders/${id}`);
  },

  async getDetailRefund(id) {
    return await http.get(`/order-refund/${id}`);
  },

  async refundPaymentVipps(code) {
    return await http.post(`/payments/vipps/refund/${code}`);
  },

  async refundPaymentStripe(code) {
    return await http.post(`/payments/stripe/refund/${code}`);
  },

  async refundPaymentCash(code) {
    return await http.post(`/payments/cash/refund/${code}`);
  },

  async refundPaymentBambora(code) {
    return await http.post(`/payments/bambora/refund/${code}`);
  },

  async getVat(id) {
    return await http.get(`/orders/groupVat/${id}`);
  },
};
