<script setup>
import EButton from "@/components/Elements/EButton.vue";
import EIcon from "@/components/Elements/EIcon.vue";
import EListEmpty from "@/components/Elements/EListEmpty.vue";
import EModal from "@/components/Elements/EModal.vue";
import { productService } from "@/services/product.service";
import ProductsDndTableRow from "@/views/merchant/Products/Components/ProductsDndTableRow.vue";
import { sortBy } from "lodash";
import { common } from "@/stores/storeCommon";
import { HTML5Backend } from "react-dnd-html5-backend";
import { onMounted, reactive, ref, watch } from "vue";
import {
  Dataset,
  DatasetInfo,
  DatasetItem,
  DatasetPager,
  DatasetSearch,
} from "vue-dataset";
import { DndProvider } from "vue3-dnd";
import { useI18n } from "vue-i18n";

const title = defineModel("title");
const disabledForm = defineModel("disabledForm");
const description = defineModel("description");
const commonStore = common();
const hide_in_rush = defineModel("hide_in_rush");
const items = defineModel("items");
const dataProps = ref(items);
console.log("dataProps", dataProps);
const { t, locale } = useI18n();
const props = defineProps({
  indexing: Number,
  isCheck: Boolean,
  isCheckTitle: Boolean,
  isSubmit: Boolean,
  global_custom_id: Number,
});
const blur = ref(false);
watch(props, () => {
  if (props.isSubmit) {
    blur.value = true;
  }
});
watch(title, () => {
  if (title.value) {
    blur.value = true;
  }
});
onMounted(async () => {
  if (props.isSubmit) {
    blur.value = true;
  }
});
const moveCard = (dragIndex, hoverIndex) => {
  const item = items.value[dragIndex];
  items.value.splice(dragIndex, 1);
  items.value.splice(hoverIndex, 0, item);
};
const onRemoveItem = (index, _id) => {
  items.value.splice(index, 1);
  selectedToppingId.value = selectedToppingId.value.filter(
    (item) => item !== _id
  );
};
const visibleModal = ref();
const listToppings = ref([]);
const onFetchListToppings = async (query) => {
  const response = await productService.getListConstituents(query);
  if (!response?.error) {
    const data =
      response.data?.map((item) => ({
        ...item,
        _id: `${item.id}-${item.type}`,
      })) || [];
    listToppings.value = sortBy(data, (item) => {
      const parsedName = item?.name?.replace(/(\d+)/g, (match) => {
        return match.padStart(10, "0"); // Pad numbers with leading zeros for correct sorting
      });
      return parsedName.toLowerCase();
    });
  }
};
const changeValueTakeAway = (value, id) => {
  for (let i = 0; i < items.value.length; i++) {
    if (items.value[i].id === id) {
      items.value[i].takeaway_inc_vat_price = value;
    }
  }
};
const changeValuePrice = (value, id) => {
  for (let i = 0; i < items.value.length; i++) {
    if (items.value[i].id === id) {
      items.value[i].dinein_inc_vat_price = value;
    }
  }
};
const openModal = async () => {
  await onFetchListToppings({
    search: searchKey.value,
  });
  visibleModal.value = true;
};
const cols = reactive([
  {
    name: t("pages.products.labels.name"),
    field: "name",
    sort: "",
  },
  {
    name: t("pages.products.labels.takeaway_price"),
    field: "takeaway_ordinary_price",
    sort: "",
  },
  {
    name: t("pages.products.labels.dine_price"),
    field: "ordinary_price",
    sort: "",
  },
  {
    name: t("pages.products.labels.topping_group"),
    field: "takeaway_ordinary_price",
    sort: "",
  },
  {
    name: t("pages.products.labels.product_category"),
    field: "ordinary_price",
    sort: "",
  },
  {
    name: t("pages.products.labels.unit"),
    field: "unit",
    sort: "",
  },
  {
    name: t("pages.products.labels.type"),
    field: "type",
    sort: "",
  },
]);
const updateCols = () => {
  cols[0].name = t("pages.products.labels.name");
  cols[1].name = t("pages.products.labels.takeaway_price");
  cols[2].name = t("pages.products.labels.dine_price");
  cols[3].name = t("pages.products.labels.topping_group");
  cols[4].name = t("pages.products.labels.product_category");
  cols[5].name = t("pages.products.labels.unit");
  cols[6].name = t("pages.products.labels.type");
};

// Watch for language changes
watch(locale, () => {
  updateCols();
});
let toppingSelected = items.value.map((item) => `${item.id}-${item.type}`);
const selectedToppingId = ref([...toppingSelected]);
const onSubmitSelectedTopping = () => {
  items.value = selectedToppingId.value.map((val) => {
    const selectedItem = listToppings.value.find((topping) => {
      return topping?._id === val;
    });
    const isItem = items.value?.find(
      (itm) => `${itm?.id}-${itm?.type}` === selectedItem?._id
    );
    return isItem
      ? isItem
      : {
          ...selectedItem,
          takeaway_inc_vat_price:
            selectedItem?.takeaway_ordinary_price !== "0"
              ? selectedItem?.takeaway_ordinary_price
              : null,
          dinein_inc_vat_price:
            selectedItem?.ordinary_price || selectedItem?.price,
          max_quantity: null,
          _type: null,
        };
  });
  searchKey.value = "";
};
const emit = defineEmits(["remove", "move"]);
const onRemoveCustomFood = (index) => {
  emit("remove", index, props.global_custom_id);
};
const searchKey = ref("");
// const onSearchConstituents = debounce(() => {
//   if (searchKey.value) {
//     onFetchListToppings({
//       search: searchKey.value
//     })
//   }
// }, 800)

const onMove = (index, step) => {
  if (index === 0 && step === -1) return;
  return emit("move", index, step);
};
const Datasett = ref();
const groupSearch = ref("");
const nameSearch = ref("");
const isNameSearch = ref(true);
const searchName = () => {
  isNameSearch.value = true;
  Datasett.value.search(nameSearch.value);
};
const searchGroup = () => {
  isNameSearch.value = false;
  Datasett.value.search(groupSearch.value);
};
const test = (row) => {
  console.log(row._id);
  console.log("selectedToppingId", selectedToppingId.value);
};
</script>

<template>
  <BaseBlock
    :title="t('pages.products.labels.title_remove_item')"
    header-class="bg-danger"
    bordered
    themed
  >
    <template #options>
      <div class="d-block d-md-inline-block" v-if="commonStore.rushModeOn">
        <div class="sw-container">
          <div class="d-none d-md-block" style="width: 180px">
            {{ t("pages.placeholder.hide-during-rush-mode") }}
          </div>
          <div
            style="cursor: pointer"
            @click="onChangeRushMode"
            class="form-check form-switch"
          >
            <input
              class="form-check-input"
              type="checkbox"
              v-model="hide_in_rush"
            />
          </div>
        </div>
      </div>
      <button
        type="button"
        class="btn-block-option"
        @click="onMove(props.indexing, -1)"
      >
        <e-icon name="angles-up" />
      </button>
      <button
        type="button"
        class="btn-block-option"
        @click="onMove(props.indexing, 1)"
      >
        <e-icon name="angles-down" />
      </button>
      <button
        type="button"
        class="btn-block-option"
        @click="onRemoveCustomFood(props.indexing)"
      >
        <e-icon name="trash-can" />
      </button>
    </template>

    <div>
      <div class="mb-4">
        <label class="form-label" for="val-title"
          >{{ t("pages.products.fields.titles")
          }}<span class="text-danger">*</span></label
        >
        <input
          type="text"
          id="val-title"
          class="form-control"
          v-model="title"
          :class="{
            'is-invalid': props.isCheckTitle && !title && !!blur,
          }"
          :placeholder="t('pages.products.labels.placeholder_title')"
          :disabled="disabledForm"
        />
        <div
          v-if="props.isCheckTitle && !title && !!blur"
          class="invalid-feedback animated fadeIn"
        >
          {{ t("pages.products.labels.required_title") }}
        </div>
      </div>
      <div class="mb-4">
        <label class="form-label" for="val-description">{{
          t("pages.products.labels.description")
        }}</label>
        <input
          type="text"
          id="val-description"
          class="form-control"
          v-model="description"
          :placeholder="t('pages.products.labels.placeholder_description')"
          :disabled="disabledForm"
        />
      </div>
      <div class="table-responsive">
        <table class="table table-bordered table-vcenter">
          <thead>
            <tr>
              <th>{{ t("pages.products.labels.constituents") }}</th>
              <th style="width: 100px">
                {{ t("pages.products.labels.arrangement") }}
              </th>
              <th>{{ t("pages.products.labels.takeaway_inc_tax") }}</th>
              <th>{{ t("pages.products.labels.tax_takeaway") }}</th>
              <th>{{ t("pages.products.labels.dinein_inc_tax") }}</th>
              <th>{{ t("pages.products.labels.tax_dine") }}</th>
              <th>{{ t("pages.products.labels.unit") }}</th>
              <th class="text-center" style="width: 100px">
                {{ t("pages.products.labels.action") }}
              </th>
            </tr>
          </thead>
          <DndProvider :backend="HTML5Backend">
            <tbody>
              <ProductsDndTableRow
                v-for="(item, index) in items"
                :key="item?.id"
                :id="item?.id"
                :index="index"
                :move-card="moveCard"
              >
                <td class="fs-sm fw-semibold">
                  {{ item?.name }}
                </td>
                <td class="fs-sm fw-semibold">
                  {{ index + 1 }}
                </td>
                <td class="fs-sm">
                  <input
                    v-if="
                      item.takeaway_ordinary_price !==
                      item.takeaway_inc_vat_price
                    "
                    type="number"
                    class="form-control fs-base"
                    min="0"
                    step="0.01"
                    :placeholder="
                      item.takeaway_inc_vat_price ||
                      item.takeaway_ordinary_price ||
                      item?.takeaway_inc_vat_price_v2
                    "
                    v-model="item.takeaway_inc_vat_price"
                    :disabled="disabledForm"
                  />
                  <input
                    v-else
                    type="number"
                    class="form-control fs-base"
                    min="0"
                    step="0.01"
                    :placeholder="
                      item.takeaway_inc_vat_price ||
                      item.takeaway_ordinary_price ||
                      item?.takeaway_inc_vat_price_v2
                    "
                    value="null"
                    :disabled="disabledForm"
                    @input="changeValueTakeAway($event.target.value, item.id)"
                  />
                </td>
                <td>{{ item.takeaway_vat_rate }}%</td>
                <td class="fs-sm">
                  <input
                    v-if="
                      item.dinein_inc_vat_price !== item.price &&
                      item.dinein_inc_vat_price !== null
                    "
                    type="number"
                    class="form-control fs-base"
                    min="0"
                    step="0.01"
                    :placeholder="
                      item.dinein_inc_vat_price ||
                      Number(item?.price) ||
                      item?.ordinary_price ||
                      '0'
                    "
                    v-model="item.dinein_inc_vat_price"
                    :disabled="disabledForm"
                  />
                  <input
                    v-else
                    type="number"
                    class="form-control fs-base"
                    min="0"
                    step="0.01"
                    :placeholder="
                      item.dinein_inc_vat_price ||
                      Number(item?.price) ||
                      item?.ordinary_price ||
                      '0'
                    "
                    value="null"
                    :disabled="disabledForm"
                    @input="changeValuePrice($event.target.value, item.id)"
                  />
                </td>
                <td>{{ item.vat_rate }}%</td>
                <td class="fs-sm">
                  {{ item?.unit?.name }}
                </td>
                <td class="text-center">
                  <button
                    type="button"
                    class="btn btn-sm btn-alt-secondary"
                    @click="onRemoveItem(index, `${item?.id}-${item?.type}`)"
                    :disabled="disabledForm"
                  >
                    <e-icon name="trash-can" />
                  </button>
                </td>
              </ProductsDndTableRow>
            </tbody>
          </DndProvider>
        </table>
      </div>
      <div class="d-flex justify-content-end pb-3" v-if="!disabledForm">
        <e-button
          @click="openModal"
          type="light"
          :bs-target="`#modal-list-topping-${props.indexing}`"
          bs-toggle="modal"
          >{{ t("pages.products.labels.search_to_add") }}</e-button
        >
      </div>
    </div>

    <e-modal
      :id="`modal-list-topping-${props.indexing}`"
      :title="t('pages.products.labels.select_constituents')"
      style="--bs-modal-width: 800px"
      @confirm="onSubmitSelectedTopping"
    >
      <Dataset
        ref="Datasett"
        v-slot="{ ds }"
        :ds-data="listToppings"
        :ds-search-in="isNameSearch ? ['name'] : ['topping_group', 'category']"
      >
        <div class="row" :data-page-count="ds.dsPagecount">
          <div style="display: flex; gap: 25px" class="col-md-12 py-2 flex">
            <input
              v-model="nameSearch"
              @input="searchName"
              :placeholder="t('pages.products.labels.search')"
              class="form-control"
            />
            <input
              v-model="groupSearch"
              @input="searchGroup"
              :placeholder="t('pages.products.labels.search_topping')"
              class="form-control"
            />
          </div>
        </div>
        <hr />
        <div class="row" v-if="listToppings?.length">
          <div class="col-md-12">
            <div class="table-responsive">
              <table class="table mb-0">
                <thead>
                  <tr>
                    <th
                      v-for="th in cols"
                      :key="th.field"
                      :class="['sort', th.sort]"
                    >
                      {{ th.name }}
                    </th>
                    <th style="width: 80px"></th>
                  </tr>
                </thead>
                <DatasetItem tag="tbody" class="fs-sm">
                  <template #default="{ row }">
                    <tr>
                      <td>{{ row.name }}</td>
                      <td>{{ row?.takeaway_ordinary_price || 0 }}</td>
                      <td>{{ row?.ordinary_price || row?.price }}</td>
                      <td>{{ row?.topping_group }}</td>
                      <td>{{ row?.category }}</td>
                      <td>{{ row?.unit?.name }}</td>
                      <td>{{ row?.type }}</td>
                      <td>
                        <input
                          class="form-check-input"
                          type="checkbox"
                          @change="test(row)"
                          :value="row?._id"
                          :id="row?._id"
                          v-model="selectedToppingId"
                        />
                      </td>
                    </tr>
                  </template>
                </DatasetItem>
              </table>
            </div>
          </div>
        </div>
        <EListEmpty v-else />
        <div
          class="d-flex flex-md-row flex-column justify-content-between align-items-center"
        >
          <DatasetInfo class="py-3 fs-sm" />
          <DatasetPager class="flex-wrap py-3 fs-sm" />
        </div>
      </Dataset>
    </e-modal>
  </BaseBlock>
</template>
<style scoped>
.sw-container {
  display: flex;
  align-items: center;
  margin-right: 10px;
}
</style>
