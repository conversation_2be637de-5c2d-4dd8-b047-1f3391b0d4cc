# Pos-Merchant

A modern Vue.js 3 based Point of Sale (POS) system for merchants.

## System Requirements

- Node.js >= 16.x
- npm >= 7.x
- Modern web browser (Chrome, Firefox, Safari, Edge)

## Environment Setup

The project uses environment variables for configuration. Create a `.env` file in the root directory with the following variables:

```env
VITE_DEFAULT_LOCALE=
VITE_FALLBACK_LOCALE=
VITE_SUPPORTED_LOCALES=
VITE_API_MERCHANT=

```

Note: All environment variables must be prefixed with `VITE_` to be exposed to the client-side code in Vite.

## Project Setup

1. Clone the repository:

```bash
git clone [repository-url]
cd Pos-Merchant
```

2. Install dependencies:

```bash
yarn install
```

3. Start the development server:

```bash
yarn dev
```

4. Build for production:

```bash
yarn build
```

5. Preview production build:

```bash
yarn preview
```

## Project Information

- **Framework**: Vue.js 3
- **Build Tool**: Vite
- **State Management**: Pinia
- **UI Framework**: Element Plus
- **Version**: 2.6.0

## Key Dependencies

### Core Dependencies

- Vue 3
- Vue Router 4
- Pinia (State Management)
- Element Plus (UI Framework)
- Bootstrap 5
- Axios (HTTP Client)

### UI Components & Features

- CKEditor 5 (Rich Text Editor)
- FullCalendar (Calendar Component)
- Chart.js (Data Visualization)
- Vue Select (Advanced Select Component)
- Vue Cropper (Image Cropping)
- Dropzone (File Upload)
- SweetAlert2 (Beautiful Alerts)

### Development Dependencies

- Vite
- ESLint
- SASS

## Browser Support

The project supports modern browsers including:

- Chrome >= 60
- Firefox >= 60
- Safari >= 12
- iOS >= 12
- Edge (Latest)

## Additional Features

- Internationalization (i18n) support
- Form validation with Vuelidate
- Date/time handling with Day.js and Moment.js
- Lightbox for images
- Star rating component
- Drag and drop functionality
- Progress indicators
- Color picker
- Slider components

## Code Quality

The project uses ESLint for code quality and consistency. To run linting:

```bash
yarn lint
```

## Project Structure

```
Pos-Merchant/
├── public/                 # Static files
├── src/
│   ├── assets/            # Global assets (images, fonts, etc.)
│   │   ├── images/
│   │   ├── fonts/
│   │   └── styles/
│   ├── components/        # Reusable Vue components
│   │   ├── common/       # Common components
│   │   ├── layout/       # Layout components
│   │   └── ui/           # UI components
│   ├── composables/      # Vue composables
│   ├── config/           # Configuration files
│   ├── locales/          # i18n translation files
│   ├── router/           # Vue Router configuration
│   ├── services/         # API services
│   ├── store/            # Pinia stores
│   │   ├── modules/     # Store modules
│   │   └── index.js     # Store setup
│   ├── utils/            # Utility functions
│   ├── views/            # Page components
│   │   ├── auth/        # Authentication pages
│   │   ├── dashboard/   # Dashboard pages
│   │   └── settings/    # Settings pages
│   ├── App.vue          # Root component
│   └── main.js          # Application entry point
├── .env                  # Environment variables
├── .eslintrc.js         # ESLint configuration
├── package.json         # Project dependencies
└── vite.config.js       # Vite configuration
```

### Key Directories Explained

- **public/**: Static files that are copied directly to the build output
- **src/assets/**: Global assets like images, fonts, and styles
- **src/components/**: Reusable Vue components organized by type
- **src/composables/**: Vue 3 composables for shared logic
- **src/config/**: Configuration files for different environments
- **src/locales/**: Internationalization files for multiple languages
- **src/router/**: Vue Router configuration and route definitions
- **src/services/**: API service layer for backend communication
- **src/store/**: Pinia store modules for state management
- **src/utils/**: Utility functions and helpers
- **src/views/**: Page components organized by feature

## Deployment

1. Navigate to the project directory:

```bash
cd /var/www/html/Pos-Merchant
```

2. Pull the latest changes:

```bash
git pull
```

3. Build the project:

```bash
yarn build
```
