<script setup>
import { reactive, computed, onMounted, ref, watch } from "vue";
import {
  Dataset,
  DatasetItem,
  DatasetInfo,
  DatasetSearch,
  DatasetShow,
} from "vue-dataset";
import { printerLogService } from "@/services/printerLog.service";
import EListEmpty from "@/components/Elements/EListEmpty.vue";
import { useTemplateStore } from "@/stores/template";
import useNotify from "@/composables/useNotify";
import dayjs from "dayjs";
import { useI18n } from "vue-i18n";
import { storeData } from "@/stores/storeData";

const store = useTemplateStore();
const dataFetch = storeData();
const { setNotify } = useNotify();
const { t, locale } = useI18n();
// Helper variables
const cols = reactive([
  {
    name: t("pages.printer_logs.fields.id"),
    field: "id",
    sort: "",
  },
  {
    name: t("pages.printer_logs.fields.job_id"),
    field: "job_id",
    sort: "",
  },
  {
    name: t("pages.printer_logs.fields.order_id"),
    field: "order_id",
    sort: "",
  },
  {
    name: t("pages.printer_logs.fields.created_at"),
    field: "created_at",
    sort: "",
  },
  {
    name: t("pages.printer_logs.fields.printer"),
    field: "printer",
    sort: "",
  },
]);

const updateCols = () => {
  cols[0].name = t("pages.printer_logs.fields.id");
  cols[1].name = t("pages.printer_logs.fields.job_id");
  cols[2].name = t("pages.printer_logs.fields.order_id");
  cols[3].name = t("pages.printer_logs.fields.created_at");
  cols[4].name = t("pages.printer_logs.fields.printer");
};

// Watch for language changes
watch(locale, () => {
  updateCols();
});

// Sort by functionality
const sortBy = computed(() => {
  return cols.reduce((acc, o) => {
    if (o.sort) {
      o.sort === "asc" ? acc.push(o.field) : acc.push("-" + o.field);
    }
    return acc;
  }, []);
});

// On sort th click
// function onSort(event, i) {
//   let toset
//   const sortEl = cols[i]

//   if (!event.shiftKey) {
//     cols.forEach((o) => {
//       if (o.field !== sortEl.field) {
//         o.sort = ''
//       }
//     })
//   }

//   if (!sortEl.sort) {
//     toset = 'asc'
//   }

//   if (sortEl.sort === 'desc') {
//     toset = event.shiftKey ? '' : 'asc'
//   }

//   if (sortEl.sort === 'asc') {
//     toset = 'desc'
//   }

//   sortEl.sort = toset
// }

const listPrinterLogs = ref([]);

const limit = ref(10);
const currentPage = ref(1);
const total = ref();
const visible = ref(true);

const onFetchList = async () => {
  try {
    store.pageLoader({ mode: "on" });
    const response = await printerLogService.getList({
      page: currentPage.value,
      limit: limit.value,
    });

    if (!response?.error) {
      listPrinterLogs.value = response.data?.data || [];
      total.value = response.data.total;
    }
    store.pageLoader({ mode: "off" });
  } catch (error) {
    setNotify({
      title: "Error",
      message: error?.message,
    });
    store.pageLoader({ mode: "off" });
  }
};

watch(limit, async () => {
  currentPage.value = 1;
  await onFetchList();
});

// Apply a few Bootstrap 5 optimizations
onMounted(async () => {
  if (dataFetch.storeData?.["merchant-printer-logs-list"]?.length > 0) {
    await (listPrinterLogs.value =
      dataFetch.storeData?.["merchant-printer-logs-list"]);
    total.value = dataFetch.total?.["merchant-printer-logs-list"];
    store.pageLoader({ mode: "off" });
  } else {
    await onFetchList();
  }

  // Remove labels from
  document.querySelectorAll("#datasetLength label").forEach((el) => {
    el.remove();
  });

  // Replace select classes
  let selectLength = document.querySelector("#datasetLength select");

  if (selectLength) {
    selectLength.classList = "";
    selectLength.classList.add("form-select");
    selectLength.style.width = "80px";
  }
});
</script>

<template>
  <BasePageHeading :title="t('pages.printer_logs.name')">
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">Manages</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">List printer logs</li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <BaseBlock :title="t('pages.printer_logs.name')">
      <Dataset
        v-slot="{ ds }"
        :ds-data="listPrinterLogs"
        :ds-sortby="sortBy"
        :ds-search-in="['id', 'job_id', 'order_id']"
      >
        <div class="row" :data-page-count="ds.dsPagecount">
          <div id="datasetLength" class="col-md-8 py-2">
            <DatasetShow v-show="false" :dsShowEntries="100" />
            <div class="form-inline">
              <select class="form-select" style="width: 80px" v-model="limit">
                <option :value="5">5</option>
                <option :value="10">10</option>
                <option :value="25">25</option>
                <option :value="50">50</option>
                <option :value="100">100</option>
              </select>
            </div>
          </div>
          <div class="col-md-4 py-2">
            <DatasetSearch
              :ds-search-placeholder="
                t('pages.printer_logs.placeholder.search')
              "
            />
          </div>
        </div>
        <hr />
        <div class="row" v-if="listPrinterLogs?.length">
          <div class="col-md-12">
            <div class="table-responsive">
              <table class="table mb-0">
                <thead>
                  <tr>
                    <th v-for="th in cols" :key="th.field">
                      {{ th.name }}
                    </th>
                  </tr>
                </thead>
                <DatasetItem tag="tbody" class="fs-sm">
                  <template #default="{ row }">
                    <tr>
                      <td style="min-width: 50px">{{ row.id }}</td>
                      <td style="min-width: 150px">{{ row.job_id }}</td>
                      <td style="min-width: 150px">{{ row.order_id }}</td>
                      <td style="min-width: 150px">
                        {{
                          dayjs(row.created_at).format("DD-MM-YYYY HH:mm:ss")
                        }}
                      </td>
                      <td style="min-width: 50px">{{ row.printer?.name }}</td>
                    </tr>
                  </template>
                </DatasetItem>
              </table>
            </div>
          </div>
        </div>
        <EListEmpty v-else />
        <div
          class="d-flex flex-md-row flex-column justify-content-between align-items-center"
        >
          <DatasetInfo class="py-3 fs-sm" />
          <el-pagination
            v-if="visible"
            v-model:current-page="currentPage"
            @current-change="onFetchList"
            background
            v-model:page-size="limit"
            layout="prev, pager, next"
            :prev-text="t('pages.footer.previous')"
            :next-text="t('pages.footer.next')"
            :total="total"
          />
        </div>
      </Dataset>
    </BaseBlock>
  </div>
</template>

<style lang="scss" scoped>
.gg-select {
  box-sizing: border-box;
  position: relative;
  display: block;
  transform: scale(1);
  width: 22px;
  height: 22px;
}
.gg-select::after,
.gg-select::before {
  content: "";
  display: block;
  box-sizing: border-box;
  position: absolute;
  width: 8px;
  height: 8px;
  left: 7px;
  transform: rotate(-45deg);
}
.gg-select::before {
  border-left: 2px solid;
  border-bottom: 2px solid;
  bottom: 4px;
  opacity: 0.3;
}
.gg-select::after {
  border-right: 2px solid;
  border-top: 2px solid;
  top: 4px;
  opacity: 0.3;
}
th.sort {
  cursor: pointer;
  user-select: none;
  &.asc {
    .gg-select::after {
      opacity: 1;
    }
  }
  &.desc {
    .gg-select::before {
      opacity: 1;
    }
  }
}
</style>
