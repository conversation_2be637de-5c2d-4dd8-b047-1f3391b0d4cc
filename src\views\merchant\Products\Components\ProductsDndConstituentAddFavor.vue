<script setup>
import { DndProvider } from "vue3-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import EIcon from "@/components/Elements/EIcon.vue";
import ProductsDndTableRow from "@/views/merchant/Products/Components/ProductsDndTableRow.vue";
import { reactive, ref } from "vue";
import EButton from "@/components/Elements/EButton.vue";
import { Dataset, DatasetInfo, DatasetItem, DatasetPager } from "vue-dataset";
import EModal from "@/components/Elements/EModal.vue";
import EListEmpty from "@/components/Elements/EListEmpty.vue";
import { productService } from "@/services/product.service";
import { debounce, sortBy } from "lodash";

const title = defineModel("title");
const description = defineModel("description");
const items = defineModel("items");
const props = defineProps({
  indexing: Number,
});
const moveCard = (dragIndex, hoverIndex) => {
  const item = items.value[dragIndex];
  items.value.splice(dragIndex, 1);
  items.value.splice(hoverIndex, 0, item);
};
const onRemoveItem = (index, _id) => {
  items.value.splice(index, 1);
  selectedToppingId.value = selectedToppingId.value.filter(
    (item) => item !== _id
  );
};
const cols = reactive([
  {
    name: "Name",
    field: "name",
    sort: "",
  },
  {
    name: "Price",
    field: "price",
    sort: "",
  },
  {
    name: "Unit",
    field: "unit",
    sort: "",
  },
  {
    name: "Type",
    field: "type",
    sort: "",
  },
]);
const listToppings = ref([]);
const onFetchListToppings = async () => {
  const response = await productService.getListConstituents(query);
  if (!response?.error) {
    const data =
      response.data?.map((item) => ({
        ...item,
        _id: `${item.id}-${item.type}`,
      })) || [];
    listToppings.value = sortBy(data, (item) => {
      const parsedName = item?.name?.replace(/(\d+)/g, (match) => {
        return match.padStart(10, "0"); // Pad numbers with leading zeros for correct sorting
      });
      return parsedName.toLowerCase();
    });
  }
};
const openModal = async () => {
  await onFetchListToppings({
    search: searchKey.value,
  });
};
let toppingSelected = items.value.map((item) => `${item.id}-${item.type}`);

const selectedToppingId = ref([...toppingSelected]);
const onSubmitSelectedTopping = () => {
  items.value = selectedToppingId.value.map((val) => {
    const selectedItem = listToppings.value.find((topping) => {
      return topping?._id === val;
    });
    return {
      ...selectedItem,
      max_quantity: null,
      _type: null,
    };
  });
  searchKey.value = "";
};
const emit = defineEmits(["remove", "move"]);
const onRemoveCustomFood = (index) => {
  emit("remove", index);
};
const searchKey = ref("");
const onSearchConstituents = debounce(() => {
  if (searchKey.value) {
    onFetchListToppings({
      search: searchKey.value,
    });
  }
}, 800);
const onMove = (index, step) => {
  if (index === 0 && step === -1) return;
  return emit("move", index, step);
};
</script>

<template>
  <BaseBlock
    title="Possible additional ingredients and options"
    header-class="bg-secondary"
    bordered
    themed
  >
    <template #options>
      <button
        type="button"
        class="btn-block-option"
        @click="onMove(props.indexing, -1)"
      >
        <e-icon name="angles-up" />
      </button>
      <button
        type="button"
        class="btn-block-option"
        @click="onMove(props.indexing, 1)"
      >
        <e-icon name="angles-down" />
      </button>
      <button
        type="button"
        class="btn-block-option"
        @click="onRemoveCustomFood(props.indexing)"
      >
        <e-icon name="trash-can" />
      </button>
    </template>

    <div>
      <div class="mb-4">
        <label class="form-label" for="val-title">Title</label>
        <input
          type="text"
          id="val-title"
          class="form-control"
          v-model="title"
          placeholder="Enter title..."
        />
      </div>
      <div class="mb-4">
        <label class="form-label" for="val-description">Description</label>
        <input
          type="text"
          id="val-description"
          class="form-control"
          v-model="description"
          placeholder="Enter description..."
        />
      </div>
      <div class="table-responsive">
        <table class="table table-bordered table-vcenter">
          <thead>
            <tr>
              <th>Constituents</th>
              <th style="width: 100px">Arrangement</th>
              <th>Price</th>
              <th>Unit</th>
              <th class="text-center" style="width: 100px">Action</th>
            </tr>
          </thead>
          <DndProvider :backend="HTML5Backend">
            <tbody>
              <ProductsDndTableRow
                v-for="(item, index) in items"
                :key="item?.id"
                :id="item?.id"
                :index="index"
                :move-card="moveCard"
              >
                <td class="fs-sm fw-semibold">
                  {{ item?.name }}
                </td>
                <td class="fs-sm fw-semibold">
                  {{ index + 1 }}
                </td>
                <td class="fs-sm">
                  {{ item?.price || item?.ordinary_price || 0 }}
                </td>
                <td class="fs-sm">
                  {{ item?.unit?.name }}
                </td>
                <td class="text-center">
                  <button
                    type="button"
                    class="btn btn-sm btn-alt-secondary"
                    @click="onRemoveItem(index, `${item?.id}-${item?.type}`)"
                  >
                    <e-icon name="trash-can" />
                  </button>
                </td>
              </ProductsDndTableRow>
            </tbody>
          </DndProvider>
        </table>
      </div>
      <div class="d-flex justify-content-end pb-3">
        <e-button
          @click="openModal"
          type="light"
          :bs-target="`#modal-list-topping-${props.indexing}`"
          bs-toggle="modal"
          >Search to add</e-button
        >
      </div>
    </div>

    <e-modal
      :id="`modal-list-topping-${props.indexing}`"
      title="Select constituents"
      style="--bs-modal-width: 800px"
      @confirm="onSubmitSelectedTopping"
    >
      <Dataset v-slot="{ ds }" :ds-data="listToppings" :ds-search-in="['name']">
        <div class="row" :data-page-count="ds.dsPagecount">
          <div class="col-md-4 py-2">
            <input
              type="text"
              placeholder="Search..."
              class="form-control"
              @keyup="onSearchConstituents"
              v-model="searchKey"
            />
          </div>
        </div>
        <hr />
        <div class="row" v-if="listToppings?.length">
          <div class="col-md-12">
            <div class="table-responsive">
              <table class="table mb-0">
                <thead>
                  <tr>
                    <th
                      v-for="th in cols"
                      :key="th.field"
                      :class="['sort', th.sort]"
                    >
                      {{ th.name }}
                    </th>
                    <th style="width: 80px"></th>
                  </tr>
                </thead>
                <DatasetItem tag="tbody" class="fs-sm">
                  <template #default="{ row }">
                    <tr>
                      <td>{{ row.name }}</td>
                      <td>{{ row?.price || row?.ordinary_price || 0 }}</td>
                      <td>{{ row?.unit?.name }}</td>
                      <td>{{ row?.type }}</td>
                      <td>
                        <input
                          class="form-check-input"
                          type="checkbox"
                          :value="row?._id"
                          :id="row?._id"
                          v-model="selectedToppingId"
                        />
                      </td>
                    </tr>
                  </template>
                </DatasetItem>
              </table>
            </div>
          </div>
        </div>
        <EListEmpty v-else />
        <div
          class="d-flex flex-md-row flex-column justify-content-between align-items-center"
        >
          <DatasetInfo class="py-3 fs-sm" />
          <DatasetPager class="flex-wrap py-3 fs-sm" />
        </div>
      </Dataset>
    </e-modal>
  </BaseBlock>
</template>

<style scoped></style>
