<script setup>
import EIcon from "@/components/Elements/EIcon.vue";
import { useRoute } from "vue-router";
import { reactive, computed, ref, onMounted } from "vue";
import useVuelidate from "@vuelidate/core";
import { required, minLength, numeric } from "@vuelidate/validators";
import { tableService } from "@/services/table.service";
import { useTemplateStore } from "@/stores/template";
import useNotify from "@/composables/useNotify";
import useAppRouter from "@/composables/useRouter";
import { useI18n } from "vue-i18n";
import { storeData } from "@/stores/storeData";
import { scrollTo } from "@/stores/scollItemInlist";

const store = useTemplateStore();
const scrollStore = scrollTo();
const dataFetch = storeData();
const route = useRoute();
const { t } = useI18n();
const router = useAppRouter();
const { setNotify } = useNotify();
const typeSubmit = ref();

const props = route.params;
const id = props?.id;

const table = ref();

let state = reactive({
  name: null,
});

const rules = computed(() => {
  return {
    name: {
      minLength: minLength(3),
    },
    table_id: {
      required,
      numeric,
    },
  };
});

let v$ = useVuelidate(rules, state);

async function onSubmit() {
  try {
    const result = await v$.value.$validate();

    if (!result) return;
    store.pageLoader({ mode: "on" });

    if (id) {
      await tableService.update(id, state);
    } else {
      await tableService.create(state);
    }
    if (typeSubmit.value === "confirm") {
      scrollStore.getElement(id);
      dataFetch.setData([], "merchant-tables-list");
      dataFetch.setTotal(0, "merchant-tables-list");
      await router.pushByName({ name: "merchant-tables-list" });
    } else {
      state.name = null;
      state.table_id = null;
      v$.value.$reset();
    }
    store.pageLoader({ mode: "off" });
    setNotify({
      title: "Success",
      message: id ? "Table update success" : "Table create success",
      type: "success",
    });
  } catch (e) {
    setNotify({
      title: "Error",
      message: e?.message,
    });
    store.pageLoader({ mode: "off" });
  }
}

const apiGetItem = async () => {
  try {
    store.pageLoader({ mode: "on" });
    const response = await tableService.get(id);
    table.value = response.data;

    state = reactive({
      name: response.data.name,
      table_id: response.data.table_id,
    });

    v$ = useVuelidate(rules, state);
    store.pageLoader({ mode: "off" });
  } catch (error) {
    console.log(error);
    store.pageLoader({ mode: "off" });
  }
};

onMounted(async () => {
  try {
    if (id) apiGetItem();
  } catch (error) {
    console.error("Error fetching data:", error);
  }
});

const handleSubmit = (type) => {
  typeSubmit.value = type;
};
</script>

<template>
  <BasePageHeading
    :title="id ? t('pages.tables.titles.update') : t('pages.tables.titles.add')"
    :go-back="true"
    :subtitle="t('pages.tables.labels.label_head_list')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.tables.labels.manages")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <router-link to="/merchant/tables">{{
              t("pages.tables.titles.list")
            }}</router-link>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <span>{{
              id
                ? t("pages.tables.titles.update")
                : t("pages.tables.titles.create")
            }}</span>
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <div class="row justify-content-center">
      <div class="col-sm-12 col-md-8">
        <form @submit.prevent="onSubmit">
          <BaseBlock
            :title="
              id
                ? t('pages.tables.titles.detail', { item: table?.id })
                : t('pages.tables.titles.create')
            "
          >
            <template #options>
              <e-icon
                @click="
                  () => {
                    router.back();
                    dataFetch.setData([], 'merchant-tables-list');
                    dataFetch.setTotal(0, 'merchant-tables-list');
                  }
                "
                name="arrow-left"
                role="button"
                class="icon_arrow_left"
              />
            </template>

            <div class="row justify-content-center py-sm-3 py-md-5">
              <div class="col-sm-10 col-md-8">
                <div class="mb-4">
                  <label class="form-label" for="block-form-name">{{
                    t("pages.tables.fields.table_name")
                  }}</label>
                  <input
                    type="text"
                    class="form-control"
                    id="block-form-name"
                    name="block-form-name"
                    :placeholder="t('pages.tables.placeholder.enter_name')"
                    :class="{
                      'is-invalid': v$.name.$errors.length,
                    }"
                    v-model="state.name"
                    @blur="v$.name.$touch"
                  />
                  <div
                    v-if="v$.name.$errors.length"
                    class="invalid-feedback animated fadeIn"
                  >
                    Min length table name 3
                  </div>
                </div>
                <div class="mb-4">
                  <label class="form-label" for="block-form-table-id"
                    >{{ t("pages.tables.fields.table_id")
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    type="number"
                    class="form-control"
                    id="block-form-table-id"
                    name="block-form-table-id"
                    placeholder="Enter id table.."
                    :class="{
                      'is-invalid': v$.table_id.$errors.length,
                    }"
                    v-model="state.table_id"
                    @blur="v$.table_id.$touch"
                  />
                  <div
                    v-if="v$.table_id.$errors.length"
                    class="invalid-feedback animated fadeIn"
                  >
                    Please enter id table
                  </div>
                </div>
                <div class="mb-4" v-if="table?.qr_code">
                  <label class="form-label" style="display: block">{{
                    t("pages.tables.fields.qr_code")
                  }}</label>
                  <img width="120" height="120" :src="table?.qr_code" />
                </div>
                <div
                  class="my-4"
                  :style="{
                    textAlign: 'end',
                    display: 'flex',
                    gap: '5px',
                    justifyContent: 'end',
                  }"
                >
                  <button
                    type="submit"
                    @click="handleSubmit('confirm')"
                    class="btn btn-sm btn-primary"
                    :style="{ color: '#fff' }"
                  >
                    {{ t("buttons.confirm") }}
                  </button>
                  <button
                    type="submit"
                    v-if="!id"
                    class="btn btn-sm btn-primary"
                    @click="handleSubmit('confirm_add')"
                    :style="{ color: '#fff' }"
                  >
                    {{ t("pages.products.buttons.confirm_add") }}
                  </button>
                </div>
              </div>
            </div>
          </BaseBlock>
        </form>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.icon_arrow_left {
  background-color: rgb(243, 235, 235);
  border-radius: 100%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
