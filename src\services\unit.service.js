import { http } from './Base/base.service'

export const unitService = {
  async getList(query) {
    return await http.get('/units', {
      params: query
    })
  },

  async get(id) {
    return await http.get('/units/' + id)
  },

  async create(data) {
    return await http.post('/units', data)
  },

  async update(id, data) {
    return await http.patch('/units/' + id, data)
  },

  async delete(id) {
    return await http.delete('/units/' + id)
  }
}
