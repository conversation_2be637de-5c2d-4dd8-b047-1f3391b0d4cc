<script setup>
import { reactive, onMounted, ref, watch } from "vue";
import { Dataset, DatasetItem, DatasetInfo, DatasetShow } from "vue-dataset";
import { useDebounceFn } from "@vueuse/core";
import { transactionService } from "@/services/transaction.service";
import EListEmpty from "@/components/Elements/EListEmpty.vue";
import { useTemplateStore } from "@/stores/template";
import { useI18n } from "vue-i18n";
import { storeData } from "@/stores/storeData";

const currentPage = ref(1);
const totalPage = ref(1);
const limit = ref(10);
const total = ref();
const search = ref();
const store = useTemplateStore();
const dataFetch = storeData();
const { t, locale } = useI18n();
// Helper variables
const cols = reactive([
  {
    name: t("pages.transaction.fields.id"),
    field: "id",
  },
  {
    name: t("pages.transaction.fields.payment_ref"),
    field: "payment_ref",
  },
  {
    name: t("pages.transaction.fields.method"),
    field: "method",
  },
  {
    name: t("pages.transaction.fields.ammount"),
    field: "amount",
  },
  {
    name: t("pages.transaction.fields.sid"),
    field: "sid",
  },
  {
    name: t("pages.transaction.fields.order"),
    field: "order",
  },
  {
    name: t("pages.transaction.fields.status"),
    field: "status",
  },
]);
const updateCols = () => {
  cols[0].name = t("pages.transaction.fields.id");
  cols[1].name = t("pages.transaction.fields.payment_ref");
  cols[2].name = t("pages.transaction.fields.method");
  cols[3].name = t("pages.transaction.fields.ammount");
  cols[4].name = t("pages.transaction.fields.sid");
  cols[5].name = t("pages.transaction.fields.order");
  cols[6].name = t("pages.transaction.fields.status");
};

// Watch for language changes
watch(locale, () => {
  updateCols();
});

const listTransactions = ref([]);

const onFetchList = async () => {
  try {
    store.pageLoader({ mode: "on" });
    const response = await transactionService.getList({
      limit: limit.value,
      search: search?.value,
      page: currentPage.value,
    });

    if (!response?.error) {
      const newTables = response.data?.data.map((item) => ({
        ...item,
        bgColor: item.status === "completed" ? "#D8EDD9" : "#FFBDB8",
      }));
      total.value = response.data.total;
      listTransactions.value = newTables || [];
      totalPage.value = response.data.last_page;
      console.log(totalPage.value);
    }
    store.pageLoader({ mode: "off" });
  } catch (error) {
    console.log(error);
    store.pageLoader({ mode: "off" });
  }
};
const handleChangeSearch = useDebounceFn((e) => {
  search.value = e?.target?.value;
  onFetchList();
}, 500);
const visible = ref(true);
watch(limit, async () => {
  currentPage.value = 1;
  await onFetchList();
});

// Apply a few Bootstrap 5 optimizations
onMounted(async () => {
  if (dataFetch.storeData?.["merchant-transactions-list"]?.length > 0) {
    const newTables = dataFetch.storeData?.["merchant-transactions-list"].map(
      (item) => ({
        ...item,
        bgColor: item.status === "completed" ? "#D8EDD9" : "#FFBDB8",
      })
    );
    await (listTransactions.value = newTables);
    total.value = dataFetch.total?.["merchant-transactions-list"];
    store.pageLoader({ mode: "off" });
  } else {
    await onFetchList();
  }

  // Remove labels from
  document.querySelectorAll("#datasetLength label").forEach((el) => {
    el.remove();
  });

  // Replace select classes
  let selectLength = document.querySelector("#datasetLength select");

  if (selectLength) {
    selectLength.classList = "";
    selectLength.classList.add("form-select");
    selectLength.style.width = "80px";
  }
});
</script>

<template>
  <BasePageHeading
    :title="t('pages.transaction.name')"
    :subtitle="t('pages.transaction.label.label_head_list')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.transaction.label.manage")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            {{ t("pages.transaction.titles.list") }}
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <BaseBlock :title="t('pages.transaction.name')">
      <Dataset
        v-slot="{ ds }"
        :ds-data="listTransactions"
        :ds-search-in="[
          'id',
          'payment_ref',
          'method',
          'amount',
          'sid',
          'order',
          'status',
        ]"
      >
        <div class="row" :data-page-count="ds.dsPagecount">
          <div id="datasetLength" class="col-md-8 py-2">
            <DatasetShow v-show="false" :dsShowEntries="100" />
            <div class="form-inline">
              <select class="form-select" style="width: 80px" v-model="limit">
                <option :value="5">5</option>
                <option :value="10">10</option>
                <option :value="25">25</option>
                <option :value="50">50</option>
                <option :value="100">100</option>
              </select>
            </div>
          </div>
          <div class="col-md-4 py-2">
            <input
              type="text"
              class="form-control"
              id="form-name"
              :placeholder="t('pages.placeholder.enter')"
              @input="handleChangeSearch"
            />
          </div>
        </div>
        <hr />
        <div class="row" v-if="listTransactions?.length">
          <div class="col-md-12">
            <div class="table-responsive">
              <table class="table mb-0">
                <thead>
                  <tr>
                    <th v-for="th in cols" :key="th.field">
                      {{ th.name }}
                    </th>
                  </tr>
                </thead>
                <DatasetItem tag="tbody" class="fs-sm">
                  <template #default="{ row }">
                    <tr>
                      <td
                        :style="{
                          minWidth: '80px',
                          backgroundColor: row.bgColor,
                        }"
                      >
                        {{ row.id }}
                      </td>
                      <td
                        :style="{
                          minWidth: '180px',
                          backgroundColor: row.bgColor,
                        }"
                      >
                        {{ row.payment_ref }}
                      </td>
                      <td
                        :style="{
                          minWidth: '150px',
                          backgroundColor: row.bgColor,
                        }"
                      >
                        {{ row.payment_gateway }}
                      </td>
                      <td
                        :style="{
                          minWidth: '150px',
                          backgroundColor: row.bgColor,
                        }"
                        v-if="row?.order"
                      >
                        {{ row.order?.amount_total }}
                      </td>
                      <td
                        :style="{
                          minWidth: '150px',
                          backgroundColor: row.bgColor,
                        }"
                        v-else-if="row?.refund"
                      >
                        -{{ row.refund?.amount_total }}
                      </td>
                      <td
                        :style="{
                          minWidth: '150px',
                          backgroundColor: row.bgColor,
                        }"
                      >
                        {{ row.sid }}
                      </td>
                      <td
                        v-if="row?.order"
                        :style="{ backgroundColor: row.bgColor }"
                      >
                        {{ row.order?.code_show }}
                      </td>
                      <td
                        v-else-if="row?.refund"
                        :style="{ backgroundColor: row.bgColor }"
                      >
                        {{ row.refund?.code_show }}
                      </td>
                      <td
                        :style="{
                          minWidth: '120px',
                          backgroundColor: row.bgColor,
                        }"
                      >
                        {{ row.status }}
                      </td>
                    </tr>
                  </template>
                </DatasetItem>
              </table>
            </div>
          </div>
        </div>
        <EListEmpty v-else />
        <div
          class="d-flex flex-md-row flex-column justify-content-between align-items-center"
        >
          <DatasetInfo class="py-3 fs-sm" />
          <el-pagination
            v-if="visible"
            v-model:current-page="currentPage"
            @current-change="onFetchList"
            background
            v-model:page-size="limit"
            layout="prev, pager, next"
            :prev-text="t('pages.footer.previous')"
            :next-text="t('pages.footer.next')"
            :total="total"
          />
        </div>
      </Dataset>
    </BaseBlock>
  </div>
</template>

<style lang="scss" scoped>
.gg-select {
  box-sizing: border-box;
  position: relative;
  display: block;
  transform: scale(1);
  width: 22px;
  height: 22px;
}
.gg-select::after,
.gg-select::before {
  content: "";
  display: block;
  box-sizing: border-box;
  position: absolute;
  width: 8px;
  height: 8px;
  left: 7px;
  transform: rotate(-45deg);
}
.gg-select::before {
  border-left: 2px solid;
  border-bottom: 2px solid;
  bottom: 4px;
  opacity: 0.3;
}
.gg-select::after {
  border-right: 2px solid;
  border-top: 2px solid;
  top: 4px;
  opacity: 0.3;
}
th.sort {
  cursor: pointer;
  user-select: none;
  &.asc {
    .gg-select::after {
      opacity: 1;
    }
  }
  &.desc {
    .gg-select::before {
      opacity: 1;
    }
  }
}
</style>
