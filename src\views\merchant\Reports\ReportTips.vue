<script setup>
import { reactive, onMounted, ref, computed } from "vue";
import { Dataset, DatasetItem, DatasetInfo, DatasetShow } from "vue-dataset";
import { reportTipService } from "@/services/report_tip.service";
import EListEmpty from "@/components/Elements/EListEmpty.vue";
import { useTemplateStore } from "@/stores/template";
import FlatPickr from "vue-flatpickr-component";
import moment from "moment";
import { watch } from "vue";
import { useI18n } from "vue-i18n";
import { tableService } from "@/services/table.service";
import { kioskService } from "@/services/kiosk.service";
import VueSelect from "vue-select";
import EButton from "@/components/Elements/EButton.vue";

const store = useTemplateStore();
const { t, locale } = useI18n();

// Helper variables
const cols = reactive([
  {
    name: t("pages.report.fields.date"),
    field: "product",
    sort: "",
  },
  {
    name: t("pages.report.fields.payment_method"),
    field: "type",
    sort: "",
  },
  {
    name: t("pages.report.fields.source"),
    field: "sales",
    sort: "",
  },
  {
    name: t("pages.report.fields.amount"),
    field: "unit_price",
    sort: "",
  },
]);

const dynamicCols = computed(() => {
  // if (listTipReports.value.length > 0) {
  //   return [
  //     ...cols,
  //     {
  //       name: t("pages.report.fields.tips"),
  //       field: "tips",
  //       sort: "",
  //     },
  //   ];
  // }
  return cols;
});

const updateCols = () => {
  cols[0].name = t("pages.report.fields.date");
  cols[1].name = t("pages.report.fields.payment_method");
  cols[2].name = t("pages.report.fields.source");
  cols[3].name = t("pages.report.fields.amount");
};

// Watch for language changes
watch(locale, () => {
  updateCols();
});

const listTipReports = ref([]);
const priceTips = ref(0);
const listPriceTips = ref([]);
const limit = ref(10);
const currentPage = ref(1);
const total = ref();
const visible = ref(true);
const dateRange = ref(defaultDateRange());
const select = ref(null);
const listCategories = ref({ listKiosk: [], listTable: [] });
const handleChange = async (val) => {
  if (val?.length === 2) {
    await onFetchList();
  }
};
const configRange = ref({
  mode: "range",
  dateFormat: "d-m-Y",
  onChange: handleChange,
  locale: { firstDayOfWeek: 1 },
});

const onFetchList = async () => {
  let start_date;
  let end_date;
  let idKiosk;
  let idTable;
  const [startDateString, endDateString] = dateRange.value.split(" to ");
  const dates = [];
  start_date = moment(startDateString, "DD-MM-YYYY").toISOString();
  end_date = endDateString
    ? moment(endDateString, "DD-MM-YYYY").endOf("day").toISOString()
    : moment(startDateString, "DD-MM-YYYY").endOf("day").toISOString();
  let currentDate = moment(startDateString, "DD-MM-YYYY").clone();
  while (
    currentDate.isSameOrBefore(
      moment(endDateString || startDateString, "DD-MM-YYYY"),
      "day"
    )
  ) {
    dates.push(currentDate.format("DD-MM-YYYY"));
    currentDate.add(1, "day");
  }
  store.pageLoader({ mode: "on" });
  if (listCategories.value.listTable?.length) {
    listCategories.value.listTable?.forEach((itm) => {
      if (itm?.id === select.value) {
        idKiosk = undefined;
        idTable = itm.id;
      }
    });
  }
  if (listCategories.value.listKiosk?.length) {
    listCategories.value.listKiosk?.forEach((itm) => {
      // console.log(itm);
      if (itm?.id === select.value) {
        idTable = undefined;
        idKiosk = itm.id;
      }
    });
  }
  const response = await reportTipService.getList({
    start_date: start_date,
    end_date: end_date,
    kiosk_id: idKiosk,
    table_id: idTable,
    page: currentPage.value,
    limit: limit.value,
  });
  // console.log("response", response);
  if (!response?.error) {
    listTipReports.value = response?.data?.reports?.data || [];
    total.value = response?.data?.reports?.total;
    priceTips.value = response?.data?.total;
    listPriceTips.value = response?.data?.total_7_days || [];
  }
  store.pageLoader({ mode: "off" });
};

async function handleExportPDF() {
  let start_date;
  let end_date;
  const [startDateString, endDateString] = dateRange.value.split(" to ");
  start_date = moment(startDateString, "YYYY-MM-DD");
  end_date = moment(endDateString, "YYYY-MM-DD");
  const response = await reportTipService.exportPDF({
    start_date: start_date?._i,
    end_date: end_date?._i,
  });
  const blod = new Blob([response], { type: "application/pdf" });
  const url = URL.createObjectURL(blod);
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", "export_data_tip.pdf");
  link.click();
}

onMounted(async () => {
  await onFetchList();
  document.querySelectorAll("#datasetLength label").forEach((el) => {
    el.remove();
  });
  let selectLength = document.querySelector("#datasetLength select");
  if (selectLength) {
    selectLength.classList = "";
    selectLength.classList.add("form-select");
    selectLength.style.width = "80px";
  }
});

watch(limit, async () => {
  currentPage.value = 1;
  await onFetchList();
});

watch(dateRange, async () => {
  if (dateRange?.value?.length === 2) {
    await onFetchList();
  }
});

watch(select, async () => {
  await onFetchList();
});

function defaultDateRange() {
  const startDate = moment().startOf("isoWeek").format("DD-MM-YYYY");
  const endDate = moment().endOf("isoWeek").format("DD-MM-YYYY");
  return `${startDate} to ${endDate}`;
}

async function onFetchTable() {
  const response = await tableService.getList();
  if (!response?.error) {
    listCategories.value.listTable = response?.data?.data || [];
  }
}

async function onFetchKiosk() {
  const response = await kioskService.getList();
  if (!response?.error) {
    listCategories.value.listKiosk = response?.data?.data || [];
  }
}

async function handleChangeSelect(e) {
  select.value = e.target.value;
}

onMounted(async () => {
  store.pageLoader({ mode: "on" });
  await Promise.all([onFetchTable(), onFetchKiosk()]);
  store.pageLoader({ mode: "off" });
});

// ✅ Tính số ngày trong khoảng
const isSevenDaysRange = computed(() => {
  if (!dateRange.value) return false;
  const [startDateString, endDateString] = dateRange.value.split(" to ");
  if (!endDateString) return false;

  const start = moment(startDateString, "DD-MM-YYYY");
  const end = moment(endDateString, "DD-MM-YYYY");
  const diffDays = end.diff(start, "days") + 1; // +1 để tính cả ngày đầu
  return diffDays === 7;
});
</script>

<template>
  <BasePageHeading
    :title="t('pages.report.titles.tip_report')"
    :subtitle="t('pages.report.labels.label_head_list_good')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.report.labels.manages")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            {{ t("pages.report.titles.goods_report") }}
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>
  <div class="content">
    <div class="d-flex justify-content-end items-center mb-4">
      <e-button type="info" size="sm" @click="() => handleExportPDF()">
        {{ t("pages.report.labels.btn_pdf") }}
      </e-button>
    </div>
    <BaseBlock :title="t('pages.report.titles.tip_report')">
      <Dataset
        v-slot="{ ds }"
        :ds-data="listTipReports"
        :ds-search-in="['id', 'name']"
      >
        <div class="row" :data-page-count="ds.dsPagecount">
          <div id="datasetLength" class="col py-2">
            <DatasetShow v-show="false" :dsShowEntries="100" />
            <div class="form-inline">
              <select class="form-select" style="width: 80px" v-model="limit">
                <option :value="5">5</option>
                <option :value="10">10</option>
                <option :value="25">25</option>
                <option :value="50">50</option>
                <option :value="100">100</option>
              </select>
            </div>
          </div>
          <div class="col py-2">
            <FlatPickr
              class="form-control"
              id="example-flatpickr-range"
              :placeholder="t('pages.report.placeholder.date_range')"
              :config="configRange"
              v-model="dateRange"
            />
          </div>
          <div class="col py-2">
            <VueSelect
              v-model="select"
              :options="[
                ...listCategories.listKiosk,
                ...listCategories.listTable,
              ]"
              label="name"
              :reduce="(item_name) => item_name.id"
              :placeholder="t('pages.placeholder.source')"
              @change="(val) => handleChangeSelect(val)"
            />
          </div>
          <!--          <div class="col py-2">-->
          <!--            <VueSelect-->
          <!--                v-model="selectTable"-->
          <!--                :options="listCategories.listTable"-->
          <!--                label="name"-->
          <!--                placeholder="Select table..."-->
          <!--                :reduce="item_name => item_name.id"-->
          <!--                @change="(val) => handleChangeSelect(val, 'table')"-->
          <!--            />-->
          <!--          </div>-->
        </div>
        <hr />
        <div class="row" v-if="listTipReports?.length">
          <div class="col-md-12">
            <div class="table-responsive">
              <table class="table table-striped mb-0">
                <thead>
                  <tr>
                    <th
                      v-for="th in dynamicCols"
                      :key="th.field"
                      :class="['sort', th.sort]"
                    >
                      {{ th.name }} <i class="gg-select float-end"></i>
                    </th>
                  </tr>
                </thead>
                <DatasetItem tag="tbody" class="fs-sm">
                  <template #default="{ row }">
                    <tr>
                      <td>
                        {{ moment(row?.date).format("DD/MM/YYYY HH:mm:ss") }}
                      </td>
                      <td>
                        {{ row.payment_method }}
                      </td>
                      <td>
                        {{ row?.kiosk?.name || row?.table?.name }}
                      </td>
                      <td>{{ row.tip }}</td>
                      <td>{{ row.tips }}</td>
                    </tr>
                  </template>
                </DatasetItem>
                <tfoot>
                  <tr>
                    <td style="font-weight: bold">Total Tips</td>
                    <td></td>
                    <td></td>
                    <td>{{ priceTips }}</td>
                  </tr>
                  <tr v-if="Object.keys(listPriceTips).length && isSevenDaysRange">
                    <td style="font-weight: bold">Total Tips by Date</td>
                  </tr>
                  <tr v-for="(value, date) in listPriceTips" :key="date">
                    <td>{{ date }}</td>
                    <td></td>
                    <td></td>
                    <td>{{ value }}</td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        </div>
        <EListEmpty v-else />
        <div
          class="d-flex flex-md-row flex-column justify-content-between align-items-center"
        >
          <DatasetInfo class="py-3 fs-sm" />
          <el-pagination
            v-if="visible"
            v-model:current-page="currentPage"
            @current-change="onFetchList"
            background
            v-model:page-size="limit"
            layout="prev, pager, next"
            :prev-text="t('pages.footer.previous')"
            :next-text="t('pages.footer.next')"
            :total="total"
          />
        </div>
      </Dataset>
    </BaseBlock>
  </div>
</template>

<style lang="scss">
@import "vue-select/dist/vue-select.css";
@import "@/assets/scss/vendor/vue-select";
@import "flatpickr/dist/flatpickr.css";
@import "@/assets/scss/vendor/flatpickr";
tfoot > tr > td {
  font-weight: bold;
}
</style>
