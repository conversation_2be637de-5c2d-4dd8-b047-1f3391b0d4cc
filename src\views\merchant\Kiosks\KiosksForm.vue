<script setup>
import EIcon from '@/components/Elements/EIcon.vue'
import useNotify from "@/composables/useNotify"
import useAppRouter from '@/composables/useRouter'
import { kioskService } from '@/services/kiosk.service'
import { printerService } from '@/services/printer.service'
import { useTemplateStore } from '@/stores/template'
import useVuelidate from '@vuelidate/core'
import { minLength, required } from '@vuelidate/validators'
import { computed, onMounted, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'

const store = useTemplateStore()

const route = useRoute()
const router = useAppRouter()
const {t} = useI18n();
const typeSubmit =  ref();
const props = route.params
const id = props?.id

const kiosk = ref()
const {setNotify}= useNotify()
const optionsPrint = ref([])

const onFetchLisPrinter = async () => {
  const response = await printerService.getList()
  if (!response?.error) {
    const newArray = response.data?.data.map((item) => ({
      value: item.id,
      text: item.name
    }))
    optionsPrint.value = [{ value: null, text: 'Please select' }, ...newArray]
  }
}

let state = reactive({
  name: null,
  kiosk_id: null,
  printer_id: null,
  type: 1
})

const rules = computed(() => {
  return {
    name: {
      required,
      minLength: minLength(3)
    },
    kiosk_id: {
      required
    },
    printer_id: {
      required
    },
    type: {
      required
    }
  }
})

let v$ = useVuelidate(rules, state)

async function onSubmit() {
  try {
    const result = await v$.value.$validate()

    if (!result) {
      return
    }
    store.pageLoader({ mode: 'on' })
    if (id) {
      await kioskService.update(id, state)
    } else {
      await kioskService.create(state)
    }
    if(typeSubmit.value === 'confirm'){
      router.pushByName({ name: 'merchant-kiosks-list' })
    }else {
      state.type = null;
      state.name = null;
      state.kiosk_id = null;
      state.type = 1;
      v$ = useVuelidate(rules, state)
    }
  } catch (e) {
    console.log(e)
    setNotify({
      title: 'Error',
      message: e?.message
    })
  } finally {
    store.pageLoader({ mode: 'off' })
  }
}

const apiGetPrinter = async () => {
  const response = await kioskService.get(id)
  kiosk.value = response.data

  state = reactive({
    name: response.data.name,
    kiosk_id: response.data.kiosk_id,
    printer_id: response.data.printer_id,
    type: response.data.type
  })

  v$ = useVuelidate(rules, state)
}

onMounted(async () => {
  try {
    store.pageLoader({ mode: 'on' })
    if (id) await apiGetPrinter()
    await onFetchLisPrinter()
    store.pageLoader({ mode: 'off' })
  } catch (error) {
    store.pageLoader({ mode: 'off' })
    console.error('Error fetching data:', error)
  }
})
const listKioskTypes = [
  {
    label: t('pages.kiosk.fields.dine_in'),
    value: 1
  },
  {
    label: t('pages.kiosk.fields.takeaway'),
    value: 2
  },
  {
    label: t('pages.kiosk.fields.dine_in_takeaway'),
    value: 3
  },
]

const handleSubmit = (type) => {
  typeSubmit.value = type;
}
</script>

<template>
  <BasePageHeading
    :title="id ? t('pages.kiosk.titles.update') : t('pages.kiosk.titles.create')"
    :go-back="true"
    :subtitle="t('pages.kiosk.labels.label_head_list')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{ t('pages.kiosk.labels.manages') }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <router-link to="/merchant/kiosks">{{ t('pages.kiosk.titles.list') }}</router-link>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <span>{{ id ? t('pages.kiosk.titles.update') : t('pages.kiosk.titles.create') }}</span>
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <div class="row justify-content-start">
      <div class="col-sm-12 col-md-8">
        <form @submit.prevent="onSubmit">
          <BaseBlock :title="id ? t('pages.kiosk.titles.detail', { item: kiosk?.name}) : t('pages.kiosk.titles.create')">
            <template #options>
              <e-icon
                @click="() => router.back()"
                name="arrow-left"
                role="button"
                class="icon_arrow_left"
              />
            </template>

            <div class="row justify-content-center py-sm-3 py-md-5">
              <div class="col-sm-10 col-md-8">
                <div class="mb-4">
                  <label class="form-label" for="block-form-name"
                    >{{ t('pages.kiosk.fields.kiosk_name') }}<span class="text-danger">*</span></label
                  >
                  <input
                    type="text"
                    class="form-control"
                    id="block-form-name"
                    name="block-form-name"
                    placeholder="Enter kiosk name.."
                    :class="{
                      'is-invalid': v$.name.$errors.length
                    }"
                    v-model="state.name"
                    @blur="v$.name.$touch"
                  />
                  <div v-if="v$.name.$errors.length" class="invalid-feedback animated fadeIn">
                    Please enter name kiosk
                  </div>
                </div>
                <div class="mb-4">
                  <label class="form-label" for="block-form-table-id"
                    >{{ t('pages.kiosk.fields.kiosk_id_field') }}<span class="text-danger">*</span></label
                  >
                  <input
                    type="text"
                    class="form-control"
                    id="block-form-table-id"
                    name="block-form-table-id"
                    placeholder="Enter id kiosk.."
                    :class="{
                      'is-invalid': v$.kiosk_id.$errors.length
                    }"
                    v-model="state.kiosk_id"
                    @blur="v$.kiosk_id.$touch"
                  />
                  <div v-if="v$.kiosk_id.$errors.length" class="invalid-feedback animated fadeIn">
                    Please enter id kiosk
                  </div>
                </div>

                <div class="mb-4">
                  <label class="form-label" for="val-print-id"
                    >{{ t('pages.kiosk.fields.receipt_printer') }}<span class="text-danger">*</span></label
                  >
                  <select
                    id="val-print-id"
                    class="form-select"
                    :class="{
                      'is-invalid': v$.printer_id.$errors.length
                    }"
                    v-model="state.printer_id"
                    @blur="v$.printer_id.$touch"
                  >
                    <option
                      v-for="(print, index) in optionsPrint"
                      :value="print.value"
                      :key="`print-${index}`"
                    >
                      {{ print.text }}
                    </option>
                  </select>
                  <div v-if="v$.printer_id.$errors.length" class="invalid-feedback animated fadeIn">
                    Please select a print id!
                  </div>
                </div>

                <div class="mb-4">
                  <label class="form-label" for="val-type"
                  >{{ t('pages.kiosk.fields.type') }}<span class="text-danger">*</span></label
                  >
                  <select
                      id="val-type"
                      class="form-select"
                      :class="{
                      'is-invalid': v$.type.$errors.length
                    }"
                      v-model="state.type"
                      @blur="v$.type.$touch"
                  >
                    <option
                        v-for="(type, index) in listKioskTypes"
                        :value="type.value"
                        :key="`type-${index}`"
                    >
                      {{ type.label }}
                    </option>
                  </select>
                  <div v-if="v$.printer_id.$errors.length" class="invalid-feedback animated fadeIn">
                    Please select a print id!
                  </div>
                </div>

                <div class="my-4" :style="{ textAlign: 'end', display: 'flex', gap: '5px', justifyContent: 'end' }">
                  <button type="submit" class="btn btn-sm btn-primary" @click="handleSubmit('confirm')" :style="{ color: '#fff' }">
                    {{ t('buttons.confirm') }}
                  </button>
                  <button v-if="!id" type="submit" class="btn btn-sm btn-primary" @click="handleSubmit('confirm_add')" :style="{ color: '#fff' }">
                    {{ t('pages.products.buttons.confirm_add') }}
                  </button>
                </div>
              </div>
            </div>
          </BaseBlock>
        </form>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.icon_arrow_left {
  background-color: rgb(243, 235, 235);
  border-radius: 100%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
