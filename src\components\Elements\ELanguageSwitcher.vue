<template>
  <select @change="switchLanguage" class="form-select">
    <option
        v-for="sLocale in supportedLocales"
        :key="sLocale.value"
        :value="sLocale.value"
        :selected="locale === sLocale.value"
    >
      {{ sLocale.label }}
    </option>
  </select>
</template>
<script lang="js" setup>
import { useI18n } from 'vue-i18n'
import Trans from '@/i18n/i18nUtils'
import useAppRouter from "@/composables/useRouter"
import { onMounted } from 'vue';

const { locale } = useI18n()
const router = useAppRouter()
const supportedLocales = [
  {
    label: 'En',
    value: 'en',
  },
  {
    label: 'Nor',
    value: 'nor',
  },
]
onMounted(async () => {
  const lang = localStorage.getItem('lang')
    await Trans.switchLanguage(lang)
    try {
    await router.replace({ params: { locale: lang === Trans.defaultLocale ? '' : lang } })
  } catch (e) {
    await router.replaceByPath('/')
  }
})
const switchLanguage = async (event) => {
  if (!event) return
  const target = event.target
  const newLocale = target.value
  localStorage.setItem('lang', target.value)
  await Trans.switchLanguage(newLocale)
  try {
    await router.replace({ params: { locale: newLocale === Trans.defaultLocale ? '' : newLocale } })
  } catch (e) {
    await router.replaceByPath('/')
  }
}
</script>
