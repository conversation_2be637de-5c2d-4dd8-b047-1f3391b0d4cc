import { http } from "./Base/base.service";

export const reportOrderService = {
  async getList(query) {
    return await http.get("/report/orders", {
      params: query,
    });
  },
  
  async export(query) {
    return await http.get("/report/export-csv-orders", {
      params: query,
    });
  },

  async exportPDF(query) {
    return await http.get("/report/export-pdf-orders", {
      params: query,
      responseType: "blob"
    });
  }
};
