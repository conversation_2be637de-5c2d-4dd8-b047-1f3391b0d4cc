<script setup>
import EButton from "@/components/Elements/EButton.vue";
import EListEmpty from "@/components/Elements/EListEmpty.vue";
import EModal from "@/components/Elements/EModal.vue";
import { openingHourService } from "@/services/openingHour.service";
import { ordinariesService } from "@/services/ordinaries.service";
import { useTemplateStore } from "@/stores/template";
import OpeningHoursModal from "@/views/merchant/OpeningHours/OpeningHoursModal.vue";
import moment from "moment";
import { computed, onMounted, reactive, ref, watch } from "vue";
import { Dataset, DatasetInfo, DatasetItem, DatasetSearch } from "vue-dataset";
import {
  default as FlatPicker,
  default as FlatPickr,
} from "vue-flatpickr-component";
import { useI18n } from "vue-i18n";

const store = useTemplateStore();
const dateRange = ref(defaultDateRange());
const handleChange = async (val) => {
  if (val?.length === 2) {
    await onFetchList();
  }
};
const configRange = ref({
  mode: "range",
  dateFormat: "d-m-Y",
  locale: { firstDayOfWeek: 1 },
  onChange: handleChange,
});

const { t, locale } = useI18n();
// Helper variables
const cols = reactive([
  {
    name: t("pages.opening_hours.fields.date"),
    field: "date",
    sort: "",
  },
  {
    name: t("pages.opening_hours.fields.day"),
    field: "day",
    sort: "",
  },
  {
    name: t("pages.opening_hours.fields.open_at"),
    field: "open_hour",
    sort: "",
  },
  {
    name: t("pages.opening_hours.fields.close_at"),
    field: "close_hour",
    sort: "",
  },
  {
    name: t("pages.opening_hours.fields.closed"),
    field: "is_closed",
    sort: "",
  },
]);
const updateCols = () => {
  cols[0].name = t("pages.opening_hours.fields.date");
  cols[1].name = t("pages.opening_hours.fields.day");
  cols[2].name = t("pages.opening_hours.fields.open_at");
  cols[3].name = t("pages.opening_hours.fields.close_at");
  cols[4].name = t("pages.opening_hours.fields.closed");
};

// Watch for language changes
watch(locale, () => {
  updateCols();
});

// Sort by functionality
const sortBy = computed(() => {
  return cols.reduce((acc, o) => {
    if (o.sort) {
      o.sort === "asc" ? acc.push(o.field) : acc.push("-" + o.field);
    }
    return acc;
  }, []);
});

// On sort th click
function onSort(event, i) {
  let toset;
  const sortEl = cols[i];

  if (!event.shiftKey) {
    cols.forEach((o) => {
      if (o.field !== sortEl.field) {
        o.sort = "";
      }
    });
  }

  if (!sortEl.sort) {
    toset = "asc";
  }

  if (sortEl.sort === "desc") {
    toset = event.shiftKey ? "" : "asc";
  }

  if (sortEl.sort === "asc") {
    toset = "desc";
  }

  sortEl.sort = toset;
}

function defaultDateRange() {
  const startDate = moment().startOf("isoWeek").format("DD-MM-YYYY");
  const endDate = moment().endOf("isoWeek").format("DD-MM-YYYY");
  return `${startDate} to ${endDate}`;
}
const listOpeningHours = ref([]);
const listOrdinaries = ref([]);

const flatPickerState = reactive({
  configTimeStandalone: {
    enableTime: true,
    noCalendar: true,
    dateFormat: "H:i",
    time_24hr: true,
  },
});

const limit = ref(10);
const currentPage = ref(1);
const total = ref();
const visible = ref(true);

const onFetchList = async () => {
  try {
    let start_date;
    let end_date;
    const dates = [];
    const dateArray = dateRange.value.split(" to ");
    if (dateArray?.length === 2) {
      start_date = moment(dateArray[0], "DD-MM-YYYY");
      end_date = moment(dateArray[1], "DD-MM-YYYY");
      let currentDate = moment(dateArray[0], "YYYY-MM-DD").clone();
      while (
        currentDate.isSameOrBefore(moment(dateArray[1], "YYYY-MM-DD"), "day")
      ) {
        dates.push(currentDate.format("YYYY-MM-DD"));
        currentDate.add(1, "day");
      }
    } else {
      start_date = moment(dateArray[0], "DD-MM-YYYY");
      end_date = moment(dateArray[0], "DD-MM-YYYY");
      let currentDate = moment(dateArray[0], "YYYY-MM-DD").clone();
      while (
        currentDate.isSameOrBefore(moment(dateArray[0], "YYYY-MM-DD"), "day")
      ) {
        dates.push(currentDate.format("YYYY-MM-DD"));
        currentDate.add(1, "day");
      }
    }

    store.pageLoader({ mode: "on" });
    const response = await openingHourService.getList({
      start_date: moment(start_date?._i, "DD-MM-YYYY").format("YYYY-MM-DD"),
      end_date: moment(end_date?._i, "DD-MM-YYYY").format("YYYY-MM-DD"),
      limit: limit.value,
      page: currentPage.value,
    });
    const data = response?.data?.data.map((item) => ({
      ...item,
      day: new Date(item.date).toLocaleDateString("en-US", { weekday: "long" }),
    }));
    listOpeningHours.value =
      data.sort((a, b) => new Date(a.date) - new Date(b.date)) || [];
    total.value = response.data.total;
    store.pageLoader({ mode: "off" });
  } catch (error) {
    console.log(error);
    store.pageLoader({ mode: "off" });
  }
};

watch(limit, async () => {
  currentPage.value = 1;
  await onFetchList();
});

const onFetchListOrdinaries = async () => {
  try {
    store.pageLoader({ mode: "on" });
    const response = await ordinariesService.getList();
    listOrdinaries.value =
      response?.data?.data.sort((a, b) => new Date(a.id) - new Date(b.id)) ||
      [];
    store.pageLoader({ mode: "off" });
  } catch (error) {
    console.log(error);
    store.pageLoader({ mode: "off" });
  }
};

const apiUpdateOrdinaries = async () => {
  try {
    store.pageLoader({ mode: "on" });
    const data = {};
    listOrdinaries.value.forEach(
      (item) =>
        item.open_hour &&
        item.close_hour &&
        (data[item.id] = {
          open_hour: moment(item.open_hour, "HH:mm:ss").format("HH:mm"),
          close_hour: moment(item.close_hour, "HH:mm:ss").format("HH:mm"),
          is_closed: item.is_closed,
        })
    );
    await ordinariesService.update(data);
    await onFetchList();
    store.pageLoader({ mode: "off" });
  } catch (error) {
    console.log(error);
    store.pageLoader({ mode: "off" });
  }
};

const handleReset = async (id) => {
  try {
    const response = await openingHourService.reset(id);
    if (!response?.data?.error) {
      const indexItemFind = listOpeningHours.value.findIndex((item) => {
        return item.id === id;
      });

      if (indexItemFind < 0) return;

      const itemOrdinaries = listOrdinaries.value.find(
        (item) => item.day === listOpeningHours.value[indexItemFind].day
      );
      if (!itemOrdinaries) return;

      listOpeningHours.value[indexItemFind] = {
        ...listOpeningHours.value[indexItemFind],
        ...itemOrdinaries,
      };
      listOpeningHours.value = [...listOpeningHours.value];
    }
  } catch (error) {
    console.log(error);
    store.pageLoader({ mode: "off" });
  }
};

const handleChangePicker = async (text, id, time) => {
  const indexItemFind = listOpeningHours.value.findIndex(
    (item) => item.id === id
  );
  if (indexItemFind < 0) return;

  const timeFormat = moment(time, "HH:mm:ss").format("HH:mm");
  const dataItem = listOpeningHours.value[indexItemFind];
  const dataUpdate =
    text === "open"
      ? {
          open_hour: timeFormat,
          close_hour: moment(dataItem.close_hour, "HH:mm:ss").format("HH:mm"),
          is_closed: dataItem.is_closed,
        }
      : {
          open_hour: moment(dataItem.open_hour, "HH:mm:ss").format("HH:mm"),
          is_closed: dataItem.is_closed,
          close_hour: timeFormat,
        };

  listOpeningHours.value[indexItemFind] = { ...dataUpdate, ...dataItem };
  listOpeningHours.value = [...listOpeningHours.value];
  await openingHourService.update(id, dataUpdate);
};

const handleChangeIsClosed = async (id, is_closed) => {
  const itemFind = listOpeningHours.value.find((item) => {
    return item.id === id;
  });

  if (!itemFind) return;

  await openingHourService.update(id, {
    open_hour: moment(itemFind.open_hour, "HH:mm:ss").format("HH:mm"),
    close_hour: moment(itemFind.close_hour, "HH:mm:ss").format("HH:mm"),
    is_closed,
  });
};

onMounted(async () => {
  await onFetchList();
  await onFetchListOrdinaries();

  // Remove labels from
  document.querySelectorAll("#datasetLength label").forEach((el) => {
    el.remove();
  });

  // Replace select classes
  let selectLength = document.querySelector("#datasetLength select");

  if (selectLength) {
    selectLength.classList = "";
    selectLength.classList.add("form-select");
    selectLength.style.width = "80px";
  }
});
</script>

<template>
  <BasePageHeading
    :title="t('pages.opening_hours.name')"
    :subtitle="t('pages.opening_hours.labels.label_head_list')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.opening_hours.labels.manage")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            {{ t("pages.opening_hours.labels.list") }}
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <BaseBlock :title="t('pages.opening_hours.name')">
      <Dataset
        v-slot="{ ds }"
        :ds-data="listOpeningHours"
        :ds-sortby="sortBy"
        :ds-search-in="['date', 'day', 'close_hour', 'open_hour', 'is_closed']"
      >
        <div
          class="row"
          :data-page-count="ds.dsPagecount"
          style="display: flex; justify-content: end"
        >
          <div
            class="col-md-4 py-2"
            style="display: flex; justify-content: end"
          >
            <EButton
              type="info"
              size="sm"
              data-bs-toggle="modal"
              data-bs-target="#modal-ordinaries"
            >
              {{ t("pages.opening_hours.labels.btn_config") }}
            </EButton>
          </div>
        </div>
        <div class="row" :data-page-count="ds.dsPagecount">
          <div id="datasetLength" class="col-md-4 py-2">
            <!-- <DatasetShow /> -->
          </div>
          <div class="col-md-4 py-2">
            <DatasetSearch
              :ds-search-placeholder="t('pages.placeholder.search')"
            />
          </div>
          <div class="col-md-4 py-2">
            <FlatPickr
              class="form-control"
              id="example-flatpickr-range"
              placeholder="Select Date Range"
              v-model="dateRange"
              :config="configRange"
            />
          </div>
        </div>
        <hr />
        <div class="row" v-if="listOpeningHours?.length">
          <div class="col-md-12">
            <div class="table-responsive">
              <table class="table mb-0">
                <thead>
                  <tr>
                    <th
                      v-for="(th, index) in cols"
                      :key="th.field"
                      :class="['sort', th.sort]"
                      @click="onSort($event, index)"
                    >
                      {{ th.name }} <i class="gg-select float-end"></i>
                    </th>
                    <th class="text-end" scope="col">
                      {{ t("pages.opening_hours.labels.action") }}
                    </th>
                  </tr>
                </thead>
                <DatasetItem tag="tbody" class="fs-sm">
                  <template #default="{ row }">
                    <tr>
                      <td style="min-width: 150px">
                        {{ moment(row.date).format("DD-MM-YYYY") }}
                      </td>
                      <td style="min-width: 150px">{{ row.day }}</td>
                      <td style="min-width: 150px">
                        <FlatPicker
                          id="flat-picker-time-standalone"
                          class="form-control"
                          v-model="row.open_hour"
                          :config="flatPickerState.configTimeStandalone"
                          style="width: 100px"
                          @on-change="
                            (e) => handleChangePicker('open', row.id, e[0])
                          "
                        />
                      </td>
                      <td style="min-width: 150px">
                        <FlatPicker
                          id="flat-picker-time-standalone"
                          class="form-control"
                          v-model="row.close_hour"
                          :config="flatPickerState.configTimeStandalone"
                          style="width: 100px"
                          @on-change="
                            (e) => handleChangePicker('close', row.id, e[0])
                          "
                        />
                      </td>
                      <td style="min-width: 150px">
                        <div class="form-check form-switch">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            :checked="row.is_closed"
                            v-model="row.is_closed"
                            @change="
                              () => handleChangeIsClosed(row.id, row.is_closed)
                            "
                          />
                        </div>
                      </td>
                      <td style="min-width: 50px" class="text-end">
                        <div class="form-check form-switch">
                          <EButton
                            type="info"
                            size="sm"
                            @click="() => handleReset(row.id)"
                            >Reset</EButton
                          >
                        </div>
                      </td>
                    </tr>
                  </template>
                </DatasetItem>
              </table>
            </div>
          </div>
        </div>
        <EListEmpty v-else />
        <div
          class="d-flex flex-md-row flex-column justify-content-between align-items-center"
        >
          <DatasetInfo class="py-3 fs-sm" />
          <el-pagination
            v-if="visible"
            v-model:current-page="currentPage"
            @current-change="onFetchList"
            background
            v-model:page-size="limit"
            layout="prev, pager, next"
            :prev-text="t('pages.footer.previous')"
            :next-text="t('pages.footer.next')"
            :total="total"
          />
        </div>
      </Dataset>
    </BaseBlock>
  </div>

  <EModal
    v-if="listOrdinaries.length"
    id="modal-ordinaries"
    :title="t('pages.opening_hours.labels.btn_config')"
    :ok-text="t('pages.opening_hours.labels.btn_confirm')"
    :cancel-text="t('pages.opening_hours.labels.btn_cancel')"
    @confirm="() => apiUpdateOrdinaries()"
  >
    <template v-slot:childrenComponent>
      <OpeningHoursModal :listOrdinaries="listOrdinaries" />
    </template>
  </EModal>
</template>

<style lang="scss">
@import "flatpickr/dist/flatpickr.css";
@import "@/assets/scss/vendor/flatpickr";
</style>

<style lang="scss" scoped>
.gg-select {
  box-sizing: border-box;
  position: relative;
  display: block;
  transform: scale(1);
  width: 22px;
  height: 22px;
}
.gg-select::after,
.gg-select::before {
  content: "";
  display: block;
  box-sizing: border-box;
  position: absolute;
  width: 8px;
  height: 8px;
  left: 7px;
  transform: rotate(-45deg);
}
.gg-select::before {
  border-left: 2px solid;
  border-bottom: 2px solid;
  bottom: 4px;
  opacity: 0.3;
}
.gg-select::after {
  border-right: 2px solid;
  border-top: 2px solid;
  top: 4px;
  opacity: 0.3;
}
th.sort {
  cursor: pointer;
  user-select: none;
  &.asc {
    .gg-select::after {
      opacity: 1;
    }
  }
  &.desc {
    .gg-select::before {
      opacity: 1;
    }
  }
}
</style>
