<script setup>
import { reactive, watch } from 'vue'
import { Dataset, DatasetItem } from 'vue-dataset'
import FlatPicker from 'vue-flatpickr-component'
import { useI18n } from 'vue-i18n';

const props = defineProps(['listOrdinaries'])
const {t, locale} = useI18n()

// Helper variables
const cols = reactive([
  {
    name: t('pages.opening_hours.fields.day'),
    field: 'day',
    sort: ''
  },
  {
    name: t('pages.opening_hours.fields.open_at'),
    field: 'open_hour',
    sort: ''
  },
  {
    name: t('pages.opening_hours.fields.close_at'),
    field: 'close_hour',
    sort: ''
  },
  {
    name: t('pages.opening_hours.fields.closed'),
    field: 'is_closed',
    sort: ''
  }
])

const updateCols = () => {
  cols[0].name = t('pages.opening_hours.fields.day');
  cols[1].name = t('pages.opening_hours.fields.open_at');
  cols[2].name = t('pages.opening_hours.fields.close_at');
  cols[3].name = t('pages.opening_hours.fields.closed');
};

// Watch for language changes
watch(locale, () => {
  updateCols();
});

const flatPickerState = reactive({
  configTimeStandalone: {
    enableTime: true,
    noCalendar: true,
    dateFormat: 'H:i',
    time_24hr: true
  }
})

const listOrdinaries = props.listOrdinaries
const handleChangePicker = (text, id, time) => {
  listOrdinaries.value = listOrdinaries.map((item) => {
    if (item.id === id) {
      if (text === 'open') return { ...item, open_hour: time }
      return { ...item, close_hour: time }
    }

    return item
  })
}
</script>

<template>
  <Dataset :ds-data="listOrdinaries">
    <div class="row">
      <div class="col-md-12">
        <div class="table-responsive">
          <table class="table mb-0">
            <thead>
              <tr>
                <th
                  v-for="(th, index) in cols"
                  :key="th.field"
                  :class="['sort', th.sort]"
                  @click="onSort($event, index)"
                >
                  {{ th.name }} <i class="gg-select float-end"></i>
                </th>
              </tr>
            </thead>
            <DatasetItem tag="tbody" class="fs-sm">
              <template #default="{ row }">
                <tr>
                  <td style="min-width: 100px">{{ row.day }}</td>
                  <td style="min-width: 100px">
                    <FlatPicker
                      id="flat-picker-time-standalone"
                      class="form-control"
                      v-model="row.open_hour"
                      :config="flatPickerState.configTimeStandalone"
                      style="width: 100px"
                      @on-change="(e) => handleChangePicker('open', row.id, e[0])"
                    />
                  </td>
                  <td style="min-width: 100px">
                    <FlatPicker
                      id="flat-picker-time-standalone"
                      class="form-control"
                      v-model="row.close_hour"
                      :config="flatPickerState.configTimeStandalone"
                      style="width: 100px"
                      @on-change="(e) => handleChangePicker('close', row.id, e[0])"
                    />
                  </td>
                  <td style="min-width: 50px">
                    <div class="form-check form-switch">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        :checked="row.is_closed"
                        v-model="row.is_closed"
                      />
                    </div>
                  </td>
                </tr>
              </template>
            </DatasetItem>
          </table>
        </div>
      </div>
    </div>
  </Dataset>
</template>

<style lang="scss">
@import 'flatpickr/dist/flatpickr.css';
@import '@/assets/scss/vendor/flatpickr';
</style>
