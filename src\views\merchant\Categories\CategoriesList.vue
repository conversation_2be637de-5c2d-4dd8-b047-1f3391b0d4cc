<script setup>
import useAppRouter from "@/composables/useRouter";
import { useI18n } from "vue-i18n";
import { computed, onMounted, reactive, ref, watch } from "vue";
import { DndProvider } from "vue3-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { useDebounceFn } from "@vueuse/core";
import CategoryDndSort from "./Components/CategoryDndSort.vue";
import { storeData } from "@/stores/storeData";
import { scrollTo } from "@/stores/scollItemInlist";
import { searchValue } from "@/stores/searchData";

let toast = Swal.mixin({
  buttonsStyling: false,
  target: "#page-container",
  customClass: {
    confirmButton: "btn btn-success m-1",
    cancelButton: "btn btn-danger m-1",
    input: "form-control",
  },
});

const { t, locale } = useI18n();
const store = useTemplateStore();
const scrollStore = scrollTo();
const searchStore = searchValue();
const router = useAppRouter();
const dataFetch = storeData();
const onNavForm = () => {
  router.pushByName({ name: "merchant-categories-form" });
};
const onNavEdit = (id) => {
  router.pushByPath(`/categories/form/${id}`);
};
import { Dataset, DatasetItem, DatasetInfo, DatasetShow } from "vue-dataset";

import EButton from "@/components/Elements/EButton.vue";
import EListEmpty from "@/components/Elements/EListEmpty.vue";
import { categoryService } from "@/services/category.service";
import Swal from "sweetalert2";
import { useTemplateStore } from "@/stores/template";
import ProductBelongToCategory from "./ProductBelongToCategory.vue";
import EModal from "@/components/Elements/EModal.vue";

const cols = reactive([
  {
    name: t("pages.categories.fields.id"),
    field: "id",
    sort: "",
  },
  {
    name: t("pages.categories.fields.priority"),
    field: "priority",
    sort: "",
  },
  {
    name: t("pages.categories.fields.category"),
    field: "name",
    sort: "",
  },
  {
    name: t("pages.categories.fields.printer"),
    field: "printer_name",
    sort: "",
  },
]);

const updateCols = () => {
  cols[0].name = t("pages.categories.fields.id");
  cols[1].name = t("pages.categories.fields.priority");
  cols[2].name = t("pages.categories.fields.category");
  cols[3].name = t("pages.categories.fields.printer");
};

// Watch for language changes
watch(locale, () => {
  updateCols();
});
const sortBy = computed(() => {
  return cols.reduce((acc, o) => {
    if (o.sort) {
      o.sort === "asc" ? acc.push(o.field) : acc.push("-" + o.field);
    }
    return acc;
  }, []);
});

// On sort th click
function onSort(event, i) {
  let toset;
  const sortEl = cols[i];

  if (!event.shiftKey) {
    cols.forEach((o) => {
      if (o.field !== sortEl.field) {
        o.sort = "";
      }
    });
  }

  if (!sortEl.sort) {
    toset = "asc";
  }

  if (sortEl.sort === "desc") {
    toset = event.shiftKey ? "" : "asc";
  }

  if (sortEl.sort === "asc") {
    toset = "desc";
  }

  sortEl.sort = toset;
}

const listCategories = ref([]);
const limit = ref(10);
const currentPage = ref(1);
const total = ref();
const visible = ref(true);

const onFetchList = async () => {
  store.pageLoader({ mode: "on" });
  scrollStore.setPage(currentPage.value);
  searchStore.setValueSearch(search?.value);
  const response = await categoryService.getList({
    page: currentPage.value,
    limit: limit.value,
    search: search?.value,
  });
  if (!response?.error) {
    listCategories.value = response.data?.data || [];
    total.value = response.data.meta.total;
  }
  store.pageLoader({ mode: "off" });
};
const handleChangeSearch = useDebounceFn((e) => {
  search.value = e?.target?.value;
  onFetchList();
}, 500);

onMounted(async () => {
  currentPage.value = scrollStore.page;
  search.value = searchStore.search;
  document.querySelectorAll("#datasetLength label").forEach((el) => {
    el.remove();
  });
  let selectLength = document.querySelector("#datasetLength select");
  selectLength.classList = "";
  selectLength.classList.add("form-select");
  selectLength.style.width = "80px";

  if (dataFetch.storeData?.["merchant-categories-list"]?.length > 0) {
    store.pageLoader({ mode: "on" });
    await (listCategories.value =
      dataFetch.storeData?.["merchant-categories-list"] || []);
    total.value = dataFetch.total?.["merchant-categories-list"];
    store.pageLoader({ mode: "off" });
  } else {
    await onFetchList();
  }
  if (scrollStore.formUpdateSuccess) {
    const item = document.querySelector(`.categoryItem-${scrollStore.idItem}`);
    item.scrollIntoView({
      behavior: "smooth",
      block: "start",
    });
    scrollStore.setUpdateSuccess();
  }
});

const onOpenDeleteConfirm = (id) => {
  toast
    .fire({
      title: "Are you sure?",
      text: "You will not be able to recover this category!",
      icon: "warning",
      showCancelButton: true,
      customClass: {
        confirmButton: "btn btn-danger m-1",
        cancelButton: "btn btn-info m-1",
      },
      confirmButtonText: "Yes, delete!",
      html: false,
      preConfirm: () => {
        return categoryService.delete(id);
      },
    })
    .then((result) => {
      console.log(result);
      if (result.value && !result.value?.error) {
        toast.fire("Deleted!", "Category has been deleted.", "success");
        onFetchList();
      } else if (result.dismiss === "cancel") {
        toast.fire("Cancelled", "Category is safe", "error");
      }
    })
    .catch((e) => {
      console.log(e);
      toast.fire("Error", e.message, "error");
    });
};

const idCategory = ref(null);
const handleClose = () => {
  idCategory.value = null;
};
const hideModal = () => {
  const backdrop = document.querySelector(".modal-backdrop");
  if (backdrop) {
    backdrop.parentNode.removeChild(backdrop);
    document.body.classList.remove("modal-open");
    document.body.removeAttribute("style");
  }
};

let debounceTimeout;

const handleUpdatePriority = async (data) => {
  await categoryService.updatePriority({ priorities: data });
  await onFetchList();
};

const moveCard = async (dragIndex, hoverIndex) => {
  const item = listCategories.value[dragIndex];
  listCategories.value.splice(dragIndex, 1);
  listCategories.value.splice(hoverIndex, 0, item);

  const listId = listCategories.value.map((item) => item.id);
  clearTimeout(debounceTimeout);
  debounceTimeout = setTimeout(() => {
    handleUpdatePriority(listId);
  }, 1200);
};
const search = ref();
watch(limit, async () => {
  currentPage.value = 1;
  await onFetchList();
});
</script>

<template>
  <BasePageHeading
    :title="t('pages.categories.titles.list')"
    :subtitle="t('pages.categories.labels.list_label')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.categories.labels.manages")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            {{ t("pages.categories.titles.list") }}
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <BaseBlock :title="t('pages.categories.titles.list')">
      <template #options>
        <e-button type="info" size="sm" @click="onNavForm"
          ><i class="fa fa-plus opacity-50 me-1"></i>
          {{ t("pages.categories.labels.btn_add") }}</e-button
        >
      </template>
      <Dataset
        v-slot="{ ds }"
        :ds-data="listCategories"
        :ds-sortby="sortBy"
        :ds-search-in="['name']"
      >
        <div>
          <div class="row" :data-page-count="ds.dsPagecount">
            <div id="datasetLength" class="col-md-8 py-2">
              <DatasetShow v-show="false" :dsShowEntries="100" />
              <div class="form-inline">
                <select class="form-select" style="width: 80px" v-model="limit">
                  <option :value="5">5</option>
                  <option :value="10">10</option>
                  <option :value="25">25</option>
                  <option :value="50">50</option>
                  <option :value="100">100</option>
                </select>
              </div>
            </div>
            <div class="col-md-4 py-2">
              <input
                v-model="search"
                type="text"
                class="form-control"
                id="form-name"
                :placeholder="t('pages.placeholder.enter')"
                @input="handleChangeSearch"
              />
            </div>
          </div>
          <hr />
          <div class="row" v-if="listCategories?.length">
            <div class="col-md-12">
              <div class="table-responsive">
                <table class="table table-striped mb-0">
                  <thead>
                    <tr>
                      <th
                        v-for="(th, index) in cols"
                        :key="th.field"
                        :class="['sort', th.sort]"
                        @click="onSort($event, index)"
                      >
                        {{ th.name }} <i class="gg-select float-end"></i>
                      </th>
                      <th style="text-align: right; width: 100px">
                        {{ t("pages.categories.fields.action") }}
                      </th>
                    </tr>
                  </thead>
                  <DndProvider :backend="HTML5Backend">
                    <DatasetItem tag="tbody" class="fs-sm">
                      <template #default="{ row, rowIndex }">
                        <CategoryDndSort
                          :id="row?.id"
                          :index="rowIndex"
                          :move-card="moveCard"
                          :class="`categoryItem-${row?.id}`"
                        >
                          <td style="width: 80px">{{ row.id }}</td>
                          <td style="width: 140px">{{ row.priority }}</td>
                          <td>
                            <button
                              @click="idCategory = row.id"
                              data-bs-toggle="modal"
                              data-bs-target="#modal-product"
                              data-target=".bd-example-modal-lg"
                              type="button"
                              class="btn btn-link"
                            >
                              {{ row.name }}
                            </button>
                          </td>
                          <td>{{ row.printer?.name }}</td>
                          <td class="text-end">
                            <div class="btn-group">
                              <button
                                type="button"
                                class="btn btn-sm btn-alt-secondary"
                                @click="onNavEdit(row?.id)"
                              >
                                <i class="fa fa-fw fa-pencil-alt"></i>
                              </button>
                              <button
                                type="button"
                                class="btn btn-sm btn-alt-secondary"
                                @click="onOpenDeleteConfirm(row.id)"
                              >
                                <i class="fa fa-fw fa-times"></i>
                              </button>
                            </div>
                          </td>
                        </CategoryDndSort>
                      </template>
                    </DatasetItem>
                  </DndProvider>
                </table>
              </div>
            </div>
          </div>
          <EListEmpty v-else />
          <div
            class="d-flex flex-md-row flex-column justify-content-between align-items-center"
          >
            <DatasetInfo class="py-3 fs-sm" />
            <el-pagination
              v-if="visible"
              v-model:current-page="currentPage"
              @current-change="onFetchList"
              background
              v-model:page-size="limit"
              layout="prev, pager, next"
              :prev-text="t('pages.footer.previous')"
              :next-text="t('pages.footer.next')"
              :total="total"
            />
          </div>
        </div>
      </Dataset>
    </BaseBlock>
  </div>
  <EModal
    id="modal-product"
    title="List Product"
    size="modal-xl"
    :hidden-button-ok="true"
    @close="handleClose"
  >
    <template v-slot:childrenComponent>
      <ProductBelongToCategory
        :idCategory="idCategory"
        :hideModal="hideModal"
      />
    </template>
  </EModal>
</template>
