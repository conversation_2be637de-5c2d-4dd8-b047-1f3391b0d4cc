<script setup>
import EButton from "@/components/Elements/EButton.vue";
import EListEmpty from "@/components/Elements/EListEmpty.vue";
import useNotify from "@/composables/useNotify";
import useAppRouter from "@/composables/useRouter";
import { toppingService } from "@/services/topping.service";
import { useTemplateStore } from "@/stores/template";
import { useDebounceFn } from "@vueuse/core";
import CountDown from "@/components/CountDown.vue";
import { scrollTo } from "@/stores/scollItemInlist";
import Swal from "sweetalert2";
import { computed, onMounted, reactive, ref, watch } from "vue";
import { Dataset, DatasetInfo, DatasetItem, DatasetShow } from "vue-dataset";
import { useI18n } from "vue-i18n";
import { useCookies } from "vue3-cookies";
import { EMPLOYEE } from "@/data/role";
import { storeData } from "@/stores/storeData";
import { searchValue } from "@/stores/searchData";

const { setNotify } = useNotify();
const store = useTemplateStore();
const searchStore = searchValue();
const dataFetch = storeData();
let toast = Swal.mixin({
  buttonsStyling: false,
  target: "#page-container",
  customClass: {
    confirmButton: "btn btn-success m-1",
    cancelButton: "btn btn-danger m-1",
    input: "form-control",
  },
});
const router = useAppRouter();
const scrollStore = scrollTo();
const search = ref();
const { t, locale } = useI18n();
// Helper variables
const cols = reactive([
  {
    name: t("pages.toppings.fields.id"),
    field: "id",
    sort: "",
  },
  {
    name: t("pages.toppings.fields.name"),
    field: "name",
    sort: "",
  },
  {
    name: t("pages.toppings.fields.topping_group"),
    field: "topping_group_id",
    sort: "",
  },
  {
    name: t("pages.toppings.fields.in_stock"),
    field: "in_stock",
    sort: "",
  },
  {
    name: t("pages.toppings.fields.in_kiosk"),
    field: "in_kiosk",
    sort: "",
  },
  {
    name: t("pages.toppings.fields.unit"),
    field: "unit_id",
    sort: "",
  },
  {
    name: t("pages.toppings.fields.suppliers"),
    field: "supplier_id",
    sort: "",
  },
  {
    name: t("pages.toppings.fields.producer"),
    field: "producer_id",
    sort: "",
  },
  {
    name: t("pages.toppings.fields.price"),
    field: "price",
    sort: "",
  },
]);

const updateCols = () => {
  cols[0].name = t("pages.toppings.fields.id");
  cols[1].name = t("pages.toppings.fields.name");
  cols[2].name = t("pages.toppings.fields.topping_group");
  cols[3].name = t("pages.toppings.fields.in_stock");
  cols[4].name = t("pages.toppings.fields.in_kiosk");
  cols[5].name = t("pages.toppings.fields.unit");
  cols[6].name = t("pages.toppings.fields.suppliers");
  cols[7].name = t("pages.toppings.fields.producer");
  cols[8].name = t("pages.toppings.fields.price");
};
const dialogVisible = ref(false);
const time = ref(9);
const options = ref([
  {
    value: 9,
    label: "Permanent",
  },
  {
    value: 1,
    label: "15 minutes",
  },
  {
    value: 2,
    label: "30 minutes",
  },
  {
    value: 3,
    label: "1 hour",
  },
  {
    value: 4,
    label: "3 hours",
  },
  {
    value: 5,
    label: "6 hours",
  },
  {
    value: 6,
    label: "8 hours",
  },
  {
    value: 7,
    label: "1 day",
  },
  {
    value: 8,
    label: "1 week",
  },
]);
const optionsNor = ref([
  {
    value: 9,
    label: "Permanent",
  },
  {
    value: 1,
    label: "15 minutter",
  },
  {
    value: 2,
    label: "30 minutter",
  },
  {
    value: 3,
    label: "1 time",
  },
  {
    value: 4,
    label: "3 time",
  },
  {
    value: 5,
    label: "6 timer",
  },
  {
    value: 6,
    label: "8 timer",
  },
  {
    value: 7,
    label: "1 dag",
  },
  {
    value: 8,
    label: "1 uke",
  },
]);
const productId = ref();
const productInStockValue = ref();
const { cookies } = useCookies();
const userRole = cookies.get("role_merchant");
const openChangeInStock = (id, value) => {
  if (value) {
    dialogVisible.value = true;
    productId.value = id;
    time.value = 9;
    productInStockValue.value = value;
  } else {
    onChangeInStock(id, value, undefined);
  }
};
const ChangeStock = () => {
  onChangeInStock(productId.value, productId.value, time.value);
  dialogVisible.value = false;
};
// Watch for language changes
watch(locale, () => {
  updateCols();
});

const sortBy = computed(() => {
  return cols.reduce((acc, o) => {
    if (o.sort) {
      o.sort === "asc" ? acc.push(o.field) : acc.push("-" + o.field);
    }
    return acc;
  }, []);
});

const listToppings = ref([]);
const limit = ref(10);
const currentPage = ref(1);
const total = ref();
const visible = ref(true);

const onFetchList = async () => {
  try {
    store.pageLoader({ mode: "on" });
    scrollStore.setPage(currentPage.value);
    searchStore.setValueSearch(search?.value);
    const response = await toppingService.getList({
      page: currentPage.value,
      limit: limit.value,
      search: search?.value,
    });
    if (!response?.error) {
      listToppings.value = response.data?.data || [];
      total.value = response.data.meta.total;
    }
    store.pageLoader({ mode: "off" });
  } catch (error) {
    console.log(error);
    store.pageLoader({ mode: "off" });
  }
};

watch(limit, async () => {
  currentPage.value = 1;
  await onFetchList();
});

onMounted(async () => {
  currentPage.value = scrollStore.page;
  search.value = searchStore.search;
  if (dataFetch.storeData?.["merchant-toppings-list"]?.length > 0) {
    await (listToppings.value =
      dataFetch.storeData?.["merchant-toppings-list"]);
    total.value = dataFetch.total?.["merchant-toppings-list"];
    store.pageLoader({ mode: "off" });
  } else {
    await onFetchList();
  }
  if (scrollStore.formUpdateSuccess) {
    const item = document.querySelector(`.toppingItem-${scrollStore.idItem}`);
    item.scrollIntoView({
      behavior: "smooth",
      block: "start",
    });
    scrollStore.setUpdateSuccess();
  }

  // Remove labels from
  document.querySelectorAll("#datasetLength label").forEach((el) => {
    el.remove();
  });

  // Replace select classes
  let selectLength = document.querySelector("#datasetLength select");
  if (selectLength) {
    selectLength.classList = "";
    selectLength.classList.add("form-select");
    selectLength.style.width = "80px";
  }
});
const handleChangeSearch = useDebounceFn((e) => {
  search.value = e?.target?.value;
  onFetchList();
}, 500);
const onOpenDeleteConfirm = (id) => {
  toast
    .fire({
      title: "Are you sure?",
      text: "You will not be able to recover this topping!",
      icon: "warning",
      showCancelButton: true,
      customClass: {
        confirmButton: "btn btn-danger m-1",
        cancelButton: "btn btn-info m-1",
      },
      confirmButtonText: "Yes, delete!",
      html: false,
      preConfirm: () => {
        return toppingService.delete(id);
      },
    })
    .then((result) => {
      if (result.value && !result.value?.error) {
        toast.fire("Deleted!", "Topping has been deleted.", "success");
        onFetchList();
        dataFetch.setData([], "merchant-toppings-list");
        dataFetch.setTotal(0, "merchant-toppings-list");
      } else if (result.dismiss === "cancel") {
        toast.fire("Cancelled", "Topping is safe", "error");
      }
    });
};

const onChangeInStock = async (id, inStock, type) => {
  const inStockValue = ref();
  if (inStock) {
    inStockValue.value = 0;
  } else {
    inStockValue.value = 1;
  }
  const payload = {
    in_stock: inStockValue.value,
    restock_type: type,
  };
  try {
    const result = await toppingService.updateStock(id, payload);
    if (!result?.error) {
      setNotify({
        title: "Success",
        message: result.message,
        type: "success",
      });
      await onFetchList();
      dataFetch.setData([], "merchant-toppings-list");
      dataFetch.setTotal(0, "merchant-toppings-list");
    }
  } catch (e) {
    setNotify({
      title: "Error",
      message: e?.message,
    });
  }
};

const onChangeInKiosk = async (id, inKiosk) => {
  const payload = {
    in_kiosk: !inKiosk,
  };
  try {
    const result = await toppingService.updateStatus(id, payload);
    if (!result?.error) {
      setNotify({
        title: "Success",
        message: result.message,
        type: "success",
      });
      await onFetchList();
      dataFetch.setData([], "merchant-toppings-list");
      dataFetch.setTotal(0, "merchant-toppings-list");
    }
  } catch (e) {
    setNotify({
      title: "Error",
      message: e?.message,
    });
  }
};
</script>

<template>
  <BasePageHeading
    :title="t('pages.toppings.name')"
    :subtitle="t('pages.toppings.labels.label_head_list')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.toppings.labels.manages")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            {{ t("pages.toppings.titles.list") }}
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <BaseBlock :title="t('pages.toppings.name')">
      <template #options>
        <e-button
          v-if="userRole !== EMPLOYEE"
          type="info"
          size="sm"
          @click="() => router.pushByName({ name: 'merchant-toppings-create' })"
          ><i class="fa fa-plus opacity-50 me-1"></i>
          {{ t("pages.toppings.labels.btn_new") }}</e-button
        >
      </template>
      <Dataset
        v-slot="{ ds }"
        :ds-data="listToppings"
        :ds-sortby="sortBy"
        :ds-search-in="['name']"
      >
        <div class="row" :data-page-count="ds.dsPagecount">
          <div id="datasetLength" class="col-md-8 py-2">
            <DatasetShow v-show="false" :dsShowEntries="100" />
            <div class="form-inline">
              <select class="form-select" style="width: 80px" v-model="limit">
                <option :value="5">5</option>
                <option :value="10">10</option>
                <option :value="25">25</option>
                <option :value="50">50</option>
                <option :value="100">100</option>
              </select>
            </div>
          </div>
          <div class="col-md-4 py-2">
            <input
              v-model="search"
              type="text"
              class="form-control"
              id="form-name"
              :placeholder="t('pages.placeholder.enter')"
              @input="handleChangeSearch"
            />
          </div>
        </div>
        <hr />
        <div class="row" v-if="listToppings?.length">
          <div class="col-md-12">
            <div class="table-responsive">
              <table class="table mb-0">
                <thead>
                  <tr>
                    <th v-for="th in cols" :key="th.field">
                      {{ th.name }}
                    </th>
                    <th
                      class="text-end"
                      scope="col"
                      v-if="userRole !== EMPLOYEE"
                    >
                      {{ t("pages.toppings.fields.action") }}
                    </th>
                  </tr>
                </thead>
                <DatasetItem tag="tbody" class="fs-sm">
                  <template #default="{ row }">
                    <tr :class="`toppingItem-${row?.id}`">
                      <td style="min-width: 50px">{{ row?.id }}</td>
                      <td style="min-width: 150px">{{ row?.name }}</td>
                      <td style="min-width: 150px">
                        {{ row?.topping_group?.name }}
                      </td>
                      <td style="min-width: 150px">
                        <div
                          class="form-check form-switch"
                          @click="openChangeInStock(row?.id, row?.in_stock)"
                        >
                          <input
                            :style="{ opacity: 1 }"
                            style="pointer-events: none"
                            class="form-check-input"
                            type="checkbox"
                            :checked="row?.in_stock"
                          />
                        </div>
                        <CountDown
                          @load="onFetchList"
                          v-if="!row?.in_stock && row?.restock_at"
                          :targetDate="row?.restock_at"
                        />
                        <div class="text-danger" v-else-if="!row?.in_stock">
                          Permanent
                        </div>
                      </td>
                      <td style="min-width: 150px">
                        <div class="form-check form-switch">
                          <input
                            :style="{ opacity: 1 }"
                            class="form-check-input"
                            type="checkbox"
                            :checked="row?.in_kiosk"
                            @change="onChangeInKiosk(row?.id, row?.in_kiosk)"
                          />
                        </div>
                      </td>
                      <td style="min-width: 150px">{{ row.unit?.name }}</td>
                      <td style="min-width: 150px">{{ row.supplier?.name }}</td>
                      <td style="min-width: 150px">{{ row.producer?.name }}</td>
                      <td style="min-width: 150px">
                        {{ row?.price + ",-kr" }}
                      </td>
                      <td class="text-end" v-if="userRole !== EMPLOYEE">
                        <div class="btn-group">
                          <button
                            type="button"
                            class="btn btn-sm btn-alt-secondary"
                            @click="
                              () =>
                                router.pushByPath(`/toppings/${row?.id}/update`)
                            "
                          >
                            <i class="fa fa-fw fa-pencil-alt"></i>
                          </button>
                          <button
                            type="button"
                            class="btn btn-sm btn-alt-secondary"
                            data-bs-toggle="modal"
                            data-bs-target="#modal-delete"
                            @click="onOpenDeleteConfirm(row.id)"
                          >
                            <i class="fa fa-fw fa-times"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </template>
                </DatasetItem>
              </table>
            </div>
          </div>
        </div>
        <EListEmpty v-else />
        <div
          class="d-flex flex-md-row flex-column justify-content-between align-items-center"
        >
          <DatasetInfo class="py-3 fs-sm" />
          <el-pagination
            v-if="visible"
            v-model:current-page="currentPage"
            @current-change="onFetchList"
            background
            v-model:page-size="limit"
            layout="prev, pager, next"
            :prev-text="t('pages.footer.previous')"
            :next-text="t('pages.footer.next')"
            :total="total"
          />
        </div>
      </Dataset>
    </BaseBlock>
  </div>

  <!-- <EModal
    id="modal-delete"
    :title="`Delete Item ${idToppingDelete}`"
    subtitle="Are you sure to delete it?"
    :handle-confirm="apiDelete"
  /> -->
  <el-dialog
    v-model="dialogVisible"
    :title="t('pages.products.labels.ask_stock')"
    width="400"
  >
    <div class="dialog-container">
      <el-select-v2
        v-model="time"
        :options="locale === 'en' ? options : optionsNor"
        placeholder="Please select"
        size="large"
      />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <div class="cancel-button">
          <el-button @click="dialogVisible = false">{{
            t("pages.products.buttons.cancel")
          }}</el-button>
        </div>
        <el-button type="primary" @click="ChangeStock">
          {{ t("pages.products.buttons.confirm") }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.gg-select {
  box-sizing: border-box;
  position: relative;
  display: block;
  transform: scale(1);
  width: 22px;
  height: 22px;
}
.gg-select::after,
.gg-select::before {
  content: "";
  display: block;
  box-sizing: border-box;
  position: absolute;
  width: 8px;
  height: 8px;
  left: 7px;
  transform: rotate(-45deg);
}
.gg-select::before {
  border-left: 2px solid;
  border-bottom: 2px solid;
  bottom: 4px;
  opacity: 0.3;
}
.gg-select::after {
  border-right: 2px solid;
  border-top: 2px solid;
  top: 4px;
  opacity: 0.3;
}
th.sort {
  cursor: pointer;
  user-select: none;
  &.asc {
    .gg-select::after {
      opacity: 1;
    }
  }
  &.desc {
    .gg-select::before {
      opacity: 1;
    }
  }
}
.cancel-button ::v-deep .el-button {
  background-color: #ebeef2 !important;
  color: #212529 !important;
  border-color: #ebeef2 !important;
}
.cancel-button {
  display: inline-block;
}
.dialog-container {
  padding: 20px 30px 0 30px;
}
</style>
