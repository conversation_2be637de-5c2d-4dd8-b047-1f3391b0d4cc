<script setup>
import EIcon from '@/components/Elements/EIcon.vue'
import { ref, onMounted } from 'vue'
import {  useRoute } from 'vue-router'
import { userService } from '@/services/user.service'
import { useTemplateStore } from '@/stores/template'
import useAppRouter from '@/composables/useRouter'
import { useI18n } from 'vue-i18n'

const store = useTemplateStore()

const router = useAppRouter()
const route = useRoute()
const { id } = route.params
const {t} = useI18n()
const user = ref()
const title = ref('View user')

const apiGetItem = async () => {
  try {
    store.pageLoader({ mode: 'on' })
    const response = await userService.get(id)
    user.value = response.data
    store.pageLoader({ mode: 'off' })
  } catch (error) {
    console.log(error)
    store.pageLoader({ mode: 'off' })
  }
}

onMounted(async () => {
  try {
    await apiGetItem()
  } catch (error) {
    console.error('Error fetching data:', error)
  }
})
</script>

<template>
  <BasePageHeading
    :title="title"
    :go-back="true"
    :subtitle="t('pages.user.labels.label_head_list')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{ t('pages.user.labels.manage') }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <router-link to="/merchant/users">{{ t('pages.user.titles.list') }}</router-link>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <span>{{ t('pages.user.titles.view_user') }}</span>
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="container">
    <div class="row justify-content-center py-sm-4 py-md-6">
      <div class="col-sm-10 col-md-8">
        <form @submit.prevent="onSubmit">
          <BaseBlock :title="t('pages.user.titles.view_user')">
            <template #options>
              <e-icon
                @click="() => router.back()"
                name="arrow-left"
                role="button"
                class="icon_arrow_left"
              />
            </template>

            <div class="row justify-content-center py-sm-3 py-md-5">
              <div class="col-sm-10 col-md-8">
                <div class="mb-4">
                  <label class="form-label" for="form-user-name">{{ t('pages.user.fields.user_name') }}</label>
                  <div class="text_user">
                    {{ user?.userName }}
                  </div>
                </div>

                <div class="mb-4">
                  <label class="form-label" for="form-user-role">{{t('pages.user.fields.role')}}</label>
                  <div class="text_user">
                    {{ user?.role }}
                  </div>
                </div>

                <div class="mb-4">
                  <label class="form-label" for="form-user-email">{{ t('pages.user.fields.email') }}</label>
                  <div class="text_user">
                    {{ user?.email }}
                  </div>
                </div>

                <div class="mb-4" :style="{ textAlign: 'end' }">
                  <button
                    class="btn btn-sm btn-primary"
                    :style="{ color: '#fff' }"
                    @click="() => router.pushByPath(`/${id}/update`)"
                  >
                    {{ t('buttons.update') }}
                  </button>
                </div>
              </div>
            </div>
          </BaseBlock>
        </form>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.text_user {
  background-color: rgb(244, 244, 244);
  border: 1px solid #e9dddd;
  border-radius: 4px;
  padding: 10px;
  min-height: 48px;
  align-items: center;
  display: flex;
}

.icon_arrow_left {
  background-color: rgb(243, 235, 235);
  border-radius: 100%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
