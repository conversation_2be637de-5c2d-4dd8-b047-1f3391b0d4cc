<script setup>
import EButton from "@/components/Elements/EButton.vue";
import EListEmpty from "@/components/Elements/EListEmpty.vue";
import useNotify from "@/composables/useNotify";
import useAppRouter from "@/composables/useRouter";
import { printerService } from "@/services/printer.service";
import { useTemplateStore } from "@/stores/template";
import Swal from "sweetalert2";
import { computed, onMounted, reactive, ref, watch } from "vue";
import {
  Dataset,
  DatasetInfo,
  DatasetItem,
  DatasetPager,
  DatasetSearch,
  DatasetShow,
} from "vue-dataset";
import { useI18n } from "vue-i18n";

const store = useTemplateStore();
const { t } = useI18n();
let toast = Swal.mixin({
  buttonsStyling: false,
  target: "#page-container",
  customClass: {
    confirmButton: "btn btn-success m-1",
    cancelButton: "btn btn-danger m-1",
    input: "form-control",
  },
});
const router = useAppRouter();

// Helper variables
const cols = reactive([
  {
    name: t("pages.printer.fields.id"),
    field: "id",
    sort: "",
  },
  {
    name: t("pages.printer.fields.printer_id"),
    field: "printer_id",
    sort: "",
  },
  {
    name: t("pages.printer.fields.printer"),
    field: "name",
    sort: "",
  },
  {
    name: t("pages.printer.fields.user_name"),
    field: "username",
    sort: "",
  },
  {
    name: t("pages.printer.fields.type"),
    field: "type",
    sort: "",
  },
]);

// Sort by functionality
const sortBy = computed(() => {
  return cols.reduce((acc, o) => {
    if (o.sort) {
      o.sort === "asc" ? acc.push(o.field) : acc.push("-" + o.field);
    }
    return acc;
  }, []);
});

// On sort th click
function onSort(event, i) {
  let toset;
  const sortEl = cols[i];

  if (!event.shiftKey) {
    cols.forEach((o) => {
      if (o.field !== sortEl.field) {
        o.sort = "";
      }
    });
  }

  if (!sortEl.sort) {
    toset = "asc";
  }

  if (sortEl.sort === "desc") {
    toset = event.shiftKey ? "" : "asc";
  }

  if (sortEl.sort === "asc") {
    toset = "desc";
  }

  sortEl.sort = toset;
}

// const idPrinterDelete = ref()
const listPrinters = ref([]);

const limit = ref(10);
const currentPage = ref(1);
const total = ref();
const visible = ref(true);

const onFetchList = async () => {
  try {
    store.pageLoader({ mode: "on" });
    const response = await printerService.getList({
      page: currentPage.value,
      limit: limit.value,
    });
    if (!response?.error) {
      listPrinters.value = response.data?.data || [];
      total.value = response.data.value;
    }
    store.pageLoader({ mode: "off" });
  } catch (error) {
    console.log(error);
    store.pageLoader({ mode: "off" });
  }
};

// const apiDelete = async () => {
//   try {
//     store.pageLoader({ mode: 'on' })
//     await printerService.delete(idPrinterDelete.value)
//     const newPrinters = listPrinters.value.filter((item) => item.id !== idPrinterDelete.value)
//     listPrinters.value = newPrinters
//     idPrinterDelete.value = null
//     store.pageLoader({ mode: 'off' })
//   } catch (error) {
//     console.log(error)
//     store.pageLoader({ mode: 'off' })
//   }
// }

watch(limit, async () => {
  currentPage.value = 1;
  await onFetchList();
});

onMounted(async () => {
  await onFetchList();

  // Remove labels from
  document.querySelectorAll("#datasetLength label").forEach((el) => {
    el.remove();
  });

  // Replace select classes
  let selectLength = document.querySelector("#datasetLength select");
  if (selectLength) {
    selectLength.classList = "";
    selectLength.classList.add("form-select");
    selectLength.style.width = "80px";
  }
});
const { setNotify } = useNotify();
const onTestPrint = async (id) => {
  try {
    const result = await printerService.testPrint(id);
    if (!result?.error) {
      setNotify({
        title: "Success",
        message: result?.message || "Print success",
        type: "success",
      });
    }
  } catch (e) {
    setNotify({
      title: "Error",
      message: e?.message,
    });
  }
};

const onOpenDeleteConfirm = (id) => {
  toast
    .fire({
      title: "Are you sure?",
      text: "You will not be able to recover this printer!",
      icon: "warning",
      showCancelButton: true,
      customClass: {
        confirmButton: "btn btn-danger m-1",
        cancelButton: "btn btn-info m-1",
      },
      confirmButtonText: "Yes, delete!",
      html: false,
      preConfirm: () => {
        return printerService.delete(id);
      },
    })
    .then((result) => {
      if (result.value && !result.value?.error) {
        toast.fire("Deleted!", "Printer has been deleted.", "success");
        onFetchList();
      } else if (result.dismiss === "cancel") {
        toast.fire("Cancelled", "Printer is safe", "error");
      }
    });
};
</script>

<template>
  <BasePageHeading
    :title="t('pages.printer.name')"
    :subtitle="t('pages.printer.labels.label_head_list')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.printer.labels.manages")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            {{ t("pages.printer.titles.list") }}
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <BaseBlock title="Printers">
      <template #options>
        <e-button
          type="info"
          size="sm"
          @click="() => router.pushByName({ name: 'merchant-printers-create' })"
          ><i class="fa fa-plus opacity-50 me-1"></i>
          {{ t("pages.printer.titles.add") }}</e-button
        >
      </template>
      <Dataset
        v-slot="{ ds }"
        :ds-data="listPrinters"
        :ds-sortby="sortBy"
        :ds-search-in="['id', 'name', 'printer_id', 'username', 'type']"
      >
        <div class="row" :data-page-count="ds.dsPagecount">
          <div id="datasetLength" class="col-md-8 py-2">
            <DatasetShow v-show="false" :dsShowEntries="100" />
            <div class="form-inline">
              <select class="form-select" style="width: 80px" v-model="limit">
                <option :value="5">5</option>
                <option :value="10">10</option>
                <option :value="25">25</option>
                <option :value="50">50</option>
                <option :value="100">100</option>
              </select>
            </div>
          </div>
          <div class="col-md-4 py-2">
            <DatasetSearch :ds-search-placeholder="t('pages.placeholder.search')" />
          </div>
        </div>
        <hr />
        <div class="row" v-if="listPrinters?.length">
          <div class="col-md-12">
            <div class="table-responsive">
              <table class="table mb-0">
                <thead>
                  <tr>
                    <th
                      v-for="(th, index) in cols"
                      :key="th.field"
                      :class="['sort', th.sort]"
                      @click="onSort($event, index)"
                    >
                      {{ th.name }} <i class="gg-select float-end"></i>
                    </th>
                    <th class="text-end" scope="col">
                      {{ t("pages.printer.fields.action") }}
                    </th>
                  </tr>
                </thead>
                <DatasetItem tag="tbody" class="fs-sm">
                  <template #default="{ row }">
                    <tr>
                      <td style="min-width: 50px">
                        <RouterLink :to="`printers/${row.id}`">
                          {{ row.id }}
                        </RouterLink>
                      </td>
                      <td style="min-width: 150px">{{ row.printer_id }}</td>
                      <td style="min-width: 150px">{{ row.name }}</td>
                      <td style="min-width: 150px">{{ row.username }}</td>
                      <td style="min-width: 150px">
                        {{ row.type === 1 ? "Counter" : "Kitchen" }}
                      </td>
                      <td class="text-end">
                        <div class="btn-group">
                          <button
                            type="button"
                            class="btn btn-sm btn-alt-secondary"
                            @click="onTestPrint(row?.id)"
                          >
                            <i class="fa fa-print"></i>
                          </button>
                          <button
                            type="button"
                            class="btn btn-sm btn-alt-secondary"
                            @click="
                              () =>
                                router.pushByPath(`/printers/${row.id}/update`)
                            "
                          >
                            <i class="fa fa-fw fa-pencil-alt"></i>
                          </button>
                          <button
                            type="button"
                            class="btn btn-sm btn-alt-secondary"
                            @click="onOpenDeleteConfirm(row.id)"
                          >
                            <i class="fa fa-fw fa-times"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </template>
                </DatasetItem>
              </table>
            </div>
          </div>
        </div>
        <EListEmpty v-else />
        <div
          class="d-flex flex-md-row flex-column justify-content-between align-items-center"
        >
          <DatasetInfo class="py-3 fs-sm" />
          <el-pagination
            v-if="visible"
            v-model:current-page="currentPage"
            @current-change="onFetchList"
            background
            v-model:page-size="limit"
            layout="prev, pager, next"
            prev-text="Previous"
            next-text="Next"
            :total="total"
          />
        </div>
      </Dataset>
    </BaseBlock>
  </div>
</template>

<style lang="scss" scoped>
.gg-select {
  box-sizing: border-box;
  position: relative;
  display: block;
  transform: scale(1);
  width: 22px;
  height: 22px;
}
.gg-select::after,
.gg-select::before {
  content: "";
  display: block;
  box-sizing: border-box;
  position: absolute;
  width: 8px;
  height: 8px;
  left: 7px;
  transform: rotate(-45deg);
}
.gg-select::before {
  border-left: 2px solid;
  border-bottom: 2px solid;
  bottom: 4px;
  opacity: 0.3;
}
.gg-select::after {
  border-right: 2px solid;
  border-top: 2px solid;
  top: 4px;
  opacity: 0.3;
}
th.sort {
  cursor: pointer;
  user-select: none;
  &.asc {
    .gg-select::after {
      opacity: 1;
    }
  }
  &.desc {
    .gg-select::before {
      opacity: 1;
    }
  }
}
</style>
