<script setup>
import EIcon from "@/components/Elements/EIcon.vue";
import { useRoute } from "vue-router";
import { reactive, computed, ref, onMounted } from "vue";
import useVuelidate from "@vuelidate/core";
import { required } from "@vuelidate/validators";
import { producerService } from "@/services/producer.service";
import { useTemplateStore } from "@/stores/template";
import useNotify from "@/composables/useNotify";
import useAppRouter from "@/composables/useRouter";
import { useI18n } from "vue-i18n";
import { storeData } from "@/stores/storeData";
import { scrollTo } from "@/stores/scollItemInlist";

const store = useTemplateStore();
const scrollStore = scrollTo();
const dataFetch = storeData();
const { t } = useI18n();
const route = useRoute();
const router = useAppRouter();
const { setNotify } = useNotify();
const typeSubmit = ref();

const props = route.params;
const id = props?.id;

const producer = ref();

let state = reactive({
  name: null,
});

const rules = computed(() => {
  return {
    name: { required },
    website: { required },
    description: { required },
  };
});

let v$ = useVuelidate(rules, state);

async function onSubmit() {
  try {
    const result = await v$.value.$validate();

    if (!result) return;
    store.pageLoader({ mode: "on" });

    if (id) {
      await producerService.update(id, state);
    } else {
      await producerService.create(state);
    }
    if (typeSubmit.value === "confirm") {
      scrollStore.getElement(id);
      dataFetch.setData([], "merchant-producers-list");
      dataFetch.setTotal(0, "merchant-producers-list");
      router.pushByName({ name: "merchant-producers-list" });
    } else {
      state.name = null;
      state.website = null;
      state.description = null;
      v$.value.$reset();
    }
    store.pageLoader({ mode: "off" });
    setNotify({
      title: "Success",
      message: id ? "Produces update success" : "Producer create success",
      type: "success",
    });
  } catch (e) {
    setNotify({
      title: "Error",
      message: e?.message,
    });
    store.pageLoader({ mode: "off" });
  }
}

const apiGetItem = async () => {
  try {
    store.pageLoader({ mode: "on" });
    const response = await producerService.get(id);
    producer.value = response.data;

    state = reactive({
      name: response.data.name,
      website: response.data.website,
      description: response.data.description,
    });

    v$ = useVuelidate(rules, state);
    store.pageLoader({ mode: "off" });
  } catch (error) {
    console.log(error);
    store.pageLoader({ mode: "off" });
  }
};

onMounted(async () => {
  try {
    if (id) apiGetItem();
  } catch (error) {
    console.error("Error fetching data:", error);
  }
});

const handleSubmit = (type) => {
  typeSubmit.value = type;
};
</script>

<template>
  <BasePageHeading
    :title="
      id
        ? t('pages.producers.titles.update')
        : t('pages.producers.titles.create')
    "
    :go-back="true"
    :subtitle="t('pages.producers.labels.label_head_form')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.producers.labels.manages")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <router-link to="/merchant/producers">{{
              t("pages.producers.titles.list")
            }}</router-link>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <span>{{
              id
                ? t("pages.producers.titles.update")
                : t("pages.producers.titles.create")
            }}</span>
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <div class="row justify-content-center">
      <div class="col-sm-12 col-md-8">
        <form @submit.prevent="onSubmit">
          <BaseBlock
            :title="
              id
                ? `${t('pages.producers.titles.update')} ${producer?.id}`
                : t('pages.producers.titles.add')
            "
          >
            <template #options>
              <e-icon
                @click="
                  () => {
                    router.back();
                    dataFetch.setData([], 'merchant-producers-list');
                    dataFetch.setTotal(0, 'merchant-producers-list');
                  }
                "
                name="arrow-left"
                role="button"
                class="icon_arrow_left"
              />
            </template>

            <div class="row justify-content-center py-sm-3 py-md-5">
              <div class="col-sm-10 col-md-8">
                <div class="mb-4">
                  <label class="form-label" for="block-form-name"
                    >{{ t("pages.producers.fields.name")
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    type="text"
                    class="form-control"
                    id="block-form-name"
                    name="block-form-name"
                    :placeholder="t('pages.producers.placeholder.enter_name')"
                    :class="{
                      'is-invalid': v$.name.$errors.length,
                    }"
                    v-model="state.name"
                    @blur="v$.name.$touch"
                  />
                  <div
                    v-if="v$.name.$errors.length"
                    class="invalid-feedback animated fadeIn"
                  >
                    {{ t("pages.producers.validate.require_name") }}
                  </div>
                </div>

                <div class="mb-4">
                  <label class="form-label" for="block-form-web-address"
                    >{{ t("pages.producers.fields.web_address_field")
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    type="text"
                    class="form-control"
                    id="block-form-web-address"
                    name="block-form-web-address"
                    :placeholder="
                      t('pages.producers.placeholder.enter_web_address')
                    "
                    :class="{
                      'is-invalid': v$.website.$errors.length,
                    }"
                    v-model="state.website"
                    @blur="v$.website.$touch"
                  />
                  <div
                    v-if="v$.website.$errors.length"
                    class="invalid-feedback animated fadeIn"
                  >
                    {{ t("pages.producers.validate.require_name") }}
                  </div>
                </div>

                <div class="mb-4">
                  <label class="form-label" for="form-description"
                    >{{ t("pages.producers.fields.description_field")
                    }}<span class="text-danger">*</span></label
                  >
                  <textarea
                    class="form-control"
                    id="form-description"
                    name="form-description"
                    :class="{
                      'is-invalid': v$.description.$errors.length,
                    }"
                    v-model="state.description"
                    @blur="v$.description.$touch"
                    rows="5"
                    :placeholder="
                      t('pages.producers.placeholder.enter_content')
                    "
                  ></textarea>
                  <div
                    v-if="v$.description.$errors.length"
                    class="invalid-feedback animated fadeIn"
                  >
                    {{ t("pages.producers.validate.require_content") }}
                  </div>
                </div>

                <div
                  class="my-4"
                  :style="{
                    textAlign: 'end',
                    display: 'flex',
                    gap: '5px',
                    justifyContent: 'end',
                  }"
                >
                  <button
                    type="submit"
                    class="btn btn-sm btn-primary"
                    @click="handleSubmit('confirm')"
                    :style="{ color: '#fff' }"
                  >
                    {{ t("pages.suppliers.labels.btn_confirm") }}
                  </button>
                  <button
                    v-if="!id"
                    type="submit"
                    class="btn btn-sm btn-primary"
                    @click="handleSubmit('confirm_add')"
                    :style="{ color: '#fff' }"
                  >
                    {{ t("pages.products.buttons.confirm_add") }}
                  </button>
                </div>
              </div>
            </div>
          </BaseBlock>
        </form>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.icon_arrow_left {
  background-color: rgb(243, 235, 235);
  border-radius: 100%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
