//
// Theme
// --------------------------------------------------

@mixin color-theme(
  $primary,
  $primary-light,
  $primary-lighter,
  $primary-dark,
  $primary-darker,
  $body-bg,
  $body-bg-dark,
  $body-bg-light,
  $body-color,
  $body-color-dark,
  $body-color-light,
  $link-color,
  $link-hover-color,
  $input-btn-focus-color,
  $input-btn-focus-box-shadow,
  $input-bg,
  $input-color,
  $input-border-color,
  $input-focus-bg,
  $input-focus-color,
  $input-focus-border-color,
  $header-bg,
  $header-dark-bg,
  $sidebar-bg,
  $sidebar-dark-bg,
  $side-overlay-bg
) {
  // Scaffolding
  & {
    color: $body-color;
    background-color: $body-bg;
  }

  // Links
  a:not(.badge):not(.btn):not(.btn-block-option):not(.block):not(
      .dropdown-item
    ):not(.nav-link):not(.page-link):not(.alert-link):not(.nav-main-link):not(
      .list-group-item-action
    ):not(.close):not(.fc-event):not(.text-success-light):not(
      .text-danger-light
    ):not(.text-warning-light):not(.text-info-light) {
    color: $link-color;

    &.link-fx::before {
      background-color: $link-color;
    }

    &:hover {
      color: $link-hover-color;
    }
  }

  // Typography
  hr {
    border-top-color: $body-bg-dark;
  }

  // Contextual text colors
  @include text-emphasis-variant(".text-primary", $primary);
  @include text-emphasis-variant(".text-primary-dark", $primary-dark);
  @include text-emphasis-variant(".text-primary-darker", $primary-darker);
  @include text-emphasis-variant(".text-primary-light", $primary-light);
  @include text-emphasis-variant(".text-primary-lighter", $primary-lighter);

  @include text-emphasis-variant(".text-body-bg", $body-bg);
  @include text-emphasis-variant(".text-body-bg-light", $body-bg-light);
  @include text-emphasis-variant(".text-body-bg-dark", $body-bg-dark);

  @include text-emphasis-variant(".text-body-color", $body-color);
  @include text-emphasis-variant(".text-body-color-dark", $body-color-dark);
  @include text-emphasis-variant(".text-body-color-light", $body-color-light);

  // Contextual dual text colors (for dark header/sidebar)
  @include text-emphasis-variant(".text-dual", $primary-dark);

  .page-header-dark #page-header,
  .sidebar-dark #sidebar {
    @include text-emphasis-variant(".text-dual", $body-color-light);
  }

  // Contextual background colors
  @include bg-variant(".bg-primary", $primary);
  @include bg-variant(".bg-primary-op", rgba($primary, 0.75));
  @include bg-variant(".bg-primary-dark", $primary-dark);
  @include bg-variant(".bg-primary-dark-op", rgba($primary-dark, 0.8));
  @include bg-variant(".bg-primary-darker", $primary-darker);
  @include bg-variant(".bg-primary-light", $primary-light);
  @include bg-variant(".bg-primary-lighter", $primary-lighter);
  @include bg-variant(".bg-body", $body-bg);
  @include bg-variant(".bg-body-light", $body-bg-light);
  @include bg-variant(".bg-body-dark", $body-bg-dark);

  // Elements
  @include bg-variant(".bg-header-light", $header-bg);
  @include bg-variant(".bg-header-dark", $header-dark-bg);

  @include bg-variant(".bg-sidebar-light", $sidebar-bg);
  @include bg-variant(".bg-sidebar-dark", $sidebar-dark-bg);

  // Buttons
  .btn-link {
    color: $link-color;

    &:hover {
      color: $link-hover-color;
    }
  }

  .btn-primary {
    @include button-variant($primary, $primary);
  }

  .btn-secondary {
    @include button-variant($primary-dark, $primary-dark);
  }

  .btn-outline-primary {
    @include button-outline-variant($primary);
  }

  .btn-alt-primary {
    @include button-variant(
      tint-color($primary, 75%),
      tint-color($primary, 75%),
      shade-color($primary, 40%),
      tint-color($primary, 50%),
      tint-color($primary, 50%),
      shade-color($primary, 60%)
    );
  }

  .btn-alt-secondary {
    @include button-variant(
      $body-bg,
      $body-bg,
      $body-color-dark,
      shade-color($body-bg, 10%),
      shade-color($body-bg, 10%),
      shade-color($body-color-dark, 15%)
    );
  }

  // Used for buttons that adapt to light/dark header and sidebar variations
  .page-header-dark #page-header .btn-alt-secondary,
  .sidebar-dark #sidebar .btn-alt-secondary {
    @include button-variant(
      lighten($primary-darker, 5%),
      lighten($primary-darker, 5%),
      $white,
      darken($primary-darker, 5%),
      darken($primary-darker, 5%),
      $white
    );
  }

  // Alerts
  .alert-primary {
    @include alert-variant($primary-lighter, $primary-lighter, $primary-dark);
  }

  // Progress Bars
  .progress-bar {
    background-color: $primary;
  }

  // Nav links
  .nav-link {
    color: $body-color;

    &:hover,
    &:focus {
      color: $primary;
    }
  }

  // Nav Pills
  .nav-pills {
    .nav-link {
      color: $body-color;

      &:hover,
      &:focus {
        background-color: $body-bg;
      }
    }

    .nav-link.active,
    .show > .nav-link {
      color: $white;
      background-color: $primary;
    }
  }

  // Nav Tabs
  .nav-tabs {
    border-bottom-color: $body-bg-dark;

    .nav-link {
      &:hover,
      &:focus {
        border-color: $body-bg-dark $body-bg-dark $body-bg-dark;
      }
    }

    .nav-link.active,
    .nav-item.show .nav-link {
      border-color: $body-bg-dark $body-bg-dark $white;
    }
  }

  // Tabs block variation
  .nav-tabs-block {
    background-color: $body-bg-light;

    .nav-link {
      border-color: transparent;
      color: $body-color;

      &:hover,
      &:focus {
        color: $primary;
        background-color: $body-bg-light;
        border-color: transparent;
      }
    }

    .nav-link.active,
    .nav-item.show .nav-link {
      color: $body-color;
      background-color: $white;
      border-color: transparent;
    }
  }

  // Tabs block alternative variation
  .nav-tabs-alt {
    border-bottom-color: $body-bg-dark;

    .nav-link {
      color: $body-color;
      background-color: transparent;
      border-color: transparent;

      &:hover,
      &:focus {
        color: $primary;
        background-color: transparent;
        border-color: transparent;
        box-shadow: inset 0 -3px $primary;
      }
    }

    .nav-link.active,
    .nav-item.show .nav-link {
      color: $body-color;
      background-color: transparent;
      border-color: transparent;
      box-shadow: inset 0 -3px $primary;
    }
  }

  // Various Items Navigation
  .nav-items {
    a {
      border-bottom-color: $body-bg;

      &:hover {
        background-color: $body-bg-light;
      }

      &:active {
        background-color: $body-bg;
      }
    }

    > li:last-child > a {
      border-bottom: none;
    }
  }

  // Cards
  .card {
    &.card-borderless {
      box-shadow: 0 1px 2px rgba(shade-color($body-bg-dark, 3%), 0.5),
        0 1px 2px rgba(shade-color($body-bg-dark, 3%), 0.5);
    }

    > .card-header:not(.bg-transparent),
    > .card-footer:not(.bg-transparent) {
      background-color: $body-bg-light;
    }

    > .card-header:not(.border-bottom-0),
    > .card-footer:not(.border-top-0) {
      border-color: $body-bg-dark;
    }

    &:not(.card-borderless) {
      &,
      > .card-header {
        border-color: $body-bg-dark;
      }
    }
  }

  // Pagination
  .page-item {
    &.active .page-link {
      color: $primary;
      background-color: $body-bg-light;
      border-color: $primary;
    }
  }

  .page-link {
    color: $body-color;

    &:hover {
      color: $primary;
      border-color: $primary;
    }

    &:focus {
      background-color: $body-bg;
    }
  }

  // List Group
  .list-group-item-action {
    color: $body-color;

    &:hover,
    &:focus {
      color: $body-color;
      background-color: $body-bg-light;
    }

    &:active {
      color: $body-color;
      background-color: $body-bg-dark;
    }
  }

  .list-group-item {
    border-color: $body-bg-dark;

    &.active {
      color: $white;
      background-color: $primary;
      border-color: $primary;
    }
  }

  // Popovers
  .popover {
    border-color: $body-bg-dark;
  }

  .bs-popover-top {
    .popover-arrow::before {
      border-top-color: fade-in($body-bg-dark, 0.05);
    }

    .popover-arrow::after {
      border-top-color: $white;
    }
  }

  .bs-popover-end {
    .popover-arrow::before {
      border-right-color: fade-in($body-bg-dark, 0.05);
    }

    .popover-arrow::after {
      border-right-color: $white;
    }
  }

  .bs-popover-bottom {
    .popover-arrow::before {
      border-bottom-color: fade-in($body-bg-dark, 0.05);
    }

    .popover-arrow::after {
      border-bottom-color: $white;
    }
  }

  .bs-popover-start {
    .popover-arrow::before {
      border-left-color: fade-in($body-bg-dark, 0.05);
    }

    .popover-arrow::after {
      border-left-color: $white;
    }
  }

  .bs-popover-auto {
    &[x-placement^="top"] {
      @extend .bs-popover-top;
    }
    &[x-placement^="right"] {
      @extend .bs-popover-end;
    }
    &[x-placement^="bottom"] {
      @extend .bs-popover-bottom;
    }
    &[x-placement^="left"] {
      @extend .bs-popover-start;
    }
  }

  // Modals
  .modal-header {
    border-bottom-color: $body-bg-dark;
  }

  .modal-footer {
    border-top-color: $body-bg-dark;
  }

  // Dropdowns
  .dropdown-menu {
    border-color: $body-bg-dark;
  }

  .dropdown-divider {
    border-top-color: $body-bg;
  }

  .dropdown-item {
    color: $body-color;

    &:hover,
    &:focus {
      color: $body-color-dark;
      background-color: $body-bg;
    }

    &.active,
    &:active {
      color: $body-color;
      background-color: $body-bg-dark;
    }
  }

  // Tables
  .table {
    --#{$variable-prefix}table-striped-bg: #{lighten($body-bg, 1.5%)};
    --#{$variable-prefix}table-active-bg: #{$body-bg};
    --#{$variable-prefix}table-hover-bg: #{darken($body-bg, 1.5%)};

    border-color: $body-bg-dark;
  }

  .table > :not(:last-child) > :last-child > * {
    border-bottom-color: $body-bg-dark;
  }

  @include table-variant("primary", $primary-lighter);

  // Forms
  .form-control,
  .form-select {
    color: $input-color;
    background-color: $input-bg;
    border-color: $input-border-color;

    &:focus {
      color: $input-focus-color;
      background-color: $input-focus-bg;
      border-color: $input-focus-border-color;
      box-shadow: $input-btn-focus-box-shadow;
    }

    &:disabled {
      background-color: $input-disabled-bg;
    }
  }

  .form-select {
    &:focus::-ms-value {
      color: $input-color;
      background-color: $input-bg;
    }
  }

  .form-control.form-control-alt,
  .form-select.form-control-alt {
    border-color: $body-bg;
    background-color: $body-bg;

    &:focus {
      border-color: $body-bg-dark;
      background-color: $body-bg-dark;
      box-shadow: none;
    }
  }

  @include form-validation-state("valid", $success, $form-feedback-icon-valid);
  @include form-validation-state(
    "invalid",
    $danger,
    $form-feedback-icon-invalid
  );

  .input-group-text {
    color: $input-color;
    background-color: $body-bg;
    border-color: $input-border-color;
  }

  .input-group-text.input-group-text-alt {
    background-color: $body-bg-dark;
    border-color: $body-bg-dark;
  }

  .form-check-input {
    border-color: darken($body-bg, 10%);

    &:focus {
      border-color: $primary;
      box-shadow: 0 0 0 0.25rem rgba($primary, 0.25);
    }

    &:checked {
      background-color: $primary;
      border-color: $primary;
    }
  }

  .form-switch .form-check-input {
    background-image: escape-svg(
      url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{darken($body-bg, 10%)}'/></svg>")
    );

    &:focus {
      background-image: escape-svg(
        url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$primary}'/></svg>")
      );
    }

    &:checked {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
    }
  }

  .form-block {
    .form-check-label {
      border: 2px solid $body-bg-dark;

      &:hover {
        border-color: darken($body-bg-dark, 5%);
      }

      &::before {
        background-color: $primary;
      }
    }

    .form-check-input:checked ~ .form-check-label {
      border-color: $primary;
    }

    .form-check-input:focus ~ .form-check-label {
      border-color: $primary;
      box-shadow: 0 0 0 0.25rem rgba($primary, 0.25);
    }

    .form-check-input:disabled:not([checked]) + .form-check-label:hover,
    .form-check-input[readonly]:not([checked]) + .form-check-label:hover {
      border-color: $body-bg-dark;
    }
  }

  // Borders
  .border {
    border-color: $body-bg-dark !important;
  }
  .border-top {
    border-top-color: $body-bg-dark !important;
  }
  .border-end {
    border-right-color: $body-bg-dark !important;
  }
  .border-bottom {
    border-bottom-color: $body-bg-dark !important;
  }
  .border-start {
    border-left-color: $body-bg-dark !important;
  }

  @each $color, $value in $theme-colors {
    .border-#{$color} {
      border-color: $value !important;
    }
  }

  .border-primary {
    border-color: $primary !important;
  }
  .border-white {
    border-color: $white !important;
  }
  .border-white-op {
    border-color: rgba($white, 0.1) !important;
  }
  .border-black-op {
    border-color: rgba($black, 0.1) !important;
  }

  // Main Structure
  #page-header {
    background-color: $header-bg;
  }
  #sidebar {
    background-color: $sidebar-bg;
  }
  #side-overlay {
    background-color: $side-overlay-bg;
  }

  // Layout/Style variations based on #page-container classes
  #page-container {
    background-color: $body-bg;

    // Page header
    &.page-header-dark #page-header {
      color: darken($body-color-light, 8%);
      background-color: $header-dark-bg;
    }

    &.page-header-glass {
      #page-header {
        background-color: transparent;
      }

      &.page-header-fixed {
        &.page-header-scroll #page-header {
          background-color: $header-bg;
        }

        &.page-header-scroll.page-header-dark #page-header {
          background-color: $header-dark-bg;
        }
      }
    }

    // Sidebar and Side Overlay
    #sidebar .content-header {
      background-color: $body-bg-light;
    }

    &.sidebar-dark #sidebar {
      color: $body-color-light;
      background-color: $sidebar-dark-bg;

      .content-header {
        background-color: lighten($sidebar-dark-bg, 2%);
      }
    }
  }

  // Sidebar
  #sidebar.with-mini-nav {
    .sidebar-mini-nav {
      color: $body-color-light;
      background-color: lighten($sidebar-dark-bg, 3%);
    }
  }

  // Blocks
  .block-header-default {
    background-color: $body-bg-light;
  }

  // Block Variations
  .block {
    &.block-bordered {
      border-color: $body-bg-dark;
    }

    &.block-themed > .block-header {
      background-color: $primary;
    }
  }

  // Block Modes
  .block {
    &.block-mode-loading {
      &::after {
        color: $primary-dark;
      }

      &.block-mode-loading-dark::after {
        background-color: $primary-dark;
      }
    }
  }

  // Block Links
  a.block {
    color: $body-color;

    &:hover {
      color: $body-color;
    }

    &.block-link-pop {
      &:hover {
        box-shadow: 0 0.5rem 2rem darken($body-bg, 8%);
      }

      &:active {
        box-shadow: 0 0.25rem 0.75rem darken($body-bg, 1%);
      }
    }

    &.block-link-shadow {
      &:hover {
        box-shadow: 0 0 2.25rem darken($body-bg, 8%);
      }

      &:active {
        box-shadow: 0 0 1.125rem darken($body-bg, 4%);
      }
    }
  }

  // Block Effects
  .block {
    &.block-fx-shadow {
      box-shadow: 0 0 2.25rem darken($body-bg, 8%);
    }

    &.block-fx-pop {
      box-shadow: 0 0.5rem 2rem darken($body-bg, 8%);
    }
  }

  // Page Loader
  #page-loader::after {
    background-color: $primary;
  }

  // Main Navigation
  // Headings
  .nav-main-heading {
    color: lighten($body-color, 20%);
  }

  // Default links
  .nav-main-link {
    color: $body-color;

    .nav-main-link-icon {
      color: lighten($body-color, 35%);
    }

    &:hover {
      color: $body-color;
      background-color: $body-bg-light;

      > .nav-main-link-icon {
        color: $black;
      }
    }

    &.active {
      color: $black;

      > .nav-main-link-icon {
        color: $black;
      }
    }
  }

  // Sub menus
  .nav-main-submenu {
    .nav-main-link {
      color: lighten($body-color, 10%);

      &:hover,
      &.active {
        color: $black;
        background-color: transparent;
      }
    }
  }

  // Active sub menu
  .nav-main-item.open {
    > .nav-main-link-submenu {
      color: $black;
    }
  }

  .nav-main-submenu .nav-main-item.open .nav-main-link {
    background-color: transparent;
  }

  // Dark Variation
  .nav-main-dark,
  .sidebar-dark #sidebar,
  .page-header-dark #page-header,
  .dark-mode #side-overlay,
  .dark-mode #main-container {
    // Headings
    .nav-main-heading {
      color: darken($body-bg-dark, 35%);
    }

    // Default links
    .nav-main-link {
      color: darken($body-bg-dark, 20%);

      > .nav-main-link-icon {
        color: rgba(darken($body-bg-dark, 20%), 0.5);
      }

      &:hover,
      &:focus {
        color: $white;
        background-color: rgba(0, 0, 0, 0.2);

        > .nav-main-link-icon {
          color: $white;
        }
      }

      &.active {
        color: $white;

        > .nav-main-link-icon {
          color: $white;
        }
      }
    }

    // Sub menus
    .nav-main-submenu {
      background-color: rgba(0, 0, 0, 0.15);

      .nav-main-link {
        color: darken($body-bg-dark, 30%);

        &:hover,
        &:focus,
        &.active {
          color: $white;
          background-color: transparent;
        }
      }
    }

    // Active sub menu
    .nav-main-item.open {
      > .nav-main-link-submenu,
      > .nav-main-link-submenu > .nav-main-link-icon {
        color: $white;
      }

      > .nav-main-submenu {
        background-color: rgba(0, 0, 0, 0.1);
      }
    }

    .nav-main-submenu .nav-main-item.open .nav-main-link {
      background-color: transparent;
    }
  }

  // Nav Main Horizontal
  @include media-breakpoint-up(lg) {
    // Light Variation
    .nav-main-horizontal {
      .nav-main-submenu {
        background-color: $body-bg;
      }
    }

    // Dark Variation
    .nav-main-dark.nav-main-horizontal,
    .sidebar-dark #sidebar .nav-main-horizontal,
    .page-header-dark #page-header .nav-main-horizontal,
    .dark-mode #main-container .nav-main-horizontal {
      .nav-main-submenu {
        background-color: darken($primary-darker, 3%) !important;
      }
    }
  }

  // Various Items Navigation
  .nav-items {
    a {
      border-bottom-color: $body-bg;

      &:hover {
        background-color: $body-bg-light;
      }
    }
  }

  // Sidebar Mini Nav
  .mini-nav-item {
    color: darken($body-bg-dark, 15%);

    &.active {
      background-color: lighten($sidebar-dark-bg, 6%);
      color: $white;
    }

    &:hover {
      color: $white;
      background-color: lighten($sidebar-dark-bg, 6%);
    }

    &:active {
      color: darken($body-bg-dark, 15%);
    }
  }

  // Activity
  .list-activity {
    > li {
      border-bottom-color: $body-bg;
    }
  }

  // Timeline
  .timeline-event-icon {
    box-shadow: 0 0.375rem 1.5rem darken($body-bg, 8%);
  }

  // Ribbons
  .ribbon-light {
    @include ribbon-variation($body-bg-dark, $body-color);
  }

  .ribbon-primary {
    @include ribbon-variation($primary, $white);
  }

  // DropzoneJS
  .dropzone {
    background-color: $body-bg-light;
    border-color: $input-border-color;

    .dz-message {
      color: $body-color;
    }

    &:hover {
      background-color: $white;
      border-color: $primary;

      .dz-message {
        color: $primary;
      }
    }
  }

  // FullCalendar
  .fc-theme-standard {
    a {
      color: $body-color;
    }

    .fc-button-primary {
      color: $body-color;
      background-color: $body-bg-dark;
      border-color: $body-bg-dark;

      &:not(:disabled):hover {
        color: $body-color;
        background-color: $body-bg;
        border-color: $body-bg;
      }

      &:not(:disabled).fc-button-active,
      &:not(:disabled):active {
        color: $body-color;
        background-color: $body-bg-light;
        border-color: $body-bg-light;
      }

      &:focus,
      &:not(:disabled).fc-button-active:focus,
      &:not(:disabled):active:focus {
        box-shadow: 0 0 0 0.2rem rgba($primary, 0.4);
      }
    }

    th,
    td,
    .fc-scrollgrid,
    .fc-list {
      border-color: $body-bg-dark;
    }

    .fc-h-event {
      background-color: $primary;
      border: $primary;
    }

    .fc-col-header-cell,
    .fc-list-day-cushion {
      background-color: $body-bg-light;
    }
  }

  // Simple Bar
  .simplebar-scrollbar::before {
    background: $primary-darker;
  }

  // Flatpickr
  .flatpickr-input.form-control:disabled,
  .flatpickr-input.form-control[readonly],
  .input.form-control:disabled,
  .input.form-control[readonly] {
    color: $input-color;
    background-color: $input-bg;
    border-color: $input-border-color;
  }

  .flatpickr-day.selected,
  .flatpickr-day.startRange,
  .flatpickr-day.endRange,
  .flatpickr-day.selected.inRange,
  .flatpickr-day.startRange.inRange,
  .flatpickr-day.endRange.inRange,
  .flatpickr-day.selected:focus,
  .flatpickr-day.startRange:focus,
  .flatpickr-day.endRange:focus,
  .flatpickr-day.selected:hover,
  .flatpickr-day.startRange:hover,
  .flatpickr-day.endRange:hover,
  .flatpickr-day.selected.prevMonthDay,
  .flatpickr-day.startRange.prevMonthDay,
  .flatpickr-day.endRange.prevMonthDay,
  .flatpickr-day.selected.nextMonthDay,
  .flatpickr-day.startRange.nextMonthDay,
  .flatpickr-day.endRange.nextMonthDay {
    border-color: $primary;
    background: $primary;
  }

  .flatpickr-months .flatpickr-prev-month:hover svg,
  .flatpickr-months .flatpickr-next-month:hover svg {
    fill: $primary;
  }

  // NProgress
  #nprogress .bar {
    background-color: $primary;
  }

  #nprogress .peg {
    box-shadow: 0 0 10px $primary, 0 0 5px $primary;
  }

  // Vueform Slider
  --slider-bg: #{$body-bg-dark};
  --slider-connect-bg: #{$primary};
  --slider-tooltip-bg: #{$primary};
  --slider-handle-ring-color: #{$primary};

  // Vue Select
  .vs__dropdown-option--highlight {
    background: $primary;
  }

  .vs__dropdown-toggle,
  .vs__dropdown-menu {
    border-color: $input-border-color;
  }

  .vs__selected {
    background: $body-bg;
    border-color: $body-bg;
  }
}
