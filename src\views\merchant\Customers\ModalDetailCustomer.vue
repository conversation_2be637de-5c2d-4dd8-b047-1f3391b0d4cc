<script setup>
import moment from "moment";
import { reactive, watch } from "vue";
import { Dataset, DatasetItem, DatasetInfo, DatasetPager } from "vue-dataset";
import EListEmpty from "@/components/Elements/EListEmpty.vue";
import { useI18n } from "vue-i18n";
import Trans from "@/i18n/i18nUtils";

const props = defineProps(["listOrder"]);
const { t, locale } = useI18n();
// Helper variables
const cols = reactive([
  {
    name: t("pages.order.fields.id"),
    field: "id",
    sort: "",
  },
  {
    name: t("pages.order.fields.order"),
    field: "Order",
    sort: "",
  },
  {
    name: t("pages.order.fields.customer"),
    field: "customer",
    sort: "",
  },
  {
    name: t("pages.order.fields.order_nr"),
    field: "order_nr",
    sort: "",
  },
  {
    name: t("pages.order.fields.queue_nr"),
    field: "queue_nr",
    sort: "",
  },
  {
    name: t("pages.order.fields.status"),
    field: "status",
    sort: "",
  },
  {
    name: t("pages.order.fields.created_at"),
    field: "created_at",
    sort: "",
  },
  {
    name: t("pages.order.fields.table_no"),
    field: "table_no",
    sort: "",
  },
  {
    name: t("pages.order.fields.items"),
    field: "items",
    sort: "",
  },
  {
    name: t("pages.order.fields.total"),
    field: "totals",
    sort: "",
  },
]);

const updateCols = () => {
  cols[0].name = t("pages.order.fields.id");
  cols[1].name = t("pages.order.fields.order");
  cols[2].name = t("pages.order.fields.customer");
  cols[3].name = t("pages.order.fields.order_nr");
  cols[4].name = t("pages.order.fields.queue_nr");
  cols[5].name = t("pages.order.fields.status");
  cols[6].name = t("pages.order.fields.created_at");
  cols[7].name = t("pages.order.fields.table_no");
  cols[8].name = t("pages.order.fields.items");
  cols[8].name = t("pages.order.fields.total");
};

// Watch for language changes
watch(locale, () => {
  updateCols();
});
// const renderCustomerName = (id) => {
//   const customer = props.listOrder.value?.find((itm) => itm?.id === id)?.name;
//   return customer === 'undefined' ? 'Unavailable': customer;
// };
</script>

<template>
  <div class="p-4">
    <Dataset :ds-data="props.listOrder">
      <div class="row" v-if="props.listOrder?.length">
        <div class="col-md-12">
          <div class="table-responsive">
            <table class="table table-striped mb-0">
              <thead>
                <tr>
                  <th v-for="th in cols" :key="th.field">
                    {{ th.name }} <i class="gg-select float-end"></i>
                  </th>
                </tr>
              </thead>
              <DatasetItem tag="tbody" class="fs-sm">
                <template #default="{ row }">
                  <tr>
                    <td style="width: 80px">{{ row.id }}</td>
                    <td style="color: #f59e0b">
                      <router-link
                        :to="Trans.i18nRouteByPath('/orders/' + row.id)"
                        @click="$emit('close')"
                        >{{ row.code_show }}</router-link
                      >
                    </td>
                    <td style="color: #f59e0b">
                      <span>{{
                        row?.customer?.name === "undefined"
                          ? "Unavailable"
                          : row?.customer?.name
                      }}</span>
                    </td>
                    <td>{{ row.queue_nr }}</td>
                    <td>{{ row.queue_nr }}</td>
                    <td>
                      <span v-if="row.status === 'completed'">
                        <i class="fa-regular fa-check-circle done-icon"></i>
                      </span>
                      <span v-else-if="row.status === 'uncompleted'">
                        <i class="fa fa-times-circle error-icon"></i>
                      </span>
                      <span v-else>
                        <i class="fa fa-dollar refund-icon"></i>
                      </span>
                    </td>
                    <td>
                      {{ moment(row.created_at).format("DD-MM-YYYY HH:mm:ss") }}
                    </td>
                    <td>{{ row.table?.name }}</td>
                    <td>{{ row.items }}</td>
                    <td>{{ Number(row.amount_total).toFixed(2) + ",-kr" }}</td>
                  </tr>
                </template>
              </DatasetItem>
            </table>
          </div>
        </div>
      </div>
      <EListEmpty v-else />
      <div
        class="d-flex flex-md-row flex-column justify-content-between align-items-center"
      >
        <DatasetInfo class="py-3 fs-sm" />
        <DatasetPager class="flex-wrap py-3 fs-sm" />
      </div>
    </Dataset>
  </div>
</template>

<style lang="scss">
.done-icon {
  color: #019521;
  font-size: 26px;
}
.error-icon {
  color: #cd0808;
  font-size: 26px;
}
.refund-icon {
  color: white;
  background-color: gray;
  border: 1px solid;
  padding: 6px 9px;
  border-radius: 50px;
}
</style>
