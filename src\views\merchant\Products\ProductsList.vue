<script setup>
import { computed, onMounted, reactive, ref, watch } from "vue";
import { useTemplateStore } from "@/stores/template";
import { scrollTo } from "@/stores/scollItemInlist";
import { useI18n } from "vue-i18n";
import useAppRouter from "@/composables/useRouter";
import { useDebounceFn } from "@vueuse/core";
import { useCookies } from "vue3-cookies";
import { EMPLOYEE } from "@/data/role";
import CountDown from "@/components/CountDown.vue";
import { storeData } from "@/stores/storeData";
import { searchValue } from "@/stores/searchData";

const limit = ref(10);
const scrollStore = scrollTo();
const searchStore = searchValue();
const currentPage = ref(1);
const totalPage = ref(1);
const total = ref();
const search = ref();
const { t, locale } = useI18n();
const store = useTemplateStore();
const dataFetch = storeData();
let toast = Swal.mixin({
  buttonsStyling: false,
  target: "#page-container",
  customClass: {
    confirmButton: "btn btn-success m-1",
    cancelButton: "btn btn-danger m-1",
    input: "form-control",
  },
});
const router = useAppRouter();
const { userInfo } = useAuth();
const isActiveSuggest = computed(
  () => userInfo?.value?.plans?.find((itm) => itm?.id === 11)?.active
);
const onNavForm = (id) => {
  if (id) {
    router.pushByPath(`/products/form?id=${id}`);
  } else {
    router.pushByName({ name: "merchant-products-form" });
  }
};
const onNavEdit = (id) => {
  if (userRole === EMPLOYEE) {
    return;
  }
  router.pushByPath(`/products/form/${id}`);
};

import { Dataset, DatasetItem, DatasetInfo, DatasetShow } from "vue-dataset";

import EButton from "@/components/Elements/EButton.vue";
import { productService } from "@/services/product.service";
import EListEmpty from "@/components/Elements/EListEmpty.vue";
import Swal from "sweetalert2";
import useNotify from "@/composables/useNotify";
import useAuth from "@/composables/useAuth";

let cols = reactive([
  {
    name: t(`pages.products.fields.product`),
    field: "product",
    sort: "",
  },
  {
    name: t(`pages.products.fields.suggested`),
    field: "suggest",
    sort: "",
    style: {
      textAlign: "center",
    },
  },
  {
    name: t(`pages.products.fields.category`),
    field: "category",
    sort: "",
  },
  {
    name: t(`pages.products.fields.in_stock`),
    field: "stock",
    sort: "",
  },
  {
    name: t(`pages.products.fields.printer`),
    field: "printer",
    sort: "",
  },
  {
    name: t(`pages.products.fields.price`),
    field: "price",
    sort: "",
  },
]);

const updateCols = () => {
  cols[0].name = t("pages.products.fields.product");
  cols[1].name = t("pages.products.fields.suggested");
  cols[2].name = t("pages.products.fields.category");
  cols[3].name = t("pages.products.fields.in_stock");
  cols[4].name = t("pages.products.fields.printer");
  cols[5].name = t("pages.products.fields.price");
};

// Watch for language changes
watch(locale, () => {
  updateCols();
});
const dialogVisible = ref(false);
const time = ref(9);
const options = ref([
  {
    value: 9,
    label: "Permanent",
  },
  {
    value: 1,
    label: "15 minutes",
  },
  {
    value: 2,
    label: "30 minutes",
  },
  {
    value: 3,
    label: "1 hour",
  },
  {
    value: 4,
    label: "3 hours",
  },
  {
    value: 5,
    label: "6 hours",
  },
  {
    value: 6,
    label: "8 hours",
  },
  {
    value: 7,
    label: "1 day",
  },
  {
    value: 8,
    label: "1 week",
  },
]);
const optionsNor = ref([
  {
    value: 9,
    label: "Permanent",
  },
  {
    value: 1,
    label: "15 minutter",
  },
  {
    value: 2,
    label: "30 minutter",
  },
  {
    value: 3,
    label: "1 time",
  },
  {
    value: 4,
    label: "3 time",
  },
  {
    value: 5,
    label: "6 timer",
  },
  {
    value: 6,
    label: "8 timer",
  },
  {
    value: 7,
    label: "1 dag",
  },
  {
    value: 8,
    label: "1 uke",
  },
]);
const productId = ref();
const productInStockValue = ref();
const { cookies } = useCookies();
const userRole = cookies.get("role_merchant");

const openChangeInStock = (id, value) => {
  if (value) {
    dialogVisible.value = true;
    productId.value = id;
    time.value = 9;
    productInStockValue.value = value;
  } else {
    onChangeInStock(id, value, undefined);
  }
};
const ChangeStock = () => {
  onChangeInStock(productId.value, productId.value, time.value);
  dialogVisible.value = false;
};
const listCol = computed(() => {
  if (!isActiveSuggest.value) {
    return cols?.filter((itm) => itm?.field !== "suggest");
  }
  return cols;
});
const sortBy = computed(() => {
  return cols.reduce((acc, o) => {
    if (o.sort) {
      o.sort === "asc" ? acc.push(o.field) : acc.push("-" + o.field);
    }
    return acc;
  }, []);
});

// On sort th click
function onSort(event, i) {
  let toset;
  const sortEl = cols[i];

  if (!event.shiftKey) {
    cols.forEach((o) => {
      if (o.field !== sortEl.field) {
        o.sort = "";
      }
    });
  }

  if (!sortEl.sort) {
    toset = "asc";
  }

  if (sortEl.sort === "desc") {
    toset = event.shiftKey ? "" : "asc";
  }

  if (sortEl.sort === "asc") {
    toset = "desc";
  }

  sortEl.sort = toset;
}

const listProducts = ref();
const visible = ref(true);
const onFetchList = async () => {
  try {
    store.pageLoader({ mode: "on" });
    // NProgress.start()
    scrollStore.setPage(currentPage.value);
    searchStore.setValueSearch(search?.value);
    const response = await productService.getList({
      limit: limit.value,
      page: currentPage.value,
      search: search?.value,
    });
    if (!response.error) {
      listProducts.value = response.data?.data || [];
      totalPage.value = response.data.meta.last_page;
      total.value = response.data.meta.total;
    }
  } catch (e) {
    console.log(e);
  } finally {
    store.pageLoader({ mode: "off" });
  }

  // NProgress.done()
};

watch([limit], async () => {
  currentPage.value = 1;
  await onFetchList();
});

const onOpenDeleteConfirm = (id) => {
  toast
    .fire({
      title: "Are you sure?",
      text: "You will not be able to recover this product!",
      icon: "warning",
      showCancelButton: true,
      customClass: {
        confirmButton: "btn btn-danger m-1",
        cancelButton: "btn btn-info m-1",
      },
      confirmButtonText: "Yes, delete!",
      html: false,
      preConfirm: () => {
        return productService.delete(id);
      },
    })
    .then((result) => {
      if (result.value && !result.value?.error) {
        toast.fire("Deleted!", "Product has been deleted.", "success");
        onFetchList();
        dataFetch.setData([], "merchant-products-list");
        dataFetch.setTotal(0, "merchant-products-list");
      } else if (result.dismiss === "cancel") {
        toast.fire("Cancelled", "Product is safe", "error");
      }
    });
};
const { setNotify } = useNotify();
const onChangeInStock = async (id, inStock, type) => {
  const inStockValue = ref();
  if (inStock) {
    inStockValue.value = 0;
  } else {
    inStockValue.value = 1;
  }
  const payload = {
    in_stock: inStockValue.value,
    restock_type: type,
  };
  try {
    const result = await productService.updateStock(id, payload);
    if (!result?.error) {
      await onFetchList();
      dataFetch.setData([], "merchant-products-list");
      dataFetch.setTotal(0, "merchant-products-list");
    }
  } catch (e) {
    setNotify({
      title: "Error",
      message: e?.message,
    });
  }
};

const handleChangeSearch = useDebounceFn((e) => {
  search.value = e?.target?.value;
  onFetchList();
}, 500);

const handleChangeSuggest = async (val, id) => {
  if (userRole === EMPLOYEE) {
    return setNotify({
      title: "Error",
      message: "Permission Denied",
    });
  }
  const payload = {
    is_suggested: Number(val),
  };
  try {
    const result = await productService.suggest(id, payload);
    if (!result?.error) {
      await onFetchList();
    }
    setNotify({
      title: "Success",
      message: result?.message,
      type: "success",
    });
  } catch (e) {
    setNotify({
      title: "Error",
      message: e?.message,
    });
  }
};

onMounted(async () => {
  currentPage.value = scrollStore.page;
  search.value = searchStore.search;
  document.querySelectorAll("#datasetLength label").forEach((el) => {
    el.remove();
  });
  let selectLength = document.querySelector("#datasetLength select");
  selectLength.classList = "";
  selectLength.classList.add("form-select");
  selectLength.style.width = "80px";
  store.pageLoader({ mode: "on" });

  if (dataFetch.storeData?.["merchant-products-list"]?.length > 0) {
    await (listProducts.value =
      dataFetch.storeData?.["merchant-products-list"]);
    total.value = dataFetch.total?.["merchant-products-list"];
    store.pageLoader({ mode: "off" });
  } else {
    await onFetchList();
  }

  if (scrollStore.formUpdateSuccess) {
    const item = document.querySelector(`.productItem-${scrollStore.idItem}`);
    item.scrollIntoView({
      behavior: "smooth",
      block: "start",
    });
    scrollStore.setUpdateSuccess();
  }
});
</script>

<template>
  <BasePageHeading
    :title="t('pages.products.name')"
    :subtitle="t('pages.products.labels.label_head_list')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.products.labels.manage")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            {{ t("pages.products.name") }}
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>
  <div class="content">
    <BaseBlock :title="t('pages.products.name')">
      <template #options>
        <e-button
          type="info"
          size="sm"
          @click="onNavForm"
          v-if="userRole !== EMPLOYEE"
          ><i class="fa fa-plus opacity-50 me-1"></i>
          {{ t("pages.products.titles.add") }}
        </e-button>
      </template>
      <Dataset
        v-slot="{ ds }"
        :ds-data="listProducts"
        :ds-sortby="sortBy"
        :ds-search-in="['name']"
      >
        <div class="row" :data-page-count="ds.dsPagecount">
          <div id="datasetLength" class="col-md-8 py-2">
            <DatasetShow v-show="false" :dsShowEntries="100" />
            <div class="form-inline">
              <select class="form-select" style="width: 80px" v-model="limit">
                <option :value="5">5</option>
                <option :value="10">10</option>
                <option :value="25">25</option>
                <option :value="50">50</option>
                <option :value="100">100</option>
              </select>
            </div>
          </div>
          <div class="col-md-4 py-2">
            <input
              v-model="search"
              type="text"
              class="form-control"
              id="form-name"
              :placeholder="t('pages.placeholder.enter')"
              @input="handleChangeSearch"
            />
          </div>
        </div>
        <hr />
        <div class="row" v-if="listProducts?.length">
          <div class="col-md-12">
            <div class="table-responsive">
              <table class="table table-striped mb-0">
                <thead>
                  <tr>
                    <th scope="col">{{ t("pages.products.fields.id") }}</th>
                    <th
                      v-for="(th, index) in listCol"
                      :key="th.field"
                      :class="['sort', th.sort]"
                      :style="th?.style"
                      @click="onSort($event, index)"
                    >
                      {{ th.name }} <i class="gg-select float-end"></i>
                    </th>
                    <th
                      scope="col"
                      style="width: 100px"
                      v-if="userRole !== EMPLOYEE"
                    >
                      {{ t("pages.products.fields.action") }}
                    </th>
                  </tr>
                </thead>
                <DatasetItem tag="tbody" class="fs-sm">
                  <template #default="{ row }">
                    <tr :class="`productItem-${row?.id}`">
                      <th scope="row">{{ row?.id }}</th>
                      <td>
                        <div class="d-flex flex-nowrap">
                          <img
                            @click="onNavEdit(row?.id)"
                            :src="row?.thumbnail"
                            width="60"
                            height="60"
                            class="me-2 cursor-pointer"
                            @error.prevent="
                              (e) =>
                                (e.target.src =
                                  '/assets/media/photos/default_photo.png')
                            "
                          />
                          <p
                            @click="onNavEdit(row?.id)"
                            class="cursor-pointer btn btn-link"
                          >
                            {{ row?.name }}
                          </p>
                        </div>
                      </td>
                      <td v-if="!!isActiveSuggest" style="text-align: center">
                        <i
                          v-if="!row.is_suggested"
                          :title="t('pages.products.fields.suggest')"
                          class="fa-regular fa-star fa-xl cursor-pointer text-danger"
                          @click="() => handleChangeSuggest(true, row?.id)"
                        ></i>
                        <i
                          v-else
                          :title="t('pages.products.fields.suggest')"
                          class="fa fa-fw fa-star fa-xl cursor-pointer text-danger"
                          @click="() => handleChangeSuggest(false, row?.id)"
                        ></i>
                      </td>
                      <td>{{ row?.category?.name }}</td>
                      <td style="max-width: 150px">
                        <div
                          class="form-check form-switch"
                          @click="openChangeInStock(row?.id, row?.in_stock)"
                        >
                          <input
                            style="pointer-events: none"
                            class="form-check-input"
                            type="checkbox"
                            :checked="row?.in_stock"
                          />
                        </div>
                        <CountDown
                          @load="onFetchList"
                          v-if="!row?.in_stock && row?.restock_at"
                          :targetDate="row?.restock_at"
                        />
                        <div class="text-danger" v-else-if="!row?.in_stock">
                          Permanent
                        </div>
                      </td>
                      <td>{{ row?.printer?.name }}</td>
                      <td>{{ row?.ordinary_price }}</td>
                      <td v-if="userRole !== EMPLOYEE">
                        <div class="btn-group">
                          <button
                            type="button"
                            class="btn btn-sm btn-alt-secondary"
                            @click="onNavEdit(row?.id)"
                          >
                            <i class="fa fa-fw fa-pencil-alt"></i>
                          </button>
                          <button
                            type="button"
                            class="btn btn-sm btn-alt-secondary"
                            @click="onNavForm(row.id)"
                          >
                            <i class="fa fa-fw fa-clone"></i>
                          </button>
                          <button
                            type="button"
                            class="btn btn-sm btn-alt-secondary"
                            @click="onOpenDeleteConfirm(row.id)"
                          >
                            <i class="fa fa-fw fa-times"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </template>
                </DatasetItem>
              </table>
            </div>
          </div>
        </div>
        <EListEmpty v-else />
        <div
          class="d-flex flex-md-row flex-column justify-content-between align-items-center"
        >
          <DatasetInfo class="py-3 fs-sm" />
          <el-pagination
            v-if="visible"
            v-model:current-page="currentPage"
            @current-change="onFetchList"
            background
            v-model:page-size="limit"
            layout="prev, pager, next"
            :prev-text="t('pages.footer.previous')"
            :next-text="t('pages.footer.next')"
            :total="total"
          />
        </div>
      </Dataset>
    </BaseBlock>
    <el-dialog
      v-model="dialogVisible"
      :title="t('pages.products.labels.ask_stock')"
      width="400"
    >
      <div class="dialog-container">
        <el-select-v2
          v-model="time"
          :options="locale === 'en' ? options : optionsNor"
          placeholder="Please select"
          size="large"
        />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="cancel-button">
            <el-button @click="dialogVisible = false">{{
              t("pages.products.buttons.cancel")
            }}</el-button>
          </div>
          <el-button type="primary" @click="ChangeStock">
            {{ t("pages.products.buttons.confirm") }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<style scoped>
.cursor-pointer {
  cursor: pointer;
}
.cancel-button ::v-deep .el-button {
  background-color: #ebeef2 !important;
  color: #212529 !important;
  border-color: #ebeef2 !important;
}
.cancel-button {
  display: inline-block;
}
.dialog-container {
  padding: 20px 30px 0 30px;
}
</style>
