# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@aashutoshrathi/word-wrap@^1.2.3":
  "integrity" "sha512-1Yjs2SvM8TflER/OD3cOjhWWOZb58A2t7wpE2S9XfBYTiIl+XFhQG2bjy4Pu1I+EAlCNUzRDYDdFwFYUKvXcIA=="
  "resolved" "https://registry.npmjs.org/@aashutoshrathi/word-wrap/-/word-wrap-1.2.6.tgz"
  "version" "1.2.6"

"@babel/helper-string-parser@^7.25.7":
  "integrity" "sha512-CbkjYdsJNHFk8uqpEkpCvRs3YRp9tY6FmFY7wLMSYuGYkrdUi7r2lc4/wqsvlHoMznX3WJ9IP8giGPq68T/Y6g=="
  "resolved" "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.25.7.tgz"
  "version" "7.25.7"

"@babel/helper-validator-identifier@^7.25.7":
  "integrity" "sha512-AM6TzwYqGChO45oiuPqwL2t20/HdMC1rTPAesnBCgPCSF1x3oN9MVUwQV2iyz4xqWrctwK5RNC8LV22kaQCNYg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.25.7.tgz"
  "version" "7.25.7"

"@babel/parser@^7.25.3":
  "integrity" "sha512-aZn7ETtQsjjGG5HruveUK06cU3Hljuhd9Iojm4M8WWv3wLE6OkE5PWbDUkItmMgegmccaITudyuW5RPYrYlgWw=="
  "resolved" "https://registry.npmjs.org/@babel/parser/-/parser-7.25.7.tgz"
  "version" "7.25.7"
  dependencies:
    "@babel/types" "^7.25.7"

"@babel/runtime@^7.9.2":
  "integrity" "sha512-0CX6F+BI2s9dkUqr08KFrAIZgNFj75rdBU/DjCyYLIaV/quFjkk6T+EJ2LkZHyZTbEV4L5p97mNkUsHl2wLFAw=="
  "resolved" "https://registry.npmjs.org/@babel/runtime/-/runtime-7.23.9.tgz"
  "version" "7.23.9"
  dependencies:
    "regenerator-runtime" "^0.14.0"

"@babel/types@^7.25.7":
  "integrity" "sha512-vwIVdXG+j+FOpkwqHRcBgHLYNL7XMkufrlaFvL9o6Ai9sJn9+PdyIL5qa0XzTZw084c+u9LOls53eoZWP/W5WQ=="
  "resolved" "https://registry.npmjs.org/@babel/types/-/types-7.25.7.tgz"
  "version" "7.25.7"
  dependencies:
    "@babel/helper-string-parser" "^7.25.7"
    "@babel/helper-validator-identifier" "^7.25.7"
    "to-fast-properties" "^2.0.0"

"@chenfengyuan/vue-countdown@^2.1.2":
  "integrity" "sha512-/XDKIQzDDBc+4hXb681B1+ySKrStCTZONspRp+TojMcMe1edpCpuk4E/QoNVYxyZ24d+pcwN1YqnfIDgc7haBA=="
  "resolved" "https://registry.npmjs.org/@chenfengyuan/vue-countdown/-/vue-countdown-2.1.2.tgz"
  "version" "2.1.2"

"@ckeditor/ckeditor5-adapter-ckfinder@40.2.0":
  "integrity" "sha512-YKdydg4DzaMk91saOL55KBNQE3St2NEj1E9hlk9CzHKQaHc79dYzHDNBolSE7ZmzkNJ4ToVbY7kRW5CDGfG5Rg=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-adapter-ckfinder/-/ckeditor5-adapter-ckfinder-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "ckeditor5" "40.2.0"

"@ckeditor/ckeditor5-autoformat@40.2.0":
  "integrity" "sha512-F3w5k7ti5l6V8U07eSQ3gup3ivltRZQXdtvstBXMmTzDb2ceazNcUDLb6TKSHp5y30ETN0dRGgbhx9xiDL0TXg=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-autoformat/-/ckeditor5-autoformat-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "ckeditor5" "40.2.0"

"@ckeditor/ckeditor5-basic-styles@40.2.0":
  "integrity" "sha512-P7jYddLnRpaR4zVCqDa8InsZ6YNRHdF0RrX6+Uz81+A1IfyfmSd+5IaiLxxdnFWQ4JlEhJutjy9vMwSmOhZocQ=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-basic-styles/-/ckeditor5-basic-styles-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "ckeditor5" "40.2.0"

"@ckeditor/ckeditor5-block-quote@40.2.0":
  "integrity" "sha512-t03Yp+MeAyQhwdGZqUlkJEx25VSiigpzkIGGOhccSaTIIZ9XcWDkrTevDhwA4Pq4Q9IRQ8Loj3KCVSBuAqkBgw=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-block-quote/-/ckeditor5-block-quote-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "ckeditor5" "40.2.0"

"@ckeditor/ckeditor5-build-balloon-block@^40.0.0":
  "integrity" "sha512-AnXsUZJ/dWcW4uGFpqkXD+nl/ddY9ZjvTXP2Rlwmb4Cjl5cU1F8MjW7icVlLdczIA2KNoNJjnBZPKvdiela2Jw=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-build-balloon-block/-/ckeditor5-build-balloon-block-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "@ckeditor/ckeditor5-adapter-ckfinder" "40.2.0"
    "@ckeditor/ckeditor5-autoformat" "40.2.0"
    "@ckeditor/ckeditor5-basic-styles" "40.2.0"
    "@ckeditor/ckeditor5-block-quote" "40.2.0"
    "@ckeditor/ckeditor5-ckbox" "40.2.0"
    "@ckeditor/ckeditor5-ckfinder" "40.2.0"
    "@ckeditor/ckeditor5-cloud-services" "40.2.0"
    "@ckeditor/ckeditor5-easy-image" "40.2.0"
    "@ckeditor/ckeditor5-editor-balloon" "40.2.0"
    "@ckeditor/ckeditor5-essentials" "40.2.0"
    "@ckeditor/ckeditor5-heading" "40.2.0"
    "@ckeditor/ckeditor5-image" "40.2.0"
    "@ckeditor/ckeditor5-indent" "40.2.0"
    "@ckeditor/ckeditor5-link" "40.2.0"
    "@ckeditor/ckeditor5-list" "40.2.0"
    "@ckeditor/ckeditor5-media-embed" "40.2.0"
    "@ckeditor/ckeditor5-paragraph" "40.2.0"
    "@ckeditor/ckeditor5-paste-from-office" "40.2.0"
    "@ckeditor/ckeditor5-table" "40.2.0"
    "@ckeditor/ckeditor5-typing" "40.2.0"
    "@ckeditor/ckeditor5-ui" "40.2.0"

"@ckeditor/ckeditor5-build-balloon@^40.0.0":
  "integrity" "sha512-diwpnpqXnw1uPgBx3MQylH0wRNOAVuvHykaNYJgM2pzXDogPHJ7J0niNKk8FdMlEGWQognLkySI56vjCZmT8cA=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-build-balloon/-/ckeditor5-build-balloon-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "@ckeditor/ckeditor5-adapter-ckfinder" "40.2.0"
    "@ckeditor/ckeditor5-autoformat" "40.2.0"
    "@ckeditor/ckeditor5-basic-styles" "40.2.0"
    "@ckeditor/ckeditor5-block-quote" "40.2.0"
    "@ckeditor/ckeditor5-ckbox" "40.2.0"
    "@ckeditor/ckeditor5-ckfinder" "40.2.0"
    "@ckeditor/ckeditor5-cloud-services" "40.2.0"
    "@ckeditor/ckeditor5-easy-image" "40.2.0"
    "@ckeditor/ckeditor5-editor-balloon" "40.2.0"
    "@ckeditor/ckeditor5-essentials" "40.2.0"
    "@ckeditor/ckeditor5-heading" "40.2.0"
    "@ckeditor/ckeditor5-image" "40.2.0"
    "@ckeditor/ckeditor5-indent" "40.2.0"
    "@ckeditor/ckeditor5-link" "40.2.0"
    "@ckeditor/ckeditor5-list" "40.2.0"
    "@ckeditor/ckeditor5-media-embed" "40.2.0"
    "@ckeditor/ckeditor5-paragraph" "40.2.0"
    "@ckeditor/ckeditor5-paste-from-office" "40.2.0"
    "@ckeditor/ckeditor5-table" "40.2.0"
    "@ckeditor/ckeditor5-typing" "40.2.0"

"@ckeditor/ckeditor5-build-classic@^40.0.0":
  "integrity" "sha512-b9zt1kU0S2Azco8mXraxj56pctHRs9y/XfdVrpFzxiRbbzyBZ42WgX1ThJNTqH6WkXCxDPYSbqPhNIvPHpuEyg=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-build-classic/-/ckeditor5-build-classic-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "@ckeditor/ckeditor5-adapter-ckfinder" "40.2.0"
    "@ckeditor/ckeditor5-autoformat" "40.2.0"
    "@ckeditor/ckeditor5-basic-styles" "40.2.0"
    "@ckeditor/ckeditor5-block-quote" "40.2.0"
    "@ckeditor/ckeditor5-ckbox" "40.2.0"
    "@ckeditor/ckeditor5-ckfinder" "40.2.0"
    "@ckeditor/ckeditor5-cloud-services" "40.2.0"
    "@ckeditor/ckeditor5-easy-image" "40.2.0"
    "@ckeditor/ckeditor5-editor-classic" "40.2.0"
    "@ckeditor/ckeditor5-essentials" "40.2.0"
    "@ckeditor/ckeditor5-heading" "40.2.0"
    "@ckeditor/ckeditor5-image" "40.2.0"
    "@ckeditor/ckeditor5-indent" "40.2.0"
    "@ckeditor/ckeditor5-link" "40.2.0"
    "@ckeditor/ckeditor5-list" "40.2.0"
    "@ckeditor/ckeditor5-media-embed" "40.2.0"
    "@ckeditor/ckeditor5-paragraph" "40.2.0"
    "@ckeditor/ckeditor5-paste-from-office" "40.2.0"
    "@ckeditor/ckeditor5-table" "40.2.0"
    "@ckeditor/ckeditor5-typing" "40.2.0"

"@ckeditor/ckeditor5-build-inline@^40.0.0":
  "integrity" "sha512-7xHEIJ6k15r89ElCEO7NeLGZN/A1O1ezc+seSX3VJZIOY2KDDjgTR8kAlyt/hY8Q5NXCrCrwwnp4OpQ+ZM8lCA=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-build-inline/-/ckeditor5-build-inline-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "@ckeditor/ckeditor5-adapter-ckfinder" "40.2.0"
    "@ckeditor/ckeditor5-autoformat" "40.2.0"
    "@ckeditor/ckeditor5-basic-styles" "40.2.0"
    "@ckeditor/ckeditor5-block-quote" "40.2.0"
    "@ckeditor/ckeditor5-ckbox" "40.2.0"
    "@ckeditor/ckeditor5-ckfinder" "40.2.0"
    "@ckeditor/ckeditor5-cloud-services" "40.2.0"
    "@ckeditor/ckeditor5-easy-image" "40.2.0"
    "@ckeditor/ckeditor5-editor-inline" "40.2.0"
    "@ckeditor/ckeditor5-essentials" "40.2.0"
    "@ckeditor/ckeditor5-heading" "40.2.0"
    "@ckeditor/ckeditor5-image" "40.2.0"
    "@ckeditor/ckeditor5-indent" "40.2.0"
    "@ckeditor/ckeditor5-link" "40.2.0"
    "@ckeditor/ckeditor5-list" "40.2.0"
    "@ckeditor/ckeditor5-media-embed" "40.2.0"
    "@ckeditor/ckeditor5-paragraph" "40.2.0"
    "@ckeditor/ckeditor5-paste-from-office" "40.2.0"
    "@ckeditor/ckeditor5-table" "40.2.0"
    "@ckeditor/ckeditor5-typing" "40.2.0"

"@ckeditor/ckeditor5-ckbox@40.2.0":
  "integrity" "sha512-w/A3RA7qpR7Scl4hgUTt8j+oV7oD9IFPNGTpp0xoyfWEV8Ymm2NrMAvV0PAJiavYS3+FT4GO4RBOM6BvLHuV3w=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-ckbox/-/ckeditor5-ckbox-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "blurhash" "2.0.5"
    "ckeditor5" "40.2.0"
    "lodash-es" "4.17.21"

"@ckeditor/ckeditor5-ckfinder@40.2.0":
  "integrity" "sha512-kiW5TZOLHYd6hhWeDIrep8FXLo0q14b0e0Jit0XSi3z4PQfdDSRk9UuMJPkkf4EdF3PUSdMJ9bccJG76DYXzFQ=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-ckfinder/-/ckeditor5-ckfinder-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "ckeditor5" "40.2.0"

"@ckeditor/ckeditor5-clipboard@40.2.0":
  "integrity" "sha512-8/xPH9/i86ukcEiHdmTgNuPVJeYTrivbx5ZYqycPO4Eem7VM99gIbOe7pIYpuV+klr9ymVxIHbGyTJDJ3oUO8A=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-clipboard/-/ckeditor5-clipboard-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "@ckeditor/ckeditor5-core" "40.2.0"
    "@ckeditor/ckeditor5-engine" "40.2.0"
    "@ckeditor/ckeditor5-ui" "40.2.0"
    "@ckeditor/ckeditor5-utils" "40.2.0"
    "@ckeditor/ckeditor5-widget" "40.2.0"
    "lodash-es" "4.17.21"

"@ckeditor/ckeditor5-cloud-services@40.2.0":
  "integrity" "sha512-xrPQjFGGy1ZfXyKIMw0uzn7OIriSv13YMw0M2ZTh+V1jRN1HW9KzzNb2DlQc+6wQfRk0bq69XGLp3M296/cVTQ=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-cloud-services/-/ckeditor5-cloud-services-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "ckeditor5" "40.2.0"

"@ckeditor/ckeditor5-core@40.2.0":
  "integrity" "sha512-0fqIaN+ZhkXXA3mpBN+alycBzPMc8ruO8VrP0OnvCjowqZVS2HXC2AaXNBdxc75xGI3ScXIor7FsgFHxVJIYYQ=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-core/-/ckeditor5-core-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "@ckeditor/ckeditor5-engine" "40.2.0"
    "@ckeditor/ckeditor5-utils" "40.2.0"
    "lodash-es" "4.17.21"

"@ckeditor/ckeditor5-easy-image@40.2.0":
  "integrity" "sha512-bE9YHGYEY1ql5mIqZSI2fkoBA4th28M0GDaJutwJYKM3t+Bost3Zh1UfmYKE2kvYgmeLY1L3hjQpN9w7NYyLpw=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-easy-image/-/ckeditor5-easy-image-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "ckeditor5" "40.2.0"

"@ckeditor/ckeditor5-editor-balloon@40.2.0":
  "integrity" "sha512-BuE/x96FdJc0JHlRjh1z2eSRl3qJsq6OCPyJQoOYqp9kmqCNjqJqbcbbne/pq36tEp5Xf0wAjolF3o7cEzB0iw=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-balloon/-/ckeditor5-editor-balloon-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "ckeditor5" "40.2.0"
    "lodash-es" "4.17.21"

"@ckeditor/ckeditor5-editor-classic@40.2.0":
  "integrity" "sha512-dftfDBxANOgqgQZ4SB3YTsEV/XX1u0g9jopbOBwqIABnVVa8zoGcktgFdGnLUFk51sL65baSx2z8Z1NNYdZcFQ=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-classic/-/ckeditor5-editor-classic-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "ckeditor5" "40.2.0"
    "lodash-es" "4.17.21"

"@ckeditor/ckeditor5-editor-inline@40.2.0":
  "integrity" "sha512-Ox9lQiCSv0acyKaQLCcoebBjAMRE6L6iCBN8XVeQ3u91KZV6/LOhP+CJ314c8AuH+UHPeJt9MHP6eGU0trKHGQ=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-inline/-/ckeditor5-editor-inline-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "ckeditor5" "40.2.0"
    "lodash-es" "4.17.21"

"@ckeditor/ckeditor5-engine@40.2.0":
  "integrity" "sha512-sgboUX8Ps+LcEgywyT3BeK1nzLHjNVIiZU1qvRxR3ixzIw4w2xRNXCGfESWLW5Y5rv9+ypUCrX61oLnZU64PQQ=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-engine/-/ckeditor5-engine-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "@ckeditor/ckeditor5-utils" "40.2.0"
    "lodash-es" "4.17.21"

"@ckeditor/ckeditor5-enter@40.2.0":
  "integrity" "sha512-GjTRaKNX8QEDJ3YYKG3GfPZfGHrcigGBxbo+1WDT7NaOsR2DA/CIZfHlAPfgJDAMV17bhWsT3gy3+oQZsExtnQ=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-enter/-/ckeditor5-enter-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "@ckeditor/ckeditor5-core" "40.2.0"
    "@ckeditor/ckeditor5-engine" "40.2.0"
    "@ckeditor/ckeditor5-utils" "40.2.0"

"@ckeditor/ckeditor5-essentials@40.2.0":
  "integrity" "sha512-7iUUy0Uwiei4yLrn145SOcyzriMeVFVc5ontQkxQE5b9alFdAc/6ZoDPZqwD7V0zi5RQ/2YsoVMRLFa4hbPfNA=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-essentials/-/ckeditor5-essentials-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "ckeditor5" "40.2.0"

"@ckeditor/ckeditor5-heading@40.2.0":
  "integrity" "sha512-uDT1sttMy+KrKi90jnqEI43886o1wfKrROWqaMbmKOerTbIi58GNH9LvX04sf1RyHV3+3566RRmB248fsLkYjA=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-heading/-/ckeditor5-heading-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "ckeditor5" "40.2.0"

"@ckeditor/ckeditor5-image@40.2.0":
  "integrity" "sha512-0Dunw1o5k2+5Q5XiWLDG1r8k9awosfIFuDZwqKJGWtDaNE4QQbJ9+iJSwiiRw2QjcGr7D3JdH7xwJZFra7kYmA=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-image/-/ckeditor5-image-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "@ckeditor/ckeditor5-ui" "40.2.0"
    "ckeditor5" "40.2.0"
    "lodash-es" "4.17.21"

"@ckeditor/ckeditor5-indent@40.2.0":
  "integrity" "sha512-gSlRGoyAslB2OpqghimIY6Oiflf3Z2/MdLBzvFipU5N4X66cL29HuWZc/bOkcFzWwNeDK5LgzfLdvXNzkdv5Xw=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-indent/-/ckeditor5-indent-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "ckeditor5" "40.2.0"

"@ckeditor/ckeditor5-link@40.2.0":
  "integrity" "sha512-/r4Ti9USdrURBX+qutvyDGOb75sNuSgtXdI8xK503EVfx5yBIi6qsYIYWoFvnGJKkLYkVo+940ilduhwzq0M7g=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-link/-/ckeditor5-link-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "@ckeditor/ckeditor5-ui" "40.2.0"
    "ckeditor5" "40.2.0"
    "lodash-es" "4.17.21"

"@ckeditor/ckeditor5-list@40.2.0":
  "integrity" "sha512-lsQWSLSFRHRQ2AxA6vgTib9YELjF2J5jpR6H4RDW1gM//dL3FjvLxKPPN/V7rMcp15rrpSiOya+qB99l24DEpQ=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-list/-/ckeditor5-list-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "ckeditor5" "40.2.0"

"@ckeditor/ckeditor5-media-embed@40.2.0":
  "integrity" "sha512-ORY7VebL7UTuBG/4++UxzqEKjnlZZKAFqUrIom7xXpQNfo6oJFtZLnKYwESZ6iNk7NBOAeiHEecP2tKWyFQd1g=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-media-embed/-/ckeditor5-media-embed-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "@ckeditor/ckeditor5-ui" "40.2.0"
    "ckeditor5" "40.2.0"

"@ckeditor/ckeditor5-paragraph@40.2.0":
  "integrity" "sha512-NotxWP1cKvbJSY1UwdTe/Oy1NnAj9Etsi4Z7XA908EvCsNSnFtzdMhYzLhFZJ18avrQFDa7PpSKSyN3M64CbSA=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-paragraph/-/ckeditor5-paragraph-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "@ckeditor/ckeditor5-core" "40.2.0"
    "@ckeditor/ckeditor5-ui" "40.2.0"
    "@ckeditor/ckeditor5-utils" "40.2.0"

"@ckeditor/ckeditor5-paste-from-office@40.2.0":
  "integrity" "sha512-kdk7uJlSa9mvyuNAwmIfV6Kc1tfWI6DbCs19jyseA/F0vySKibb0DsBVSZ7xa5ihcjphfJvwpypWYL0BYdYKLQ=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-paste-from-office/-/ckeditor5-paste-from-office-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "ckeditor5" "40.2.0"

"@ckeditor/ckeditor5-select-all@40.2.0":
  "integrity" "sha512-yaYCqhdMcoEH3BsilhweNdbOfuO/cexQ1r1/mYoBoW4CypIuAeq8J/3qLpvFaThmCRPzJBn1J7v2Yjs/0UnamA=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-select-all/-/ckeditor5-select-all-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "@ckeditor/ckeditor5-core" "40.2.0"
    "@ckeditor/ckeditor5-ui" "40.2.0"
    "@ckeditor/ckeditor5-utils" "40.2.0"

"@ckeditor/ckeditor5-table@40.2.0":
  "integrity" "sha512-yODne7az/aJ9lsuI7w476pgGV2QBoH2tOKp3JFh/e2DdHC20637LCVd0cx8sUe3zk61X/eYPY+wOiRJx/mIUqg=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-table/-/ckeditor5-table-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "ckeditor5" "40.2.0"
    "lodash-es" "4.17.21"

"@ckeditor/ckeditor5-typing@40.2.0":
  "integrity" "sha512-2E7LkmC4RHdenMUwow0EZDKxlbX00c5UHysUVT51EBGrXiJcN++0cqxQaeJzQ262oTDpk94qE5IZdGXt3ntzrw=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-typing/-/ckeditor5-typing-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "@ckeditor/ckeditor5-core" "40.2.0"
    "@ckeditor/ckeditor5-engine" "40.2.0"
    "@ckeditor/ckeditor5-utils" "40.2.0"
    "lodash-es" "4.17.21"

"@ckeditor/ckeditor5-ui@40.2.0":
  "integrity" "sha512-K8oC9zrJokZD5Nl4uQjJMo8Couds0eHmfNI/go6iU4A4OAdDzph+W50QnyMed4etKnMdhvUSbnuZnPtQjnsvFA=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-ui/-/ckeditor5-ui-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "@ckeditor/ckeditor5-core" "40.2.0"
    "@ckeditor/ckeditor5-utils" "40.2.0"
    "color-convert" "2.0.1"
    "color-parse" "1.4.2"
    "lodash-es" "4.17.21"
    "vanilla-colorful" "0.7.2"

"@ckeditor/ckeditor5-undo@40.2.0":
  "integrity" "sha512-k2VZS5x4SJtYk3zhdwHYg+D00DgD0iWR0H4qQgcWmQMFRipYvXJRixP3hSLZGJciQanPFeYcjZgxNQ+rU1s8ug=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-undo/-/ckeditor5-undo-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "@ckeditor/ckeditor5-core" "40.2.0"
    "@ckeditor/ckeditor5-engine" "40.2.0"
    "@ckeditor/ckeditor5-ui" "40.2.0"

"@ckeditor/ckeditor5-upload@40.2.0":
  "integrity" "sha512-AdJSKvWEQbSSyA/DfxbCHRhFN6S4ew4kuYETO57e6AS3aOuYGLBRdu9Mub7IAQcOyy1LL6ktr9u5WEOoWS2h0w=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-upload/-/ckeditor5-upload-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "@ckeditor/ckeditor5-core" "40.2.0"
    "@ckeditor/ckeditor5-ui" "40.2.0"
    "@ckeditor/ckeditor5-utils" "40.2.0"

"@ckeditor/ckeditor5-utils@40.2.0":
  "integrity" "sha512-f+kTJBwwk7Y/LXm8pEPxBTXVlJwQrH7Levzye9zxEDB0Jtj7+brGr87o666fPmL/ATQc5M+VPhbvnk2sOv7WKg=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-utils/-/ckeditor5-utils-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "lodash-es" "4.17.21"

"@ckeditor/ckeditor5-vue@^5.1.0":
  "integrity" "sha512-KEx4Tj2Irr4ZbLG8LnaKpb0Dgd8qmLmKFWeiKkQwM3RAAeYRYOCcBVB2Y168I9KA8wRosPxgOO9jbQ92yopYHA=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-vue/-/ckeditor5-vue-5.1.0.tgz"
  "version" "5.1.0"

"@ckeditor/ckeditor5-watchdog@40.2.0":
  "integrity" "sha512-ets7o2dUR7l23G9o/RAbu+gJzUkc2Ul269E3TEhZnbQXFjshvEGK2kzuay7I+/waL3ADuYe4zuoBqsqdPoAhfg=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-watchdog/-/ckeditor5-watchdog-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "lodash-es" "4.17.21"

"@ckeditor/ckeditor5-widget@40.2.0":
  "integrity" "sha512-okeUSwbnu6TUKvwBOl0YdED6Me0/vvs1ybfKZPNEJNwGl989iG0LQO4oYUye8BTCZvzCZ2cBTb1Cvnwr8KRcbg=="
  "resolved" "https://registry.npmjs.org/@ckeditor/ckeditor5-widget/-/ckeditor5-widget-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "@ckeditor/ckeditor5-core" "40.2.0"
    "@ckeditor/ckeditor5-engine" "40.2.0"
    "@ckeditor/ckeditor5-enter" "40.2.0"
    "@ckeditor/ckeditor5-typing" "40.2.0"
    "@ckeditor/ckeditor5-ui" "40.2.0"
    "@ckeditor/ckeditor5-utils" "40.2.0"
    "lodash-es" "4.17.21"

"@ckpack/vue-color@^1.5.0":
  "integrity" "sha512-dj1zXVyay2m4LdlLJCQSdIS2FYwUl77BZqyKmUXiehyqjCP0bGYnPcL38lrShzYUc2FdkYQX8ANZZjRahd4PQw=="
  "resolved" "https://registry.npmjs.org/@ckpack/vue-color/-/vue-color-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "@ctrl/tinycolor" "^3.6.0"
    "material-colors" "^1.2.6"

"@ctrl/tinycolor@^3.4.1", "@ctrl/tinycolor@^3.6.0":
  "integrity" "sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA=="
  "resolved" "https://registry.npmjs.org/@ctrl/tinycolor/-/tinycolor-3.6.1.tgz"
  "version" "3.6.1"

"@element-plus/icons-vue@^2.3.1":
  "integrity" "sha512-XxVUZv48RZAd87ucGS48jPf6pKu0yV5UCg9f4FFwtrYxXOwWuVJo6wOvSLKEoMQKjv8GsX/mhP6UsC1lRwbUWg=="
  "resolved" "https://registry.npmjs.org/@element-plus/icons-vue/-/icons-vue-2.3.1.tgz"
  "version" "2.3.1"

"@esbuild/win32-x64@0.18.20":
  "integrity" "sha512-kTdfRcSiDfQca/y9QIkng02avJ+NCaQvrMejlsB3RRv5sE9rRoeBPISaZpKxHELzRxZyLvNts1P27W3wV+8geQ=="
  "resolved" "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.18.20.tgz"
  "version" "0.18.20"

"@eslint-community/eslint-utils@^4.2.0", "@eslint-community/eslint-utils@^4.4.0":
  "integrity" "sha512-1/sA4dwrzBAyeUoQ6oxahHKmrZvsnLCg4RfxW3ZFGGmQkSNQPFNLV9CUEFQP1x9EYXHTo5p6xdhZM1Ne9p/AfA=="
  "resolved" "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.4.0.tgz"
  "version" "4.4.0"
  dependencies:
    "eslint-visitor-keys" "^3.3.0"

"@eslint-community/regexpp@^4.6.1":
  "integrity" "sha512-Cu96Sd2By9mCNTx2iyKOmq10v22jUVQv0lQnlGNy16oE9589yE+QADPbrMGCkA51cKZSg3Pu/aTJVTGfL/qjUA=="
  "resolved" "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.10.0.tgz"
  "version" "4.10.0"

"@eslint/eslintrc@^2.1.4":
  "integrity" "sha512-269Z39MS6wVJtsoUl10L60WdkhJVdPG24Q4eZTH3nnF6lpvSShEK3wQjDX9JRWAUPvPh7COouPpU9IrqaZFvtQ=="
  "resolved" "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "ajv" "^6.12.4"
    "debug" "^4.3.2"
    "espree" "^9.6.0"
    "globals" "^13.19.0"
    "ignore" "^5.2.0"
    "import-fresh" "^3.2.1"
    "js-yaml" "^4.1.0"
    "minimatch" "^3.1.2"
    "strip-json-comments" "^3.1.1"

"@eslint/js@8.56.0":
  "integrity" "sha512-gMsVel9D7f2HLkBma9VbtzZRehRogVRfbr++f06nL2vnCGCNlzOD+/MUov/F4p8myyAHspEhVobgjpX64q5m6A=="
  "resolved" "https://registry.npmjs.org/@eslint/js/-/js-8.56.0.tgz"
  "version" "8.56.0"

"@floating-ui/core@^1.6.0":
  "integrity" "sha512-yDzVT/Lm101nQ5TCVeK65LtdN7Tj4Qpr9RTXJ2vPFLqtLxwOrpoxAHAJI8J3yYWUc40J0BDBheaitK5SJmno2g=="
  "resolved" "https://registry.npmjs.org/@floating-ui/core/-/core-1.6.7.tgz"
  "version" "1.6.7"
  dependencies:
    "@floating-ui/utils" "^0.2.7"

"@floating-ui/dom@^1.0.1":
  "integrity" "sha512-fskgCFv8J8OamCmyun8MfjB1Olfn+uZKjOKZ0vhYF3gRmEUXcGOjxWL8bBr7i4kIuPZ2KD2S3EUIOxnjC8kl2A=="
  "resolved" "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.6.10.tgz"
  "version" "1.6.10"
  dependencies:
    "@floating-ui/core" "^1.6.0"
    "@floating-ui/utils" "^0.2.7"

"@floating-ui/utils@^0.2.7":
  "integrity" "sha512-X8R8Oj771YRl/w+c1HqAC1szL8zWQRwFvgDwT129k9ACdBoud/+/rX9V0qiMl6LWUdP9voC2nDVZYPMQQsb6eA=="
  "resolved" "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.7.tgz"
  "version" "0.2.7"

"@fortawesome/fontawesome-free@^6.4.2":
  "integrity" "sha512-CNy5vSwN3fsUStPRLX7fUYojyuzoEMSXPl7zSLJ8TgtRfjv24LOnOWKT2zYwaHZCJGkdyRnTmstR0P+Ah503Gw=="
  "resolved" "https://registry.npmjs.org/@fortawesome/fontawesome-free/-/fontawesome-free-6.5.1.tgz"
  "version" "6.5.1"

"@fullcalendar/core@^6.1.9", "@fullcalendar/core@~6.1.11":
  "integrity" "sha512-TjG7c8sUz+Vkui2FyCNJ+xqyu0nq653Ibe99A66LoW95oBo6tVhhKIaG1Wh0GVKymYiqAQN/OEdYTuj4ay27kA=="
  "resolved" "https://registry.npmjs.org/@fullcalendar/core/-/core-6.1.11.tgz"
  "version" "6.1.11"
  dependencies:
    "preact" "~10.12.1"

"@fullcalendar/daygrid@^6.1.9", "@fullcalendar/daygrid@~6.1.11":
  "integrity" "sha512-hF5jJB7cgUIxWD5MVjj8IU407HISyLu7BWXcEIuTytkfr8oolOXeCazqnnjmRbnFOncoJQVstTtq6SIhaT32Xg=="
  "resolved" "https://registry.npmjs.org/@fullcalendar/daygrid/-/daygrid-6.1.11.tgz"
  "version" "6.1.11"

"@fullcalendar/interaction@^6.1.9":
  "integrity" "sha512-ynOKjzuPwEAMgTQ6R/Z2zvzIIqG4p8/Qmnhi1q0vzPZZxSIYx3rlZuvpEK2WGBZZ1XEafDOP/LGfbWoNZe+qdg=="
  "resolved" "https://registry.npmjs.org/@fullcalendar/interaction/-/interaction-6.1.11.tgz"
  "version" "6.1.11"

"@fullcalendar/list@^6.1.9":
  "integrity" "sha512-9Qx8uvik9pXD12u50FiHwNzlHv4wkhfsr+r03ycahW7vEeIAKCsIZGTkUfFP+96I5wHihrfLazu1cFQG4MPiuw=="
  "resolved" "https://registry.npmjs.org/@fullcalendar/list/-/list-6.1.11.tgz"
  "version" "6.1.11"

"@fullcalendar/timegrid@^6.1.9":
  "integrity" "sha512-0seUHK/ferH89IeuCvV4Bib0zWjgK0nsptNdmAc9wDBxD/d9hm5Mdti0URJX6bDoRtsSfRDu5XsRcrzwoc+AUQ=="
  "resolved" "https://registry.npmjs.org/@fullcalendar/timegrid/-/timegrid-6.1.11.tgz"
  "version" "6.1.11"
  dependencies:
    "@fullcalendar/daygrid" "~6.1.11"

"@fullcalendar/vue3@^6.1.9":
  "integrity" "sha512-jBoDS0WSpuOM9ZgjL3lNh6o385u/LthFZDaMUACjVVJZh3JuBbuA7ghdUvIelcTNXa5VRCkSZOpivTJWOnLfcg=="
  "resolved" "https://registry.npmjs.org/@fullcalendar/vue3/-/vue3-6.1.11.tgz"
  "version" "6.1.11"

"@highlightjs/vue-plugin@^2.1.0":
  "integrity" "sha512-E+bmk4ncca+hBEYRV2a+1aIzIV0VSY/e5ArjpuSN9IO7wBJrzUE2u4ESCwrbQD7sAy+jWQjkV5qCCWgc+pu7CQ=="
  "resolved" "https://registry.npmjs.org/@highlightjs/vue-plugin/-/vue-plugin-2.1.0.tgz"
  "version" "2.1.0"

"@humanwhocodes/config-array@^0.11.13":
  "integrity" "sha512-3T8LkOmg45BV5FICb15QQMsyUSWrQ8AygVfC7ZG32zOalnqrilm018ZVCw0eapXux8FtA33q8PSRSstjee3jSg=="
  "resolved" "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.14.tgz"
  "version" "0.11.14"
  dependencies:
    "@humanwhocodes/object-schema" "^2.0.2"
    "debug" "^4.3.1"
    "minimatch" "^3.0.5"

"@humanwhocodes/module-importer@^1.0.1":
  "integrity" "sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA=="
  "resolved" "https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz"
  "version" "1.0.1"

"@humanwhocodes/object-schema@^2.0.2":
  "integrity" "sha512-6EwiSjwWYP7pTckG6I5eyFANjPhmPjUX9JRLUSfNPC7FX7zK9gyZAfUEaECL6ALTpGX5AjnBq3C9XmVWPitNpw=="
  "resolved" "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-2.0.2.tgz"
  "version" "2.0.2"

"@intlify/bundle-utils@^8.0.0":
  "integrity" "sha512-1B++zykRnMwQ+20SpsZI1JCnV/YJt9Oq7AGlEurzkWJOFtFAVqaGc/oV36PBRYeiKnTbY9VYfjBimr2Vt42wLQ=="
  "resolved" "https://registry.npmjs.org/@intlify/bundle-utils/-/bundle-utils-8.0.0.tgz"
  "version" "8.0.0"
  dependencies:
    "@intlify/message-compiler" "^9.4.0"
    "@intlify/shared" "^9.4.0"
    "acorn" "^8.8.2"
    "escodegen" "^2.1.0"
    "estree-walker" "^2.0.2"
    "jsonc-eslint-parser" "^2.3.0"
    "mlly" "^1.2.0"
    "source-map-js" "^1.0.1"
    "yaml-eslint-parser" "^1.2.2"

"@intlify/core-base@9.13.0":
  "integrity" "sha512-Lx8+YTrFpom7AtdbbuJHzgmr612/bceHU92v8ZPU9HU9/rczf+TmCs95BxWPIR4K42xh4MVMLsNzLUWiXcNaLg=="
  "resolved" "https://registry.npmjs.org/@intlify/core-base/-/core-base-9.13.0.tgz"
  "version" "9.13.0"
  dependencies:
    "@intlify/message-compiler" "9.13.0"
    "@intlify/shared" "9.13.0"

"@intlify/message-compiler@^9.4.0", "@intlify/message-compiler@9.13.0":
  "integrity" "sha512-zhESuudiDpFQhUOx/qrSMd7ZYHbmgCc0QzBc27cDUxaaAj3olbYJnsx3osiHPQyYnv/LuC+WTqoNOEBoHP6dqQ=="
  "resolved" "https://registry.npmjs.org/@intlify/message-compiler/-/message-compiler-9.13.0.tgz"
  "version" "9.13.0"
  dependencies:
    "@intlify/shared" "9.13.0"
    "source-map-js" "^1.0.2"

"@intlify/shared@^9.4.0", "@intlify/shared@9.13.0":
  "integrity" "sha512-fUwWcpDz9Wm4dSaz+6XmjoNXWBjZLJtT1Zf1cpLBELbCAOS8WBRscPtgOSfzm6JCqf5KgMI4g917f5TtEeez3A=="
  "resolved" "https://registry.npmjs.org/@intlify/shared/-/shared-9.13.0.tgz"
  "version" "9.13.0"

"@intlify/unplugin-vue-i18n@^4.0.0":
  "integrity" "sha512-q2Mhqa/mLi0tulfLFO4fMXXvEbkSZpI5yGhNNsLTNJJ41icEGUuyDe+j5zRZIKSkOJRgX6YbCyibTDJdRsukmw=="
  "resolved" "https://registry.npmjs.org/@intlify/unplugin-vue-i18n/-/unplugin-vue-i18n-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "@intlify/bundle-utils" "^8.0.0"
    "@intlify/shared" "^9.4.0"
    "@rollup/pluginutils" "^5.1.0"
    "@vue/compiler-sfc" "^3.2.47"
    "debug" "^4.3.3"
    "fast-glob" "^3.2.12"
    "js-yaml" "^4.1.0"
    "json5" "^2.2.3"
    "pathe" "^1.0.0"
    "picocolors" "^1.0.0"
    "source-map-js" "^1.0.2"
    "unplugin" "^1.1.0"

"@jridgewell/sourcemap-codec@^1.5.0":
  "integrity" "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ=="
  "resolved" "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz"
  "version" "1.5.0"

"@kurkle/color@^0.3.0":
  "integrity" "sha512-fuscdXJ9G1qb7W8VdHi+IwRqij3lBkosAm4ydQtEmbY58OzHXqQhvlxqEkoz0yssNVn38bcpRWgA9PP+OGoisw=="
  "resolved" "https://registry.npmjs.org/@kurkle/color/-/color-0.3.2.tgz"
  "version" "0.3.2"

"@nodelib/fs.scandir@2.1.5":
  "integrity" "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  "version" "2.1.5"
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    "run-parallel" "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  "integrity" "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  "version" "2.0.5"

"@nodelib/fs.walk@^1.2.3", "@nodelib/fs.walk@^1.2.8":
  "integrity" "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  "version" "1.2.8"
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    "fastq" "^1.6.0"

"@popperjs/core@^2.11.8", "@popperjs/core@^2.9.0", "@popperjs/core@npm:@sxzz/popperjs-es@^2.11.7":
  "integrity" "sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A=="
  "resolved" "https://registry.npmjs.org/@popperjs/core/-/core-2.11.8.tgz"
  "version" "2.11.8"

"@react-dnd/asap@^5.0.1":
  "integrity" "sha512-WLyfoHvxhs0V9U+GTsGilGgf2QsPl6ZZ44fnv0/b8T3nQyvzxidxsg/ZltbWssbsRDlYW8UKSQMTGotuTotZ6A=="
  "resolved" "https://registry.npmjs.org/@react-dnd/asap/-/asap-5.0.2.tgz"
  "version" "5.0.2"

"@react-dnd/asap@4.0.1":
  "integrity" "sha512-kLy0PJDDwvwwTXxqTFNAAllPHD73AycE9ypWeln/IguoGBEbvFcPDbCV03G52bEcC5E+YgupBE0VzHGdC8SIXg=="
  "resolved" "https://registry.npmjs.org/@react-dnd/asap/-/asap-4.0.1.tgz"
  "version" "4.0.1"

"@react-dnd/invariant@^3.0.1", "@react-dnd/invariant@3.0.1":
  "integrity" "sha512-blqduwV86oiKw2Gr44wbe3pj3Z/OsXirc7ybCv9F/pLAR+Aih8F3rjeJzK0ANgtYKv5lCpkGVoZAeKitKDaD/g=="
  "resolved" "https://registry.npmjs.org/@react-dnd/invariant/-/invariant-3.0.1.tgz"
  "version" "3.0.1"

"@react-dnd/invariant@^4.0.1":
  "integrity" "sha512-xKCTqAK/FFauOM9Ta2pswIyT3D8AQlfrYdOi/toTPEhqCuAs1v5tcJ3Y08Izh1cJ5Jchwy9SeAXmMg6zrKs2iw=="
  "resolved" "https://registry.npmjs.org/@react-dnd/invariant/-/invariant-4.0.2.tgz"
  "version" "4.0.2"

"@react-dnd/shallowequal@^3.0.1":
  "integrity" "sha512-XjDVbs3ZU16CO1h5Q3Ew2RPJqmZBDE/EVf1LYp6ePEffs3V/MX9ZbL5bJr8qiK5SbGmUMuDoaFgyKacYz8prRA=="
  "resolved" "https://registry.npmjs.org/@react-dnd/shallowequal/-/shallowequal-3.0.1.tgz"
  "version" "3.0.1"

"@remirror/core-constants@^2.0.2":
  "integrity" "sha512-dyHY+sMF0ihPus3O27ODd4+agdHMEmuRdyiZJ2CCWjPV5UFmn17ZbElvk6WOGVE4rdCJKZQCrPV2BcikOMLUGQ=="
  "resolved" "https://registry.npmjs.org/@remirror/core-constants/-/core-constants-2.0.2.tgz"
  "version" "2.0.2"

"@rollup/pluginutils@^5.1.0":
  "integrity" "sha512-XTIWOPPcpvyKI6L1NHo0lFlCyznUEyPmPY1mc3KpPVDYulHSTvyeLNVW00QTLIAFNhR3kYnJTQHeGqU4M3n09g=="
  "resolved" "https://registry.npmjs.org/@rollup/pluginutils/-/pluginutils-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "@types/estree" "^1.0.0"
    "estree-walker" "^2.0.2"
    "picomatch" "^2.3.1"

"@swc/helpers@^0.2.13":
  "integrity" "sha512-wpCQMhf5p5GhNg2MmGKXzUNwxe7zRiCsmqYsamez2beP7mKPCSiu+BjZcdN95yYSzO857kr0VfQewmGpS77nqA=="
  "resolved" "https://registry.npmjs.org/@swc/helpers/-/helpers-0.2.14.tgz"
  "version" "0.2.14"

"@tiptap/core@^2.0.0", "@tiptap/core@^2.2.3":
  "integrity" "sha512-VO5qTsjt6rwworkuo0s5AqYMfDA0ZwiTiH6FHKFSu2G/6sS7HKcc/LjPq+5Legzps4QYdBDl3W28wGsGuS1GdQ=="
  "resolved" "https://registry.npmjs.org/@tiptap/core/-/core-2.6.6.tgz"
  "version" "2.6.6"

"@tiptap/extension-blockquote@^2.2.3":
  "integrity" "sha512-gN23d/ADhTOB0YIM4lR0VrVczdyaXpmIVYYWZ45tQEVJzFWRSIScE9m9NaVqtqwEMpYHyTHxLth0OQutZ91sog=="
  "resolved" "https://registry.npmjs.org/@tiptap/extension-blockquote/-/extension-blockquote-2.2.3.tgz"
  "version" "2.2.3"

"@tiptap/extension-bold@^2.2.3":
  "integrity" "sha512-bHeFkRY5+Nf2DKupstV8EIVn359tw/9MFwDEDoF9F+Sn/vjuS35vm0OqjXYg/Ya9CQvwl/2oym/fKv5kO+Q6og=="
  "resolved" "https://registry.npmjs.org/@tiptap/extension-bold/-/extension-bold-2.2.3.tgz"
  "version" "2.2.3"

"@tiptap/extension-bubble-menu@^2.2.3":
  "integrity" "sha512-6ybColxLznGilzOY/yk3KcpV4JQy+QDDW6Za6zWV6OEs9D8I8VUeMAS77isMMc1dffvHfmgZpVZm/lsva8UuCw=="
  "resolved" "https://registry.npmjs.org/@tiptap/extension-bubble-menu/-/extension-bubble-menu-2.2.3.tgz"
  "version" "2.2.3"
  dependencies:
    "tippy.js" "^6.3.7"

"@tiptap/extension-bullet-list@^2.2.3":
  "integrity" "sha512-BpYg1pIfLE+2LTC90ts53deEWGSmAojhM/jJ84U19qfbfXt/7/KHrZJ4SAMxJSW3pLpy0bIq2XuOuvppOYVR5g=="
  "resolved" "https://registry.npmjs.org/@tiptap/extension-bullet-list/-/extension-bullet-list-2.2.3.tgz"
  "version" "2.2.3"

"@tiptap/extension-code-block@^2.2.3":
  "integrity" "sha512-1xFM2Aj/JEWAT1PWjQ/7hEVmo1Av6JHxTANxMIjXUcmrMJkXDA+BQ7yItlwrrHxY0SJdxBbR/WWFn4dWIxd7iA=="
  "resolved" "https://registry.npmjs.org/@tiptap/extension-code-block/-/extension-code-block-2.2.3.tgz"
  "version" "2.2.3"

"@tiptap/extension-code@^2.2.3":
  "integrity" "sha512-ZMp3CrbAV+PVOnPbGmruvlxFENLc+J/Fos8Y4mWvS1nDbrGuu19OKgKimwdzfDBpZVFVnHpEUnDTMBDzDe0hkg=="
  "resolved" "https://registry.npmjs.org/@tiptap/extension-code/-/extension-code-2.2.3.tgz"
  "version" "2.2.3"

"@tiptap/extension-document@^2.2.3":
  "integrity" "sha512-60Egd9yKb5SzpQlstQAP2A/2a/Qr+A+TblMRKZugrT+NENUhAj6Tx1HxWlblqGu2MsS1iXvQLZ6BQO1jHkL2IQ=="
  "resolved" "https://registry.npmjs.org/@tiptap/extension-document/-/extension-document-2.2.3.tgz"
  "version" "2.2.3"

"@tiptap/extension-dropcursor@^2.2.3":
  "integrity" "sha512-SFvxgVX8/l3H+fV1q6dwmVEwlHuGbaKp1pkQb16/cDiWke/AWOBFTGOIVDfulLI5IiRIL7u3uc+Fy7BXrGDqQw=="
  "resolved" "https://registry.npmjs.org/@tiptap/extension-dropcursor/-/extension-dropcursor-2.2.3.tgz"
  "version" "2.2.3"

"@tiptap/extension-floating-menu@^2.2.3":
  "integrity" "sha512-ZeQGmIFNImhu/zzn//Xzupwa82j2vIwiMoviX2zd+2DutoFnm4qRIAU6qpjzV+ZOSHAq3aBMGnYwEAY6vl4f3g=="
  "resolved" "https://registry.npmjs.org/@tiptap/extension-floating-menu/-/extension-floating-menu-2.2.3.tgz"
  "version" "2.2.3"
  dependencies:
    "tippy.js" "^6.3.7"

"@tiptap/extension-gapcursor@^2.2.3":
  "integrity" "sha512-zPVpxembkuOQL/eJ5oAjvZ9Tyv480OpViKrNtOsQh+0nZctmWKnfDntMoWBZiSeW1vsGjkeFIckdeEAQ1KbIxA=="
  "resolved" "https://registry.npmjs.org/@tiptap/extension-gapcursor/-/extension-gapcursor-2.2.3.tgz"
  "version" "2.2.3"

"@tiptap/extension-hard-break@^2.2.3":
  "integrity" "sha512-P7sP4WBEaQyiiFAswy9lKvaUWUAUwnfTSN3svTAgx0fpU3/ZeVWg+SDi5ve474Ym2oz2eRAr09mNTdWEUsL32Q=="
  "resolved" "https://registry.npmjs.org/@tiptap/extension-hard-break/-/extension-hard-break-2.2.3.tgz"
  "version" "2.2.3"

"@tiptap/extension-heading@^2.2.3":
  "integrity" "sha512-7atctuvtwPqIAdnBPOhAMsJZd41UPnWN3CktzgzfsfEoplq/86QR1hGIE4JXVB2wAZDmbnKP9Fe8PCNr7Q8JCQ=="
  "resolved" "https://registry.npmjs.org/@tiptap/extension-heading/-/extension-heading-2.2.3.tgz"
  "version" "2.2.3"

"@tiptap/extension-history@^2.2.3":
  "integrity" "sha512-S1TUfLtrasyv4zFNlBL302uYaR4wxqR/T36a4d71c0ozr0PsdVc6/f9lfH4aYw4PmS3fzDwJj0PAJ9bb+qDbPw=="
  "resolved" "https://registry.npmjs.org/@tiptap/extension-history/-/extension-history-2.2.3.tgz"
  "version" "2.2.3"

"@tiptap/extension-horizontal-rule@^2.2.3":
  "integrity" "sha512-pc0J0hBcvj9ymJkFau1W/3L+OhB1PQzMjsx4ZWJvxURL8U7zdDqvYvJjfCA0i5Qw2ZuSVXFACGbEVr6NoCMRAw=="
  "resolved" "https://registry.npmjs.org/@tiptap/extension-horizontal-rule/-/extension-horizontal-rule-2.2.3.tgz"
  "version" "2.2.3"

"@tiptap/extension-italic@^2.2.3":
  "integrity" "sha512-SSsFuRnm4Y4Qnc6EuvmA4iarLCt/sg8qkqCKiNPjDUP5JR8HGESeoYVjQzprLHY8jusT9qoC26TP1Sin5vZmWQ=="
  "resolved" "https://registry.npmjs.org/@tiptap/extension-italic/-/extension-italic-2.2.3.tgz"
  "version" "2.2.3"

"@tiptap/extension-list-item@^2.2.3":
  "integrity" "sha512-eyfk4f1jOioj+mkIN2m6XQK61MpV0fi17utt8VNx893Td8kS0g7HHuuYMwyjIRtG35ENUaAt7c216JQwnLsrAw=="
  "resolved" "https://registry.npmjs.org/@tiptap/extension-list-item/-/extension-list-item-2.2.3.tgz"
  "version" "2.2.3"

"@tiptap/extension-ordered-list@^2.2.3":
  "integrity" "sha512-YIWpjkHAJN74tY185ZqatlG4+KbXQOdkJpc5cKWqO89gVWLi7+4xwdeeXbTEG64/LOOWS4Q6r1/EJmDy2FCbyA=="
  "resolved" "https://registry.npmjs.org/@tiptap/extension-ordered-list/-/extension-ordered-list-2.2.3.tgz"
  "version" "2.2.3"

"@tiptap/extension-paragraph@^2.2.3":
  "integrity" "sha512-4dP+Ecb2iEWW33ckFKjXRnSfEygaFUN19qzc7mUYD8e61ZA8caWL6//uL7DFIz4Q1rchyefbU52gCwTh2P42kQ=="
  "resolved" "https://registry.npmjs.org/@tiptap/extension-paragraph/-/extension-paragraph-2.2.3.tgz"
  "version" "2.2.3"

"@tiptap/extension-strike@^2.2.3":
  "integrity" "sha512-3wwFk01ociZajRzD08hp4j/4isFUeD6BIkKPDnZeGD5HKPdTOaDciE3dJ3JaZZrRZPPdPV3yMt5hkBOapqEKzQ=="
  "resolved" "https://registry.npmjs.org/@tiptap/extension-strike/-/extension-strike-2.2.3.tgz"
  "version" "2.2.3"

"@tiptap/extension-text@^2.2.3":
  "integrity" "sha512-BrWGCkmuzVcsNy7dSCfJyVwedPzeNz6BR/OUNzM8Mqt2KSxfoIRy7cg16HvFB4YW+ijrM9XUqDIFvqYI0TY+Jg=="
  "resolved" "https://registry.npmjs.org/@tiptap/extension-text/-/extension-text-2.2.3.tgz"
  "version" "2.2.3"

"@tiptap/pm@^2.0.0", "@tiptap/pm@^2.2.3", "@tiptap/pm@^2.6.6":
  "integrity" "sha512-56FGLPn3fwwUlIbLs+BO21bYfyqP9fKyZQbQyY0zWwA/AG2kOwoXaRn7FOVbjP6CylyWpFJnpRRmgn694QKHEg=="
  "resolved" "https://registry.npmjs.org/@tiptap/pm/-/pm-2.6.6.tgz"
  "version" "2.6.6"
  dependencies:
    "prosemirror-changeset" "^2.2.1"
    "prosemirror-collab" "^1.3.1"
    "prosemirror-commands" "^1.5.2"
    "prosemirror-dropcursor" "^1.8.1"
    "prosemirror-gapcursor" "^1.3.2"
    "prosemirror-history" "^1.4.1"
    "prosemirror-inputrules" "^1.4.0"
    "prosemirror-keymap" "^1.2.2"
    "prosemirror-markdown" "^1.13.0"
    "prosemirror-menu" "^1.2.4"
    "prosemirror-model" "^1.22.2"
    "prosemirror-schema-basic" "^1.2.3"
    "prosemirror-schema-list" "^1.4.1"
    "prosemirror-state" "^1.4.3"
    "prosemirror-tables" "^1.4.0"
    "prosemirror-trailing-node" "^2.0.9"
    "prosemirror-transform" "^1.9.0"
    "prosemirror-view" "^1.33.9"

"@tiptap/starter-kit@^2.1.11":
  "integrity" "sha512-Jx+QXz0SE1y+j498TqYEJcjbIV9YSMwcuIJQ04q8KqHuSmZrq9B22Qa4d0fpcM7uL7dLI4AcRrcqaOuIahCJYQ=="
  "resolved" "https://registry.npmjs.org/@tiptap/starter-kit/-/starter-kit-2.2.3.tgz"
  "version" "2.2.3"
  dependencies:
    "@tiptap/core" "^2.2.3"
    "@tiptap/extension-blockquote" "^2.2.3"
    "@tiptap/extension-bold" "^2.2.3"
    "@tiptap/extension-bullet-list" "^2.2.3"
    "@tiptap/extension-code" "^2.2.3"
    "@tiptap/extension-code-block" "^2.2.3"
    "@tiptap/extension-document" "^2.2.3"
    "@tiptap/extension-dropcursor" "^2.2.3"
    "@tiptap/extension-gapcursor" "^2.2.3"
    "@tiptap/extension-hard-break" "^2.2.3"
    "@tiptap/extension-heading" "^2.2.3"
    "@tiptap/extension-history" "^2.2.3"
    "@tiptap/extension-horizontal-rule" "^2.2.3"
    "@tiptap/extension-italic" "^2.2.3"
    "@tiptap/extension-list-item" "^2.2.3"
    "@tiptap/extension-ordered-list" "^2.2.3"
    "@tiptap/extension-paragraph" "^2.2.3"
    "@tiptap/extension-strike" "^2.2.3"
    "@tiptap/extension-text" "^2.2.3"

"@tiptap/vue-3@^2.1.11":
  "integrity" "sha512-TC+pncpxP6GHgM+qvX/1mfT+Xl3OlEekZqjetNJC/MfFM55OL5dIEu5kVjOiRsGQ/TWgwBGPrRYEE0Y1D6FuBA=="
  "resolved" "https://registry.npmjs.org/@tiptap/vue-3/-/vue-3-2.2.3.tgz"
  "version" "2.2.3"
  dependencies:
    "@tiptap/extension-bubble-menu" "^2.2.3"
    "@tiptap/extension-floating-menu" "^2.2.3"

"@types/estree@^1.0.0":
  "integrity" "sha512-/kYRxGDLWzHOB7q+wtSUQlFrtcdUccpfy+X+9iMBpHK8QLLhx2wIPYuS5DYtR9Wa/YlZAbIovy7qVdB1Aq6Lyw=="
  "resolved" "https://registry.npmjs.org/@types/estree/-/estree-1.0.5.tgz"
  "version" "1.0.5"

"@types/lodash-es@*", "@types/lodash-es@^4.17.6":
  "integrity" "sha512-0NgftHUcV4v34VhXm8QBSftKVXtbkBG3ViCjs6+eJ5a6y6Mi/jiFGPc1sC7QK+9BFhWrURE3EOggmWaSxL9OzQ=="
  "resolved" "https://registry.npmjs.org/@types/lodash-es/-/lodash-es-4.17.12.tgz"
  "version" "4.17.12"
  dependencies:
    "@types/lodash" "*"

"@types/lodash@*", "@types/lodash@^4.14.182":
  "integrity" "sha512-OvlIYQK9tNneDlS0VN54LLd5uiPCBOp7gS5Z0f1mjoJYBrtStzgmJBxONW3U6OZqdtNzZPmn9BS/7WI7BFFcFQ=="
  "resolved" "https://registry.npmjs.org/@types/lodash/-/lodash-4.14.202.tgz"
  "version" "4.14.202"

"@types/web-bluetooth@^0.0.16":
  "integrity" "sha512-oh8q2Zc32S6gd/j50GowEjKLoOVOwHP/bWVjKJInBwQqdOYMdPrf1oVlelTlyfFK3CKxL1uahMDAr+vy8T7yMQ=="
  "resolved" "https://registry.npmjs.org/@types/web-bluetooth/-/web-bluetooth-0.0.16.tgz"
  "version" "0.0.16"

"@types/web-bluetooth@^0.0.20":
  "integrity" "sha512-g9gZnnXVq7gM7v3tJCWV/qw7w+KeOlSHAhgF9RytFyifW6AF61hdT2ucrYhPq9hLs5JIryeupHV3qGk95dH9ow=="
  "resolved" "https://registry.npmjs.org/@types/web-bluetooth/-/web-bluetooth-0.0.20.tgz"
  "version" "0.0.20"

"@ungap/structured-clone@^1.2.0":
  "integrity" "sha512-zuVdFrMJiuCDQUMCzQaD6KL28MjnqqN8XnAqiEq9PNm/hCPTSGfrXCOfwj1ow4LFb/tNymJPwsNbVePc1xFqrQ=="
  "resolved" "https://registry.npmjs.org/@ungap/structured-clone/-/structured-clone-1.2.0.tgz"
  "version" "1.2.0"

"@vitejs/plugin-vue@^4.4.0":
  "integrity" "sha512-kqf7SGFoG+80aZG6Pf+gsZIVvGSCKE98JbiWqcCV9cThtg91Jav0yvYFC9Zb+jKetNGF6ZKeoaxgZfND21fWKw=="
  "resolved" "https://registry.npmjs.org/@vitejs/plugin-vue/-/plugin-vue-4.6.2.tgz"
  "version" "4.6.2"

"@vue/compiler-core@3.5.11":
  "integrity" "sha512-PwAdxs7/9Hc3ieBO12tXzmTD+Ln4qhT/56S+8DvrrZ4kLDn4Z/AMUr8tXJD0axiJBS0RKIoNaR0yMuQB9v9Udg=="
  "resolved" "https://registry.npmjs.org/@vue/compiler-core/-/compiler-core-3.5.11.tgz"
  "version" "3.5.11"
  dependencies:
    "@babel/parser" "^7.25.3"
    "@vue/shared" "3.5.11"
    "entities" "^4.5.0"
    "estree-walker" "^2.0.2"
    "source-map-js" "^1.2.0"

"@vue/compiler-dom@3.5.11":
  "integrity" "sha512-pyGf8zdbDDRkBrEzf8p7BQlMKNNF5Fk/Cf/fQ6PiUz9at4OaUfyXW0dGJTo2Vl1f5U9jSLCNf0EZJEogLXoeew=="
  "resolved" "https://registry.npmjs.org/@vue/compiler-dom/-/compiler-dom-3.5.11.tgz"
  "version" "3.5.11"
  dependencies:
    "@vue/compiler-core" "3.5.11"
    "@vue/shared" "3.5.11"

"@vue/compiler-sfc@^3.2.47", "@vue/compiler-sfc@3.5.11":
  "integrity" "sha512-gsbBtT4N9ANXXepprle+X9YLg2htQk1sqH/qGJ/EApl+dgpUBdTv3yP7YlR535uHZY3n6XaR0/bKo0BgwwDniw=="
  "resolved" "https://registry.npmjs.org/@vue/compiler-sfc/-/compiler-sfc-3.5.11.tgz"
  "version" "3.5.11"
  dependencies:
    "@babel/parser" "^7.25.3"
    "@vue/compiler-core" "3.5.11"
    "@vue/compiler-dom" "3.5.11"
    "@vue/compiler-ssr" "3.5.11"
    "@vue/shared" "3.5.11"
    "estree-walker" "^2.0.2"
    "magic-string" "^0.30.11"
    "postcss" "^8.4.47"
    "source-map-js" "^1.2.0"

"@vue/compiler-ssr@3.5.11":
  "integrity" "sha512-P4+GPjOuC2aFTk1Z4WANvEhyOykcvEd5bIj2KVNGKGfM745LaXGr++5njpdBTzVz5pZifdlR1kpYSJJpIlSePA=="
  "resolved" "https://registry.npmjs.org/@vue/compiler-ssr/-/compiler-ssr-3.5.11.tgz"
  "version" "3.5.11"
  dependencies:
    "@vue/compiler-dom" "3.5.11"
    "@vue/shared" "3.5.11"

"@vue/devtools-api@^6.5.0", "@vue/devtools-api@^6.6.1":
  "integrity" "sha512-LgPscpE3Vs0x96PzSSB4IGVSZXZBZHpfxs+ZA1d+VEPwHdOXowy/Y2CsvCAIFrf+ssVU1pD1jidj505EpUnfbA=="
  "resolved" "https://registry.npmjs.org/@vue/devtools-api/-/devtools-api-6.6.1.tgz"
  "version" "6.6.1"

"@vue/reactivity@3.5.11":
  "integrity" "sha512-Nqo5VZEn8MJWlCce8XoyVqHZbd5P2NH+yuAaFzuNSR96I+y1cnuUiq7xfSG+kyvLSiWmaHTKP1r3OZY4mMD50w=="
  "resolved" "https://registry.npmjs.org/@vue/reactivity/-/reactivity-3.5.11.tgz"
  "version" "3.5.11"
  dependencies:
    "@vue/shared" "3.5.11"

"@vue/runtime-core@3.5.11":
  "integrity" "sha512-7PsxFGqwfDhfhh0OcDWBG1DaIQIVOLgkwA5q6MtkPiDFjp5gohVnJEahSktwSFLq7R5PtxDKy6WKURVN1UDbzA=="
  "resolved" "https://registry.npmjs.org/@vue/runtime-core/-/runtime-core-3.5.11.tgz"
  "version" "3.5.11"
  dependencies:
    "@vue/reactivity" "3.5.11"
    "@vue/shared" "3.5.11"

"@vue/runtime-dom@3.5.11":
  "integrity" "sha512-GNghjecT6IrGf0UhuYmpgaOlN7kxzQBhxWEn08c/SQDxv1yy4IXI1bn81JgEpQ4IXjRxWtPyI8x0/7TF5rPfYQ=="
  "resolved" "https://registry.npmjs.org/@vue/runtime-dom/-/runtime-dom-3.5.11.tgz"
  "version" "3.5.11"
  dependencies:
    "@vue/reactivity" "3.5.11"
    "@vue/runtime-core" "3.5.11"
    "@vue/shared" "3.5.11"
    "csstype" "^3.1.3"

"@vue/server-renderer@3.5.11":
  "integrity" "sha512-cVOwYBxR7Wb1B1FoxYvtjJD8X/9E5nlH4VSkJy2uMA1MzYNdzAAB//l8nrmN9py/4aP+3NjWukf9PZ3TeWULaA=="
  "resolved" "https://registry.npmjs.org/@vue/server-renderer/-/server-renderer-3.5.11.tgz"
  "version" "3.5.11"
  dependencies:
    "@vue/compiler-ssr" "3.5.11"
    "@vue/shared" "3.5.11"

"@vue/shared@3.5.11":
  "integrity" "sha512-W8GgysJVnFo81FthhzurdRAWP/byq3q2qIw70e0JWblzVhjgOMiC2GyovXrZTFQJnFVryYaKGP3Tc9vYzYm6PQ=="
  "resolved" "https://registry.npmjs.org/@vue/shared/-/shared-3.5.11.tgz"
  "version" "3.5.11"

"@vueform/slider@^2.1.7":
  "integrity" "sha512-L2G3Ju51Yq6yWF2wzYYsicUUaH56kL1QKGVtimUVHT1K1ADcRT94xVyIeJpS0klliVEeF6iMZFbdXtHq8AsDHw=="
  "resolved" "https://registry.npmjs.org/@vueform/slider/-/slider-2.1.10.tgz"
  "version" "2.1.10"

"@vuelidate/core@^2.0.3":
  "integrity" "sha512-AN6l7KF7+mEfyWG0doT96z+47ljwPpZfi9/JrNMkOGLFv27XVZvKzRLXlmDPQjPl/wOB1GNnHuc54jlCLRNqGA=="
  "resolved" "https://registry.npmjs.org/@vuelidate/core/-/core-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "vue-demi" "^0.13.11"

"@vuelidate/validators@^2.0.4":
  "integrity" "sha512-odTxtUZ2JpwwiQ10t0QWYJkkYrfd0SyFYhdHH44QQ1jDatlZgTh/KRzrWVmn/ib9Gq7H4hFD4e8ahoo5YlUlDw=="
  "resolved" "https://registry.npmjs.org/@vuelidate/validators/-/validators-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "vue-demi" "^0.13.11"

"@vueuse/core@^11.1.0":
  "integrity" "sha512-7OC4Rl1f9G8IT6rUfi9JrKiXy4bfmHhZ5x2Ceojy0jnd3mHNEvV4JaRygH362ror6/NZ+Nl+n13LPzGiPN8cKA=="
  "resolved" "https://registry.npmjs.org/@vueuse/core/-/core-11.3.0.tgz"
  "version" "11.3.0"
  dependencies:
    "@types/web-bluetooth" "^0.0.20"
    "@vueuse/metadata" "11.3.0"
    "@vueuse/shared" "11.3.0"
    "vue-demi" ">=0.14.10"

"@vueuse/core@^9.1.0":
  "integrity" "sha512-pujnclbeHWxxPRqXWmdkKV5OX4Wk4YeK7wusHqRwU0Q7EFusHoqNA/aPhB6KCh9hEqJkLAJo7bb0Lh9b+OIVzw=="
  "resolved" "https://registry.npmjs.org/@vueuse/core/-/core-9.13.0.tgz"
  "version" "9.13.0"
  dependencies:
    "@types/web-bluetooth" "^0.0.16"
    "@vueuse/metadata" "9.13.0"
    "@vueuse/shared" "9.13.0"
    "vue-demi" "*"

"@vueuse/metadata@11.3.0":
  "integrity" "sha512-pwDnDspTqtTo2HwfLw4Rp6yywuuBdYnPYDq+mO38ZYKGebCUQC/nVj/PXSiK9HX5otxLz8Fn7ECPbjiRz2CC3g=="
  "resolved" "https://registry.npmjs.org/@vueuse/metadata/-/metadata-11.3.0.tgz"
  "version" "11.3.0"

"@vueuse/metadata@9.13.0":
  "integrity" "sha512-gdU7TKNAUVlXXLbaF+ZCfte8BjRJQWPCa2J55+7/h+yDtzw3vOoGQDRXzI6pyKyo6bXFT5/QoPE4hAknExjRLQ=="
  "resolved" "https://registry.npmjs.org/@vueuse/metadata/-/metadata-9.13.0.tgz"
  "version" "9.13.0"

"@vueuse/shared@11.3.0":
  "integrity" "sha512-P8gSSWQeucH5821ek2mn/ciCk+MS/zoRKqdQIM3bHq6p7GXDAJLmnRRKmF5F65sAVJIfzQlwR3aDzwCn10s8hA=="
  "resolved" "https://registry.npmjs.org/@vueuse/shared/-/shared-11.3.0.tgz"
  "version" "11.3.0"
  dependencies:
    "vue-demi" ">=0.14.10"

"@vueuse/shared@9.13.0":
  "integrity" "sha512-UrnhU+Cnufu4S6JLCPZnkWh0WwZGUp72ktOF2DFptMlOs3TOdVv8xJN53zhHGARmVOsz5KqOls09+J1NR6sBKw=="
  "resolved" "https://registry.npmjs.org/@vueuse/shared/-/shared-9.13.0.tgz"
  "version" "9.13.0"
  dependencies:
    "vue-demi" "*"

"acorn-jsx@^5.3.2":
  "integrity" "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ=="
  "resolved" "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  "version" "5.3.2"

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", "acorn@^8.11.3", "acorn@^8.5.0", "acorn@^8.8.2", "acorn@^8.9.0":
  "integrity" "sha512-Y9rRfJG5jcKOE0CLisYbojUjIrIEE7AGMzA/Sm4BslANhbS+cDMpgBdcPT91oJ7OuJ9hYJBx59RjbhxVnrF8Xg=="
  "resolved" "https://registry.npmjs.org/acorn/-/acorn-8.11.3.tgz"
  "version" "8.11.3"

"ajv@^6.12.4":
  "integrity" "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz"
  "version" "6.12.6"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"ansi-regex@^5.0.1":
  "integrity" "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="
  "resolved" "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
  "version" "5.0.1"

"ansi-styles@^4.1.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"anymatch@~3.1.2":
  "integrity" "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw=="
  "resolved" "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "normalize-path" "^3.0.0"
    "picomatch" "^2.0.4"

"argparse@^2.0.1":
  "integrity" "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q=="
  "resolved" "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz"
  "version" "2.0.1"

"async-validator@^4.2.5":
  "integrity" "sha512-7HhHjtERjqlNbZtqNqy2rckN/SpOOlmDliet+lP7k+eKZEjPk3DgyeU9lIXLdeLz0uBbbVp+9Qdow9wJWgwwfg=="
  "resolved" "https://registry.npmjs.org/async-validator/-/async-validator-4.2.5.tgz"
  "version" "4.2.5"

"asynckit@^0.4.0":
  "integrity" "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="
  "resolved" "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  "version" "0.4.0"

"axios@^1.6.7":
  "integrity" "sha512-/hDJGff6/c7u0hDkvkGxR/oy6CbCs8ziCsC7SqmhjfozqiJGc8Z11wrv9z9lYfY4K8l+H9TpjcMDX0xOZmx+RA=="
  "resolved" "https://registry.npmjs.org/axios/-/axios-1.6.7.tgz"
  "version" "1.6.7"
  dependencies:
    "follow-redirects" "^1.15.4"
    "form-data" "^4.0.0"
    "proxy-from-env" "^1.1.0"

"balanced-match@^1.0.0":
  "integrity" "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw=="
  "resolved" "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
  "version" "1.0.2"

"binary-extensions@^2.0.0":
  "integrity" "sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA=="
  "resolved" "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.2.0.tgz"
  "version" "2.2.0"

"blurhash@2.0.5":
  "integrity" "sha512-cRygWd7kGBQO3VEhPiTgq4Wc43ctsM+o46urrmPOiuAe+07fzlSB9OJVdpgDL0jPqXUVQ9ht7aq7kxOeJHRK+w=="
  "resolved" "https://registry.npmjs.org/blurhash/-/blurhash-2.0.5.tgz"
  "version" "2.0.5"

"boolbase@^1.0.0":
  "integrity" "sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww=="
  "resolved" "https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz"
  "version" "1.0.0"

"bootstrap@5.3.2":
  "integrity" "sha512-D32nmNWiQHo94BKHLmOrdjlL05q1c8oxbtBphQFb9Z5to6eGRDCm0QgeaZ4zFBHzfg2++rqa2JkqCcxDy0sH0g=="
  "resolved" "https://registry.npmjs.org/bootstrap/-/bootstrap-5.3.2.tgz"
  "version" "5.3.2"

"brace-expansion@^1.1.7":
  "integrity" "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA=="
  "resolved" "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"braces@^3.0.2", "braces@~3.0.2":
  "integrity" "sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A=="
  "resolved" "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "fill-range" "^7.0.1"

"callsites@^3.0.0":
  "integrity" "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ=="
  "resolved" "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz"
  "version" "3.1.0"

"can-use-dom@^0.1.0":
  "integrity" "sha512-ceOhN1DL7Y4O6M0j9ICgmTYziV89WMd96SvSl0REd8PMgrY0B/WBOPoed5S1KUmJqXgUXh8gzSe6E3ae27upsQ=="
  "resolved" "https://registry.npmjs.org/can-use-dom/-/can-use-dom-0.1.0.tgz"
  "version" "0.1.0"

"chalk@^4.0.0":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chart.js@^4.1.1", "chart.js@^4.4.0":
  "integrity" "sha512-C74QN1bxwV1v2PEujhmKjOZ7iUM4w6BWs23Md/6aOZZSlwMzeCIDGuZay++rBgChYru7/+QFeoQW0fQoP534Dg=="
  "resolved" "https://registry.npmjs.org/chart.js/-/chart.js-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "@kurkle/color" "^0.3.0"

"chokidar@^3.6.0", "chokidar@>=3.0.0 <4.0.0":
  "integrity" "sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw=="
  "resolved" "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "anymatch" "~3.1.2"
    "braces" "~3.0.2"
    "glob-parent" "~5.1.2"
    "is-binary-path" "~2.1.0"
    "is-glob" "~4.0.1"
    "normalize-path" "~3.0.0"
    "readdirp" "~3.6.0"
  optionalDependencies:
    "fsevents" "~2.3.2"

"ckeditor5@40.2.0":
  "integrity" "sha512-JaFuY/6DX1wbA6yRB2xQVMr+9W1C3HvSX4AT10ccoKBKe9OctIatekDt2ztV+cMaVHLF1wocskS/Ql9XFRy2Eg=="
  "resolved" "https://registry.npmjs.org/ckeditor5/-/ckeditor5-40.2.0.tgz"
  "version" "40.2.0"
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "40.2.0"
    "@ckeditor/ckeditor5-core" "40.2.0"
    "@ckeditor/ckeditor5-engine" "40.2.0"
    "@ckeditor/ckeditor5-enter" "40.2.0"
    "@ckeditor/ckeditor5-paragraph" "40.2.0"
    "@ckeditor/ckeditor5-select-all" "40.2.0"
    "@ckeditor/ckeditor5-typing" "40.2.0"
    "@ckeditor/ckeditor5-ui" "40.2.0"
    "@ckeditor/ckeditor5-undo" "40.2.0"
    "@ckeditor/ckeditor5-upload" "40.2.0"
    "@ckeditor/ckeditor5-utils" "40.2.0"
    "@ckeditor/ckeditor5-watchdog" "40.2.0"
    "@ckeditor/ckeditor5-widget" "40.2.0"

"color-convert@^2.0.1", "color-convert@2.0.1":
  "integrity" "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ=="
  "resolved" "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "color-name" "~1.1.4"

"color-name@^1.0.0", "color-name@~1.1.4":
  "integrity" "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="
  "resolved" "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  "version" "1.1.4"

"color-parse@1.4.2":
  "integrity" "sha512-RI7s49/8yqDj3fECFZjUI1Yi0z/Gq1py43oNJivAIIDSyJiOZLfYCRQEgn8HEVAj++PcRe8AnL2XF0fRJ3BTnA=="
  "resolved" "https://registry.npmjs.org/color-parse/-/color-parse-1.4.2.tgz"
  "version" "1.4.2"
  dependencies:
    "color-name" "^1.0.0"

"combined-stream@^1.0.8":
  "integrity" "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg=="
  "resolved" "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "delayed-stream" "~1.0.0"

"concat-map@0.0.1":
  "integrity" "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg=="
  "resolved" "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"confbox@^0.1.7":
  "integrity" "sha512-uJcB/FKZtBMCJpK8MQji6bJHgu1tixKPxRLeGkNzBoOZzpnZUJm0jm2/sBDWcuBx1dYgxV4JU+g5hmNxCyAmdA=="
  "resolved" "https://registry.npmjs.org/confbox/-/confbox-0.1.7.tgz"
  "version" "0.1.7"

"crelt@^1.0.0":
  "integrity" "sha512-VQ2MBenTq1fWZUH9DJNGti7kKv6EeAuYr3cLwxUWhIu1baTaXh4Ib5W2CqHVqib4/MqbYGJqiL3Zb8GJZr3l4g=="
  "resolved" "https://registry.npmjs.org/crelt/-/crelt-1.0.6.tgz"
  "version" "1.0.6"

"cropperjs@^1.5.6":
  "integrity" "sha512-F4wsi+XkDHCOMrHMYjrTEE4QBOrsHHN5/2VsVAaRq8P7E5z7xQpT75S+f/9WikmBEailas3+yo+6zPIomW+NOA=="
  "resolved" "https://registry.npmjs.org/cropperjs/-/cropperjs-1.6.1.tgz"
  "version" "1.6.1"

"cross-spawn@^7.0.2":
  "integrity" "sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w=="
  "resolved" "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz"
  "version" "7.0.3"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"cssesc@^3.0.0":
  "integrity" "sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg=="
  "resolved" "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz"
  "version" "3.0.0"

"csstype@^3.1.3":
  "integrity" "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw=="
  "resolved" "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz"
  "version" "3.1.3"

"dayjs@^1.11.10", "dayjs@^1.11.3":
  "integrity" "sha512-vjAczensTgRcqDERK0SR2XMwsF/tSvnvlv6VcF2GIhg6Sx4yOIt/irsr1RDJsKiIyBzJDpCoXiWWq28MqH2cnQ=="
  "resolved" "https://registry.npmjs.org/dayjs/-/dayjs-1.11.10.tgz"
  "version" "1.11.10"

"debug@^4.3.1", "debug@^4.3.2", "debug@^4.3.3", "debug@^4.3.4":
  "integrity" "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "ms" "2.1.2"

"deep-is@^0.1.3":
  "integrity" "sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ=="
  "resolved" "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz"
  "version" "0.1.4"

"delayed-stream@~1.0.0":
  "integrity" "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ=="
  "resolved" "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  "version" "1.0.0"

"dnd-core@^15.1.2":
  "integrity" "sha512-EOec1LyJUuGRFg0LDa55rSRAUe97uNVKVkUo8iyvzQlcECYTuPblVQfRWXWj1OyPseFIeebWpNmKFy0h6BcF1A=="
  "resolved" "https://registry.npmjs.org/dnd-core/-/dnd-core-15.1.2.tgz"
  "version" "15.1.2"
  dependencies:
    "@react-dnd/asap" "4.0.1"
    "@react-dnd/invariant" "3.0.1"
    "redux" "^4.1.2"

"dnd-core@^16.0.1":
  "integrity" "sha512-HK294sl7tbw6F6IeuK16YSBUoorvHpY8RHO+9yFfaJyCDVb6n7PRcezrOEOa2SBCqiYpemh5Jx20ZcjKdFAVng=="
  "resolved" "https://registry.npmjs.org/dnd-core/-/dnd-core-16.0.1.tgz"
  "version" "16.0.1"
  dependencies:
    "@react-dnd/asap" "^5.0.1"
    "@react-dnd/invariant" "^4.0.1"
    "redux" "^4.2.0"

"doctrine@^3.0.0":
  "integrity" "sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w=="
  "resolved" "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "esutils" "^2.0.2"

"dropzone@^6.0.0-beta.2":
  "integrity" "sha512-k44yLuFFhRk53M8zP71FaaNzJYIzr99SKmpbO/oZKNslDjNXQsBTdfLs+iONd0U0L94zzlFzRnFdqbLcs7h9fQ=="
  "resolved" "https://registry.npmjs.org/dropzone/-/dropzone-6.0.0-beta.2.tgz"
  "version" "6.0.0-beta.2"
  dependencies:
    "@swc/helpers" "^0.2.13"
    "just-extend" "^5.0.0"

"element-plus@^2.8.3":
  "integrity" "sha512-BXQOyDf0s7JHyNEV8iaO+iaOzTZPsBXVKMzMI967vLCodUBDLrtiY5vglAn1YEebQcUOEUMhGcttTpIvEkcBjQ=="
  "resolved" "https://registry.npmjs.org/element-plus/-/element-plus-2.8.3.tgz"
  "version" "2.8.3"
  dependencies:
    "@ctrl/tinycolor" "^3.4.1"
    "@element-plus/icons-vue" "^2.3.1"
    "@floating-ui/dom" "^1.0.1"
    "@popperjs/core" "npm:@sxzz/popperjs-es@^2.11.7"
    "@types/lodash" "^4.14.182"
    "@types/lodash-es" "^4.17.6"
    "@vueuse/core" "^9.1.0"
    "async-validator" "^4.2.5"
    "dayjs" "^1.11.3"
    "escape-html" "^1.0.3"
    "lodash" "^4.17.21"
    "lodash-es" "^4.17.21"
    "lodash-unified" "^1.0.2"
    "memoize-one" "^6.0.0"
    "normalize-wheel-es" "^1.2.0"

"entities@^4.4.0", "entities@^4.5.0":
  "integrity" "sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw=="
  "resolved" "https://registry.npmjs.org/entities/-/entities-4.5.0.tgz"
  "version" "4.5.0"

"esbuild@^0.18.10":
  "integrity" "sha512-ceqxoedUrcayh7Y7ZX6NdbbDzGROiyVBgC4PriJThBKSVPWnnFHZAkfI1lJT8QFkOwH4qOS2SJkS4wvpGl8BpA=="
  "resolved" "https://registry.npmjs.org/esbuild/-/esbuild-0.18.20.tgz"
  "version" "0.18.20"
  optionalDependencies:
    "@esbuild/android-arm" "0.18.20"
    "@esbuild/android-arm64" "0.18.20"
    "@esbuild/android-x64" "0.18.20"
    "@esbuild/darwin-arm64" "0.18.20"
    "@esbuild/darwin-x64" "0.18.20"
    "@esbuild/freebsd-arm64" "0.18.20"
    "@esbuild/freebsd-x64" "0.18.20"
    "@esbuild/linux-arm" "0.18.20"
    "@esbuild/linux-arm64" "0.18.20"
    "@esbuild/linux-ia32" "0.18.20"
    "@esbuild/linux-loong64" "0.18.20"
    "@esbuild/linux-mips64el" "0.18.20"
    "@esbuild/linux-ppc64" "0.18.20"
    "@esbuild/linux-riscv64" "0.18.20"
    "@esbuild/linux-s390x" "0.18.20"
    "@esbuild/linux-x64" "0.18.20"
    "@esbuild/netbsd-x64" "0.18.20"
    "@esbuild/openbsd-x64" "0.18.20"
    "@esbuild/sunos-x64" "0.18.20"
    "@esbuild/win32-arm64" "0.18.20"
    "@esbuild/win32-ia32" "0.18.20"
    "@esbuild/win32-x64" "0.18.20"

"escape-html@^1.0.3":
  "integrity" "sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow=="
  "resolved" "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz"
  "version" "1.0.3"

"escape-string-regexp@^4.0.0":
  "integrity" "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA=="
  "resolved" "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  "version" "4.0.0"

"escodegen@^2.1.0":
  "integrity" "sha512-2NlIDTwUWJN0mRPQOdtQBzbUHvdGY2P1VXSyU83Q3xKxM7WHX2Ql8dKq782Q9TgQUNOLEzEYu9bzLNj1q88I5w=="
  "resolved" "https://registry.npmjs.org/escodegen/-/escodegen-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "esprima" "^4.0.1"
    "estraverse" "^5.2.0"
    "esutils" "^2.0.2"
  optionalDependencies:
    "source-map" "~0.6.1"

"eslint-plugin-vue@^9.17.0":
  "integrity" "sha512-XVtI7z39yOVBFJyi8Ljbn7kY9yHzznKXL02qQYn+ta63Iy4A9JFBw6o4OSB9hyD2++tVT+su9kQqetUyCCwhjw=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-vue/-/eslint-plugin-vue-9.21.1.tgz"
  "version" "9.21.1"
  dependencies:
    "@eslint-community/eslint-utils" "^4.4.0"
    "natural-compare" "^1.4.0"
    "nth-check" "^2.1.1"
    "postcss-selector-parser" "^6.0.13"
    "semver" "^7.5.4"
    "vue-eslint-parser" "^9.4.2"
    "xml-name-validator" "^4.0.0"

"eslint-scope@^7.1.1", "eslint-scope@^7.2.2":
  "integrity" "sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg=="
  "resolved" "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.2.2.tgz"
  "version" "7.2.2"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^5.2.0"

"eslint-visitor-keys@^3.0.0", "eslint-visitor-keys@^3.3.0", "eslint-visitor-keys@^3.4.1", "eslint-visitor-keys@^3.4.3":
  "integrity" "sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag=="
  "resolved" "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz"
  "version" "3.4.3"

"eslint@^6.0.0 || ^7.0.0 || >=8.0.0", "eslint@^6.2.0 || ^7.0.0 || ^8.0.0", "eslint@^8.51.0", "eslint@>=6.0.0":
  "integrity" "sha512-Go19xM6T9puCOWntie1/P997aXxFsOi37JIHRWI514Hc6ZnaHGKY9xFhrU65RT6CcBEzZoGG1e6Nq+DT04ZtZQ=="
  "resolved" "https://registry.npmjs.org/eslint/-/eslint-8.56.0.tgz"
  "version" "8.56.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.6.1"
    "@eslint/eslintrc" "^2.1.4"
    "@eslint/js" "8.56.0"
    "@humanwhocodes/config-array" "^0.11.13"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@nodelib/fs.walk" "^1.2.8"
    "@ungap/structured-clone" "^1.2.0"
    "ajv" "^6.12.4"
    "chalk" "^4.0.0"
    "cross-spawn" "^7.0.2"
    "debug" "^4.3.2"
    "doctrine" "^3.0.0"
    "escape-string-regexp" "^4.0.0"
    "eslint-scope" "^7.2.2"
    "eslint-visitor-keys" "^3.4.3"
    "espree" "^9.6.1"
    "esquery" "^1.4.2"
    "esutils" "^2.0.2"
    "fast-deep-equal" "^3.1.3"
    "file-entry-cache" "^6.0.1"
    "find-up" "^5.0.0"
    "glob-parent" "^6.0.2"
    "globals" "^13.19.0"
    "graphemer" "^1.4.0"
    "ignore" "^5.2.0"
    "imurmurhash" "^0.1.4"
    "is-glob" "^4.0.0"
    "is-path-inside" "^3.0.3"
    "js-yaml" "^4.1.0"
    "json-stable-stringify-without-jsonify" "^1.0.1"
    "levn" "^0.4.1"
    "lodash.merge" "^4.6.2"
    "minimatch" "^3.1.2"
    "natural-compare" "^1.4.0"
    "optionator" "^0.9.3"
    "strip-ansi" "^6.0.1"
    "text-table" "^0.2.0"

"espree@^9.0.0", "espree@^9.3.1", "espree@^9.6.0", "espree@^9.6.1":
  "integrity" "sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ=="
  "resolved" "https://registry.npmjs.org/espree/-/espree-9.6.1.tgz"
  "version" "9.6.1"
  dependencies:
    "acorn" "^8.9.0"
    "acorn-jsx" "^5.3.2"
    "eslint-visitor-keys" "^3.4.1"

"esprima@^4.0.1":
  "integrity" "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A=="
  "resolved" "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz"
  "version" "4.0.1"

"esquery@^1.4.0", "esquery@^1.4.2":
  "integrity" "sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg=="
  "resolved" "https://registry.npmjs.org/esquery/-/esquery-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "estraverse" "^5.1.0"

"esrecurse@^4.3.0":
  "integrity" "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag=="
  "resolved" "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "estraverse" "^5.2.0"

"estraverse@^5.1.0", "estraverse@^5.2.0":
  "integrity" "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA=="
  "resolved" "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz"
  "version" "5.3.0"

"estree-walker@^2.0.2":
  "integrity" "sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w=="
  "resolved" "https://registry.npmjs.org/estree-walker/-/estree-walker-2.0.2.tgz"
  "version" "2.0.2"

"esutils@^2.0.2":
  "integrity" "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g=="
  "resolved" "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz"
  "version" "2.0.3"

"fast-deep-equal@^3.1.1", "fast-deep-equal@^3.1.3":
  "integrity" "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="
  "resolved" "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  "version" "3.1.3"

"fast-glob@^3.2.12":
  "integrity" "sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow=="
  "resolved" "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.2.tgz"
  "version" "3.3.2"
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    "glob-parent" "^5.1.2"
    "merge2" "^1.3.0"
    "micromatch" "^4.0.4"

"fast-json-stable-stringify@^2.0.0":
  "integrity" "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="
  "resolved" "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  "version" "2.1.0"

"fast-levenshtein@^2.0.6":
  "integrity" "sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw=="
  "resolved" "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  "version" "2.0.6"

"fastq@^1.6.0":
  "integrity" "sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w=="
  "resolved" "https://registry.npmjs.org/fastq/-/fastq-1.17.1.tgz"
  "version" "1.17.1"
  dependencies:
    "reusify" "^1.0.4"

"file-entry-cache@^6.0.1":
  "integrity" "sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg=="
  "resolved" "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "flat-cache" "^3.0.4"

"fill-range@^7.0.1":
  "integrity" "sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ=="
  "resolved" "https://registry.npmjs.org/fill-range/-/fill-range-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "to-regex-range" "^5.0.1"

"find-up@^5.0.0":
  "integrity" "sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng=="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "locate-path" "^6.0.0"
    "path-exists" "^4.0.0"

"flat-cache@^3.0.4":
  "integrity" "sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw=="
  "resolved" "https://registry.npmjs.org/flat-cache/-/flat-cache-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "flatted" "^3.2.9"
    "keyv" "^4.5.3"
    "rimraf" "^3.0.2"

"flatpickr@^4.6.13":
  "integrity" "sha512-97PMG/aywoYpB4IvbvUJi0RQi8vearvU0oov1WW3k0WZPBMrTQVqekSX5CjSG/M4Q3i6A/0FKXC7RyAoAUUSPw=="
  "resolved" "https://registry.npmjs.org/flatpickr/-/flatpickr-4.6.13.tgz"
  "version" "4.6.13"

"flatted@^3.2.9":
  "integrity" "sha512-noqGuLw158+DuD9UPRKHpJ2hGxpFyDlYYrfM0mWt4XhT4n0lwzTLh70Tkdyy4kyTmyTT9Bv7bWAJqw7cgkEXDg=="
  "resolved" "https://registry.npmjs.org/flatted/-/flatted-3.3.0.tgz"
  "version" "3.3.0"

"follow-redirects@^1.15.4":
  "integrity" "sha512-vSFWUON1B+yAw1VN4xMfxgn5fTUiaOzAJCKBwIIgT/+7CuGy9+r+5gITvP62j3RmaD5Ph65UaERdOSRGUzZtgw=="
  "resolved" "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.5.tgz"
  "version" "1.15.5"

"form-data@^4.0.0":
  "integrity" "sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww=="
  "resolved" "https://registry.npmjs.org/form-data/-/form-data-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.8"
    "mime-types" "^2.1.12"

"fs.realpath@^1.0.0":
  "integrity" "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw=="
  "resolved" "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
  "version" "1.0.0"

"glob-parent@^5.1.2":
  "integrity" "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow=="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob-parent@^6.0.2":
  "integrity" "sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A=="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "is-glob" "^4.0.3"

"glob-parent@~5.1.2":
  "integrity" "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow=="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob@^7.1.3":
  "integrity" "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q=="
  "resolved" "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz"
  "version" "7.2.3"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.1.1"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"globals@^13.19.0":
  "integrity" "sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ=="
  "resolved" "https://registry.npmjs.org/globals/-/globals-13.24.0.tgz"
  "version" "13.24.0"
  dependencies:
    "type-fest" "^0.20.2"

"graphemer@^1.4.0":
  "integrity" "sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag=="
  "resolved" "https://registry.npmjs.org/graphemer/-/graphemer-1.4.0.tgz"
  "version" "1.4.0"

"has-flag@^4.0.0":
  "integrity" "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ=="
  "resolved" "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz"
  "version" "4.0.0"

"highlight.js@^11.0.1", "highlight.js@^11.9.0":
  "integrity" "sha512-fJ7cW7fQGCYAkgv4CPfwFHrfd/cLS4Hau96JuJ+ZTOWhjnhoeN1ub1tFmALm/+lW5z4WCAuAV9bm05AP0mS6Gw=="
  "resolved" "https://registry.npmjs.org/highlight.js/-/highlight.js-11.9.0.tgz"
  "version" "11.9.0"

"ignore@^5.2.0":
  "integrity" "sha512-5Fytz/IraMjqpwfd34ke28PTVMjZjJG2MPn5t7OE4eUCUNf8BAa7b5WUS9/Qvr6mwOQS7Mk6vdsMno5he+T8Xw=="
  "resolved" "https://registry.npmjs.org/ignore/-/ignore-5.3.1.tgz"
  "version" "5.3.1"

"immutable@^4.0.0":
  "integrity" "sha512-8eabxkth9gZatlwl5TBuJnCsoTADlL6ftEr7A4qgdaTsPyreilDSnUk57SO+jfKcNtxPa22U5KK6DSeAYhpBJw=="
  "resolved" "https://registry.npmjs.org/immutable/-/immutable-4.3.5.tgz"
  "version" "4.3.5"

"import-fresh@^3.2.1":
  "integrity" "sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw=="
  "resolved" "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "parent-module" "^1.0.0"
    "resolve-from" "^4.0.0"

"imurmurhash@^0.1.4":
  "integrity" "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA=="
  "resolved" "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"inflight@^1.0.4":
  "integrity" "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA=="
  "resolved" "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "once" "^1.3.0"
    "wrappy" "1"

"inherits@2":
  "integrity" "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="
  "resolved" "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  "version" "2.0.4"

"is-binary-path@~2.1.0":
  "integrity" "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw=="
  "resolved" "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "binary-extensions" "^2.0.0"

"is-extglob@^2.1.1":
  "integrity" "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ=="
  "resolved" "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-glob@^4.0.0", "is-glob@^4.0.1", "is-glob@^4.0.3", "is-glob@~4.0.1":
  "integrity" "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg=="
  "resolved" "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "is-extglob" "^2.1.1"

"is-number@^7.0.0":
  "integrity" "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng=="
  "resolved" "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  "version" "7.0.0"

"is-path-inside@^3.0.3":
  "integrity" "sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ=="
  "resolved" "https://registry.npmjs.org/is-path-inside/-/is-path-inside-3.0.3.tgz"
  "version" "3.0.3"

"isexe@^2.0.0":
  "integrity" "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw=="
  "resolved" "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  "version" "2.0.0"

"js-yaml@^4.1.0":
  "integrity" "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA=="
  "resolved" "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "argparse" "^2.0.1"

"json-buffer@3.0.1":
  "integrity" "sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ=="
  "resolved" "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz"
  "version" "3.0.1"

"json-schema-traverse@^0.4.1":
  "integrity" "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="
  "resolved" "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-stable-stringify-without-jsonify@^1.0.1":
  "integrity" "sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw=="
  "resolved" "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  "version" "1.0.1"

"json5@^2.2.3":
  "integrity" "sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg=="
  "resolved" "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz"
  "version" "2.2.3"

"jsonc-eslint-parser@^2.3.0":
  "integrity" "sha512-WYDyuc/uFcGp6YtM2H0uKmUwieOuzeE/5YocFJLnLfclZ4inf3mRn8ZVy1s7Hxji7Jxm6Ss8gqpexD/GlKoGgg=="
  "resolved" "https://registry.npmjs.org/jsonc-eslint-parser/-/jsonc-eslint-parser-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "acorn" "^8.5.0"
    "eslint-visitor-keys" "^3.0.0"
    "espree" "^9.0.0"
    "semver" "^7.3.5"

"just-extend@^5.0.0":
  "integrity" "sha512-b+z6yF1d4EOyDgylzQo5IminlUmzSeqR1hs/bzjBNjuGras4FXq/6TrzjxfN0j+TmI0ltJzTNlqXUMCniciwKQ=="
  "resolved" "https://registry.npmjs.org/just-extend/-/just-extend-5.1.1.tgz"
  "version" "5.1.1"

"keyv@^4.5.3":
  "integrity" "sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw=="
  "resolved" "https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz"
  "version" "4.5.4"
  dependencies:
    "json-buffer" "3.0.1"

"levn@^0.4.1":
  "integrity" "sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ=="
  "resolved" "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "prelude-ls" "^1.2.1"
    "type-check" "~0.4.0"

"linkify-it@^5.0.0":
  "integrity" "sha512-5aHCbzQRADcdP+ATqnDuhhJ/MRIqDkZX5pyjFHRRysS8vZ5AbqGEoFIb6pYHPZ+L/OC2Lc+xT8uHVVR5CAK/wQ=="
  "resolved" "https://registry.npmjs.org/linkify-it/-/linkify-it-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "uc.micro" "^2.0.0"

"locate-path@^6.0.0":
  "integrity" "sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw=="
  "resolved" "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "p-locate" "^5.0.0"

"lodash-es@*", "lodash-es@^4.17.21", "lodash-es@4.17.21":
  "integrity" "sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw=="
  "resolved" "https://registry.npmjs.org/lodash-es/-/lodash-es-4.17.21.tgz"
  "version" "4.17.21"

"lodash-unified@^1.0.2":
  "integrity" "sha512-WK9qSozxXOD7ZJQlpSqOT+om2ZfcT4yO+03FuzAHD0wF6S0l0090LRPDx3vhTTLZ8cFKpBn+IOcVXK6qOcIlfQ=="
  "resolved" "https://registry.npmjs.org/lodash-unified/-/lodash-unified-1.0.3.tgz"
  "version" "1.0.3"

"lodash.merge@^4.6.2":
  "integrity" "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ=="
  "resolved" "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz"
  "version" "4.6.2"

"lodash@*", "lodash@^4.17.21":
  "integrity" "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="
  "resolved" "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
  "version" "4.17.21"

"lru-cache@^6.0.0":
  "integrity" "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA=="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "yallist" "^4.0.0"

"magic-string@^0.30.11":
  "integrity" "sha512-+Wri9p0QHMy+545hKww7YAu5NyzF8iomPL/RQazugQ9+Ez4Ic3mERMd8ZTX5rfK944j+560ZJi8iAwgak1Ac7A=="
  "resolved" "https://registry.npmjs.org/magic-string/-/magic-string-0.30.11.tgz"
  "version" "0.30.11"
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"

"markdown-it@^14.0.0":
  "integrity" "sha512-seFjF0FIcPt4P9U39Bq1JYblX0KZCjDLFFQPHpL5AzHpqPEKtosxmdq/LTVZnjfH7tjt9BxStm+wXcDBNuYmzw=="
  "resolved" "https://registry.npmjs.org/markdown-it/-/markdown-it-14.0.0.tgz"
  "version" "14.0.0"
  dependencies:
    "argparse" "^2.0.1"
    "entities" "^4.4.0"
    "linkify-it" "^5.0.0"
    "mdurl" "^2.0.0"
    "punycode.js" "^2.3.1"
    "uc.micro" "^2.0.0"

"material-colors@^1.2.6":
  "integrity" "sha512-6qE4B9deFBIa9YSpOc9O0Sgc43zTeVYbgDT5veRKSlB2+ZuHNoVVxA1L/ckMUayV9Ay9y7Z/SZCLcGteW9i7bg=="
  "resolved" "https://registry.npmjs.org/material-colors/-/material-colors-1.2.6.tgz"
  "version" "1.2.6"

"mdurl@^2.0.0":
  "integrity" "sha512-Lf+9+2r+Tdp5wXDXC4PcIBjTDtq4UKjCPMQhKIuzpJNW0b96kVqSwW0bT7FhRSfmAiFYgP+SCRvdrDozfh0U5w=="
  "resolved" "https://registry.npmjs.org/mdurl/-/mdurl-2.0.0.tgz"
  "version" "2.0.0"

"memoize-one@^6.0.0":
  "integrity" "sha512-rkpe71W0N0c0Xz6QD0eJETuWAJGnJ9afsl1srmwPrI+yBCkge5EycXXbYRyvL29zZVUWQCY7InPRCv3GDXuZNw=="
  "resolved" "https://registry.npmjs.org/memoize-one/-/memoize-one-6.0.0.tgz"
  "version" "6.0.0"

"merge2@^1.3.0":
  "integrity" "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg=="
  "resolved" "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz"
  "version" "1.4.1"

"micromatch@^4.0.4":
  "integrity" "sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA=="
  "resolved" "https://registry.npmjs.org/micromatch/-/micromatch-4.0.5.tgz"
  "version" "4.0.5"
  dependencies:
    "braces" "^3.0.2"
    "picomatch" "^2.3.1"

"mime-db@1.52.0":
  "integrity" "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="
  "resolved" "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz"
  "version" "1.52.0"

"mime-types@^2.1.12":
  "integrity" "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw=="
  "resolved" "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz"
  "version" "2.1.35"
  dependencies:
    "mime-db" "1.52.0"

"minimatch@^3.0.5", "minimatch@^3.1.1", "minimatch@^3.1.2":
  "integrity" "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw=="
  "resolved" "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "brace-expansion" "^1.1.7"

"mlly@^1.2.0", "mlly@^1.6.1":
  "integrity" "sha512-vLgaHvaeunuOXHSmEbZ9izxPx3USsk8KCQ8iC+aTlp5sKRSoZvwhHh5L9VbKSaVC6sJDqbyohIS76E2VmHIPAA=="
  "resolved" "https://registry.npmjs.org/mlly/-/mlly-1.6.1.tgz"
  "version" "1.6.1"
  dependencies:
    "acorn" "^8.11.3"
    "pathe" "^1.1.2"
    "pkg-types" "^1.0.3"
    "ufo" "^1.3.2"

"moment-timezone@^0.5.45":
  "integrity" "sha512-ZXm9b36esbe7OmdABqIWJuBBiLLwAjrN7CE+7sYdCCx82Nabt1wHDj8TVseS59QIlfFPbOoiBPm6ca9BioG4hw=="
  "resolved" "https://registry.npmjs.org/moment-timezone/-/moment-timezone-0.5.46.tgz"
  "version" "0.5.46"
  dependencies:
    "moment" "^2.29.4"

"moment@^2.29.4", "moment@^2.30.1":
  "integrity" "sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how=="
  "resolved" "https://registry.npmjs.org/moment/-/moment-2.30.1.tgz"
  "version" "2.30.1"

"ms@2.1.2":
  "integrity" "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz"
  "version" "2.1.2"

"nanoid@^3.3.7":
  "integrity" "sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g=="
  "resolved" "https://registry.npmjs.org/nanoid/-/nanoid-3.3.7.tgz"
  "version" "3.3.7"

"natural-compare@^1.4.0":
  "integrity" "sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw=="
  "resolved" "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz"
  "version" "1.4.0"

"normalize-path@^3.0.0", "normalize-path@~3.0.0":
  "integrity" "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA=="
  "resolved" "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"normalize-wheel-es@^1.2.0":
  "integrity" "sha512-Wj7+EJQ8mSuXr2iWfnujrimU35R2W4FAErEyTmJoJ7ucwTn2hOUSsRehMb5RSYkxXGTM7Y9QpvPmp++w5ftoJw=="
  "resolved" "https://registry.npmjs.org/normalize-wheel-es/-/normalize-wheel-es-1.2.0.tgz"
  "version" "1.2.0"

"nprogress@^0.2.0":
  "integrity" "sha512-I19aIingLgR1fmhftnbWWO3dXc0hSxqHQHQb3H8m+K3TnEn/iSeTZZOyvKXWqQESMwuUVnatlCnZdLBZZt2VSA=="
  "resolved" "https://registry.npmjs.org/nprogress/-/nprogress-0.2.0.tgz"
  "version" "0.2.0"

"nth-check@^2.1.1":
  "integrity" "sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w=="
  "resolved" "https://registry.npmjs.org/nth-check/-/nth-check-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "boolbase" "^1.0.0"

"once@^1.3.0":
  "integrity" "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w=="
  "resolved" "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1"

"optionator@^0.9.3":
  "integrity" "sha512-JjCoypp+jKn1ttEFExxhetCKeJt9zhAgAve5FXHixTvFDW/5aEktX9bufBKLRRMdU7bNtpLfcGu94B3cdEJgjg=="
  "resolved" "https://registry.npmjs.org/optionator/-/optionator-0.9.3.tgz"
  "version" "0.9.3"
  dependencies:
    "@aashutoshrathi/word-wrap" "^1.2.3"
    "deep-is" "^0.1.3"
    "fast-levenshtein" "^2.0.6"
    "levn" "^0.4.1"
    "prelude-ls" "^1.2.1"
    "type-check" "^0.4.0"

"orderedmap@^2.0.0":
  "integrity" "sha512-TvAWxi0nDe1j/rtMcWcIj94+Ffe6n7zhow33h40SKxmsmozs6dz/e+EajymfoFcHd7sxNn8yHM8839uixMOV6g=="
  "resolved" "https://registry.npmjs.org/orderedmap/-/orderedmap-2.1.1.tgz"
  "version" "2.1.1"

"p-limit@^3.0.2":
  "integrity" "sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ=="
  "resolved" "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "yocto-queue" "^0.1.0"

"p-locate@^5.0.0":
  "integrity" "sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw=="
  "resolved" "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-limit" "^3.0.2"

"parent-module@^1.0.0":
  "integrity" "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g=="
  "resolved" "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "callsites" "^3.0.0"

"path-exists@^4.0.0":
  "integrity" "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w=="
  "resolved" "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz"
  "version" "4.0.0"

"path-is-absolute@^1.0.0":
  "integrity" "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg=="
  "resolved" "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  "version" "1.0.1"

"path-key@^3.1.0":
  "integrity" "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q=="
  "resolved" "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  "version" "3.1.1"

"pathe@^1.0.0", "pathe@^1.1.2":
  "integrity" "sha512-whLdWMYL2TwI08hn8/ZqAbrVemu0LNaNNJZX73O6qaIdCTfXutsLhMkjdENX0qhsQ9uIimo4/aQOmXkoon2nDQ=="
  "resolved" "https://registry.npmjs.org/pathe/-/pathe-1.1.2.tgz"
  "version" "1.1.2"

"picocolors@^1.0.0", "picocolors@^1.1.0":
  "integrity" "sha512-TQ92mBOW0l3LeMeyLV6mzy/kWr8lkd/hp3mTg7wYK7zJhuBStmGMBG0BdeDZS/dZx1IukaX6Bk11zcln25o1Aw=="
  "resolved" "https://registry.npmjs.org/picocolors/-/picocolors-1.1.0.tgz"
  "version" "1.1.0"

"picomatch@^2.0.4", "picomatch@^2.2.1", "picomatch@^2.3.1":
  "integrity" "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="
  "resolved" "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  "version" "2.3.1"

"pinia@^2.1.6":
  "integrity" "sha512-+C2AHFtcFqjPih0zpYuvof37SFxMQ7OEG2zV9jRI12i9BOy3YQVAHwdKtyyc8pDcDyIc33WCIsZaCFWU7WWxGQ=="
  "resolved" "https://registry.npmjs.org/pinia/-/pinia-2.1.7.tgz"
  "version" "2.1.7"
  dependencies:
    "@vue/devtools-api" "^6.5.0"
    "vue-demi" ">=0.14.5"

"pkg-types@^1.0.3":
  "integrity" "sha512-/RpmvKdxKf8uILTtoOhAgf30wYbP2Qw+L9p3Rvshx1JZVX+XQNZQFjlbmGHEGIm4CkVPlSn+NXmIM8+9oWQaSA=="
  "resolved" "https://registry.npmjs.org/pkg-types/-/pkg-types-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "confbox" "^0.1.7"
    "mlly" "^1.6.1"
    "pathe" "^1.1.2"

"postcss-selector-parser@^6.0.13":
  "integrity" "sha512-rEYkQOMUCEMhsKbK66tbEU9QVIxbhN18YiniAwA7XQYTVBqrBy+P2p5JcdqsHgKM2zWylp8d7J6eszocfds5Sw=="
  "resolved" "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.15.tgz"
  "version" "6.0.15"
  dependencies:
    "cssesc" "^3.0.0"
    "util-deprecate" "^1.0.2"

"postcss@^8.4.27", "postcss@^8.4.47":
  "integrity" "sha512-56rxCq7G/XfB4EkXq9Egn5GCqugWvDFjafDOThIdMBsI15iqPqR5r15TfSr1YPYeEI19YeaXMCbY6u88Y76GLQ=="
  "resolved" "https://registry.npmjs.org/postcss/-/postcss-8.4.47.tgz"
  "version" "8.4.47"
  dependencies:
    "nanoid" "^3.3.7"
    "picocolors" "^1.1.0"
    "source-map-js" "^1.2.1"

"preact@~10.12.1":
  "integrity" "sha512-l8386ixSsBdbreOAkqtrwqHwdvR35ID8c3rKPa8lCWuO86dBi32QWHV4vfsZK1utLLFMvw+Z5Ad4XLkZzchscg=="
  "resolved" "https://registry.npmjs.org/preact/-/preact-10.12.1.tgz"
  "version" "10.12.1"

"prelude-ls@^1.2.1":
  "integrity" "sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g=="
  "resolved" "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz"
  "version" "1.2.1"

"property-expr@^2.0.5":
  "integrity" "sha512-SVtmxhRE/CGkn3eZY1T6pC8Nln6Fr/lu1mKSgRud0eC73whjGfoAogbn78LkD8aFL0zz3bAFerKSnOl7NlErBA=="
  "resolved" "https://registry.npmjs.org/property-expr/-/property-expr-2.0.6.tgz"
  "version" "2.0.6"

"prosemirror-changeset@^2.2.1":
  "integrity" "sha512-J7msc6wbxB4ekDFj+n9gTW/jav/p53kdlivvuppHsrZXCaQdVgRghoZbSS3kwrRyAstRVQ4/+u5k7YfLgkkQvQ=="
  "resolved" "https://registry.npmjs.org/prosemirror-changeset/-/prosemirror-changeset-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "prosemirror-transform" "^1.0.0"

"prosemirror-collab@^1.3.1":
  "integrity" "sha512-4SnynYR9TTYaQVXd/ieUvsVV4PDMBzrq2xPUWutHivDuOshZXqQ5rGbZM84HEaXKbLdItse7weMGOUdDVcLKEQ=="
  "resolved" "https://registry.npmjs.org/prosemirror-collab/-/prosemirror-collab-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "prosemirror-state" "^1.0.0"

"prosemirror-commands@^1.0.0", "prosemirror-commands@^1.5.2":
  "integrity" "sha512-hgLcPaakxH8tu6YvVAaILV2tXYsW3rAdDR8WNkeKGcgeMVQg3/TMhPdVoh7iAmfgVjZGtcOSjKiQaoeKjzd2mQ=="
  "resolved" "https://registry.npmjs.org/prosemirror-commands/-/prosemirror-commands-1.5.2.tgz"
  "version" "1.5.2"
  dependencies:
    "prosemirror-model" "^1.0.0"
    "prosemirror-state" "^1.0.0"
    "prosemirror-transform" "^1.0.0"

"prosemirror-dropcursor@^1.8.1":
  "integrity" "sha512-M30WJdJZLyXHi3N8vxN6Zh5O8ZBbQCz0gURTfPmTIBNQ5pxrdU7A58QkNqfa98YEjSAL1HUyyU34f6Pm5xBSGw=="
  "resolved" "https://registry.npmjs.org/prosemirror-dropcursor/-/prosemirror-dropcursor-1.8.1.tgz"
  "version" "1.8.1"
  dependencies:
    "prosemirror-state" "^1.0.0"
    "prosemirror-transform" "^1.1.0"
    "prosemirror-view" "^1.1.0"

"prosemirror-gapcursor@^1.3.2":
  "integrity" "sha512-wtjswVBd2vaQRrnYZaBCbyDqr232Ed4p2QPtRIUK5FuqHYKGWkEwl08oQM4Tw7DOR0FsasARV5uJFvMZWxdNxQ=="
  "resolved" "https://registry.npmjs.org/prosemirror-gapcursor/-/prosemirror-gapcursor-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "prosemirror-keymap" "^1.0.0"
    "prosemirror-model" "^1.0.0"
    "prosemirror-state" "^1.0.0"
    "prosemirror-view" "^1.0.0"

"prosemirror-history@^1.0.0", "prosemirror-history@^1.4.1":
  "integrity" "sha512-2JZD8z2JviJrboD9cPuX/Sv/1ChFng+xh2tChQ2X4bB2HeK+rra/bmJ3xGntCcjhOqIzSDG6Id7e8RJ9QPXLEQ=="
  "resolved" "https://registry.npmjs.org/prosemirror-history/-/prosemirror-history-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "prosemirror-state" "^1.2.2"
    "prosemirror-transform" "^1.0.0"
    "prosemirror-view" "^1.31.0"
    "rope-sequence" "^1.3.0"

"prosemirror-inputrules@^1.4.0":
  "integrity" "sha512-6ygpPRuTJ2lcOXs9JkefieMst63wVJBgHZGl5QOytN7oSZs3Co/BYbc3Yx9zm9H37Bxw8kVzCnDsihsVsL4yEg=="
  "resolved" "https://registry.npmjs.org/prosemirror-inputrules/-/prosemirror-inputrules-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "prosemirror-state" "^1.0.0"
    "prosemirror-transform" "^1.0.0"

"prosemirror-keymap@^1.0.0", "prosemirror-keymap@^1.1.2", "prosemirror-keymap@^1.2.2":
  "integrity" "sha512-EAlXoksqC6Vbocqc0GtzCruZEzYgrn+iiGnNjsJsH4mrnIGex4qbLdWWNza3AW5W36ZRrlBID0eM6bdKH4OStQ=="
  "resolved" "https://registry.npmjs.org/prosemirror-keymap/-/prosemirror-keymap-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "prosemirror-state" "^1.0.0"
    "w3c-keyname" "^2.2.0"

"prosemirror-markdown@^1.13.0":
  "integrity" "sha512-UziddX3ZYSYibgx8042hfGKmukq5Aljp2qoBiJRejD/8MH70siQNz5RB1TrdTPheqLMy4aCe4GYNF10/3lQS5g=="
  "resolved" "https://registry.npmjs.org/prosemirror-markdown/-/prosemirror-markdown-1.13.0.tgz"
  "version" "1.13.0"
  dependencies:
    "markdown-it" "^14.0.0"
    "prosemirror-model" "^1.20.0"

"prosemirror-menu@^1.2.4":
  "integrity" "sha512-S/bXlc0ODQup6aiBbWVsX/eM+xJgCTAfMq/nLqaO5ID/am4wS0tTCIkzwytmao7ypEtjj39i7YbJjAgO20mIqA=="
  "resolved" "https://registry.npmjs.org/prosemirror-menu/-/prosemirror-menu-1.2.4.tgz"
  "version" "1.2.4"
  dependencies:
    "crelt" "^1.0.0"
    "prosemirror-commands" "^1.0.0"
    "prosemirror-history" "^1.0.0"
    "prosemirror-state" "^1.0.0"

"prosemirror-model@^1.0.0", "prosemirror-model@^1.19.0", "prosemirror-model@^1.20.0", "prosemirror-model@^1.21.0", "prosemirror-model@^1.22.1", "prosemirror-model@^1.22.2", "prosemirror-model@^1.8.1":
  "integrity" "sha512-V4XCysitErI+i0rKFILGt/xClnFJaohe/wrrlT2NSZ+zk8ggQfDH4x2wNK7Gm0Hp4CIoWizvXFP7L9KMaCuI0Q=="
  "resolved" "https://registry.npmjs.org/prosemirror-model/-/prosemirror-model-1.22.3.tgz"
  "version" "1.22.3"
  dependencies:
    "orderedmap" "^2.0.0"

"prosemirror-schema-basic@^1.2.3":
  "integrity" "sha512-h+H0OQwZVqMon1PNn0AG9cTfx513zgIG2DY00eJ00Yvgb3UD+GQ/VlWW5rcaxacpCGT1Yx8nuhwXk4+QbXUfJA=="
  "resolved" "https://registry.npmjs.org/prosemirror-schema-basic/-/prosemirror-schema-basic-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "prosemirror-model" "^1.19.0"

"prosemirror-schema-list@^1.4.1":
  "integrity" "sha512-jbDyaP/6AFfDfu70VzySsD75Om2t3sXTOdl5+31Wlxlg62td1haUpty/ybajSfJ1pkGadlOfwQq9kgW5IMo1Rg=="
  "resolved" "https://registry.npmjs.org/prosemirror-schema-list/-/prosemirror-schema-list-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "prosemirror-model" "^1.0.0"
    "prosemirror-state" "^1.0.0"
    "prosemirror-transform" "^1.7.3"

"prosemirror-state@^1.0.0", "prosemirror-state@^1.2.2", "prosemirror-state@^1.3.1", "prosemirror-state@^1.4.2", "prosemirror-state@^1.4.3":
  "integrity" "sha512-goFKORVbvPuAQaXhpbemJFRKJ2aixr+AZMGiquiqKxaucC6hlpHNZHWgz5R7dS4roHiwq9vDctE//CZ++o0W1Q=="
  "resolved" "https://registry.npmjs.org/prosemirror-state/-/prosemirror-state-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "prosemirror-model" "^1.0.0"
    "prosemirror-transform" "^1.0.0"
    "prosemirror-view" "^1.27.0"

"prosemirror-tables@^1.4.0":
  "integrity" "sha512-VMx4zlYWm7aBlZ5xtfJHpqa3Xgu3b7srV54fXYnXgsAcIGRqKSrhiK3f89omzzgaAgAtDOV4ImXnLKhVfheVNQ=="
  "resolved" "https://registry.npmjs.org/prosemirror-tables/-/prosemirror-tables-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "prosemirror-keymap" "^1.1.2"
    "prosemirror-model" "^1.8.1"
    "prosemirror-state" "^1.3.1"
    "prosemirror-transform" "^1.2.1"
    "prosemirror-view" "^1.13.3"

"prosemirror-trailing-node@^2.0.9":
  "integrity" "sha512-YvyIn3/UaLFlFKrlJB6cObvUhmwFNZVhy1Q8OpW/avoTbD/Y7H5EcjK4AZFKhmuS6/N6WkGgt7gWtBWDnmFvHg=="
  "resolved" "https://registry.npmjs.org/prosemirror-trailing-node/-/prosemirror-trailing-node-2.0.9.tgz"
  "version" "2.0.9"
  dependencies:
    "@remirror/core-constants" "^2.0.2"
    "escape-string-regexp" "^4.0.0"

"prosemirror-transform@^1.0.0", "prosemirror-transform@^1.1.0", "prosemirror-transform@^1.2.1", "prosemirror-transform@^1.7.3", "prosemirror-transform@^1.9.0":
  "integrity" "sha512-9UOgFSgN6Gj2ekQH5CTDJ8Rp/fnKR2IkYfGdzzp5zQMFsS4zDllLVx/+jGcX86YlACpG7UR5fwAXiWzxqWtBTg=="
  "resolved" "https://registry.npmjs.org/prosemirror-transform/-/prosemirror-transform-1.10.0.tgz"
  "version" "1.10.0"
  dependencies:
    "prosemirror-model" "^1.21.0"

"prosemirror-view@^1.0.0", "prosemirror-view@^1.1.0", "prosemirror-view@^1.13.3", "prosemirror-view@^1.27.0", "prosemirror-view@^1.31.0", "prosemirror-view@^1.33.8", "prosemirror-view@^1.33.9":
  "integrity" "sha512-tPX/V2Xd70vrAGQ/V9CppJtPKnQyQMypJGlLylvdI94k6JaG+4P6fVmXPR1zc1eVTW0gq3c6zsfqwJKCRLaG9Q=="
  "resolved" "https://registry.npmjs.org/prosemirror-view/-/prosemirror-view-1.34.2.tgz"
  "version" "1.34.2"
  dependencies:
    "prosemirror-model" "^1.20.0"
    "prosemirror-state" "^1.0.0"
    "prosemirror-transform" "^1.1.0"

"proxy-from-env@^1.1.0":
  "integrity" "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg=="
  "resolved" "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz"
  "version" "1.1.0"

"punycode.js@^2.3.1":
  "integrity" "sha512-uxFIHU0YlHYhDQtV4R9J6a52SLx28BCjT+4ieh7IGbgwVJWO+km431c4yRlREUAsAmt/uMjQUyQHNEPf0M39CA=="
  "resolved" "https://registry.npmjs.org/punycode.js/-/punycode.js-2.3.1.tgz"
  "version" "2.3.1"

"punycode@^2.1.0":
  "integrity" "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg=="
  "resolved" "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz"
  "version" "2.3.1"

"queue-microtask@^1.2.2":
  "integrity" "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A=="
  "resolved" "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz"
  "version" "1.2.3"

"react-dnd-html5-backend@^16.0.1":
  "integrity" "sha512-Wu3dw5aDJmOGw8WjH1I1/yTH+vlXEL4vmjk5p+MHxP8HuHJS1lAGeIdG/hze1AvNeXWo/JgULV87LyQOr+r5jw=="
  "resolved" "https://registry.npmjs.org/react-dnd-html5-backend/-/react-dnd-html5-backend-16.0.1.tgz"
  "version" "16.0.1"
  dependencies:
    "dnd-core" "^16.0.1"

"readdirp@~3.6.0":
  "integrity" "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA=="
  "resolved" "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "picomatch" "^2.2.1"

"redux@^4.1.2", "redux@^4.2.0":
  "integrity" "sha512-LAUYz4lc+Do8/g7aeRa8JkyDErK6ekstQaqWQrNRW//MY1TvCEpMtpTWvlQ+FPbWCx+Xixu/6SHt5N0HR+SB4w=="
  "resolved" "https://registry.npmjs.org/redux/-/redux-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "@babel/runtime" "^7.9.2"

"regenerator-runtime@^0.14.0":
  "integrity" "sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw=="
  "resolved" "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz"
  "version" "0.14.1"

"resolve-from@^4.0.0":
  "integrity" "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g=="
  "resolved" "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz"
  "version" "4.0.0"

"reusify@^1.0.4":
  "integrity" "sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw=="
  "resolved" "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz"
  "version" "1.0.4"

"rimraf@^3.0.2":
  "integrity" "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA=="
  "resolved" "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "glob" "^7.1.3"

"rollup@^1.20.0||^2.0.0||^3.0.0||^4.0.0", "rollup@^3.27.1":
  "integrity" "sha512-oWzmBZwvYrU0iJHtDmhsm662rC15FRXmcjCk1xD771dFDx5jJ02ufAQQTn0etB2emNk4J9EZg/yWKpsn9BWGRw=="
  "resolved" "https://registry.npmjs.org/rollup/-/rollup-3.29.4.tgz"
  "version" "3.29.4"
  optionalDependencies:
    "fsevents" "~2.3.2"

"rope-sequence@^1.3.0":
  "integrity" "sha512-UT5EDe2cu2E/6O4igUr5PSFs23nvvukicWHx6GnOPlHAiiYbzNuCRQCuiUdHJQcqKalLKlrYJnjY0ySGsXNQXQ=="
  "resolved" "https://registry.npmjs.org/rope-sequence/-/rope-sequence-1.3.4.tgz"
  "version" "1.3.4"

"run-parallel@^1.1.9":
  "integrity" "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA=="
  "resolved" "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "queue-microtask" "^1.2.2"

"sass@*", "sass@^1.69.1":
  "integrity" "sha512-wovtnV2PxzteLlfNzbgm1tFXPLoZILYAMJtvoXXkD7/+1uP41eKkIt1ypWq5/q2uT94qHjXehEYfmjKOvjL9sg=="
  "resolved" "https://registry.npmjs.org/sass/-/sass-1.71.1.tgz"
  "version" "1.71.1"
  dependencies:
    "chokidar" ">=3.0.0 <4.0.0"
    "immutable" "^4.0.0"
    "source-map-js" ">=0.6.2 <2.0.0"

"semver@^7.3.5", "semver@^7.3.6", "semver@^7.5.4":
  "integrity" "sha512-EnwXhrlwXMk9gKu5/flx5sv/an57AkRplG3hTK68W7FRDN+k+OWBj65M7719OkA82XLBxrcX0KSHj+X5COhOVg=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-7.6.0.tgz"
  "version" "7.6.0"
  dependencies:
    "lru-cache" "^6.0.0"

"shebang-command@^2.0.0":
  "integrity" "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA=="
  "resolved" "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "shebang-regex" "^3.0.0"

"shebang-regex@^3.0.0":
  "integrity" "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A=="
  "resolved" "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  "version" "3.0.0"

"simplebar-core@^1.2.4":
  "integrity" "sha512-P+Sqshef4fq3++gQ82TgNYcgl3qZFSCP5jS2/8NMmw18oagXOijMzs1G+vm6RUY3oMvpwH3wGoqh9u6SyDjHfQ=="
  "resolved" "https://registry.npmjs.org/simplebar-core/-/simplebar-core-1.2.4.tgz"
  "version" "1.2.4"
  dependencies:
    "@types/lodash-es" "^4.17.6"
    "can-use-dom" "^0.1.0"
    "lodash" "^4.17.21"
    "lodash-es" "^4.17.21"

"simplebar@^6.2.5":
  "integrity" "sha512-vfxKR6KNBsPx7+sZnqO7T8VuCvi4px6OlycrrkNgyjvoHhRW7LIyVkHhUfXxbz33Gw99Wb9UMMsnEZv35wtLSw=="
  "resolved" "https://registry.npmjs.org/simplebar/-/simplebar-6.2.5.tgz"
  "version" "6.2.5"
  dependencies:
    "can-use-dom" "^0.1.0"
    "simplebar-core" "^1.2.4"

"source-map-js@^1.0.1", "source-map-js@^1.0.2", "source-map-js@^1.2.0", "source-map-js@^1.2.1", "source-map-js@>=0.6.2 <2.0.0":
  "integrity" "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA=="
  "resolved" "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz"
  "version" "1.2.1"

"source-map@~0.6.1":
  "integrity" "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="
  "resolved" "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  "version" "0.6.1"

"strip-ansi@^6.0.1":
  "integrity" "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A=="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "ansi-regex" "^5.0.1"

"strip-json-comments@^3.1.1":
  "integrity" "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig=="
  "resolved" "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  "version" "3.1.1"

"supports-color@^7.1.0":
  "integrity" "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "has-flag" "^4.0.0"

"sweetalert2@^11.7.32":
  "integrity" "sha512-q9eE3EKhMcpIDU/Xcz7z5lk8axCGkgxwK47gXGrrfncnBJWxHPPHnBVAjfsVXcTt8Yi8U6HNEcBRSu+qGeyFdA=="
  "resolved" "https://registry.npmjs.org/sweetalert2/-/sweetalert2-11.10.5.tgz"
  "version" "11.10.5"

"text-table@^0.2.0":
  "integrity" "sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw=="
  "resolved" "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz"
  "version" "0.2.0"

"tiny-case@^1.0.3":
  "integrity" "sha512-Eet/eeMhkO6TX8mnUteS9zgPbUMQa4I6Kkp5ORiBD5476/m+PIRiumP5tmh5ioJpH7k51Kehawy2UDfsnxxY8Q=="
  "resolved" "https://registry.npmjs.org/tiny-case/-/tiny-case-1.0.3.tgz"
  "version" "1.0.3"

"tippy.js@^6.3.7":
  "integrity" "sha512-E1d3oP2emgJ9dRQZdf3Kkn0qJgI6ZLpyS5z6ZkY1DF3kaQaBsGZsndEpHwx+eC+tYM41HaSNvNtLx8tU57FzTQ=="
  "resolved" "https://registry.npmjs.org/tippy.js/-/tippy.js-6.3.7.tgz"
  "version" "6.3.7"
  dependencies:
    "@popperjs/core" "^2.9.0"

"to-fast-properties@^2.0.0":
  "integrity" "sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog=="
  "resolved" "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-2.0.0.tgz"
  "version" "2.0.0"

"to-regex-range@^5.0.1":
  "integrity" "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ=="
  "resolved" "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "is-number" "^7.0.0"

"toposort@^2.0.2":
  "integrity" "sha512-0a5EOkAUp8D4moMi2W8ZF8jcga7BgZd91O/yabJCFY8az+XSzeGyTKs0Aoo897iV1Nj6guFq8orWDS96z91oGg=="
  "resolved" "https://registry.npmjs.org/toposort/-/toposort-2.0.2.tgz"
  "version" "2.0.2"

"type-check@^0.4.0", "type-check@~0.4.0":
  "integrity" "sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew=="
  "resolved" "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "prelude-ls" "^1.2.1"

"type-fest@^0.20.2":
  "integrity" "sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ=="
  "resolved" "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz"
  "version" "0.20.2"

"type-fest@^2.19.0":
  "integrity" "sha512-RAH822pAdBgcNMAfWnCBU3CFZcfZ/i1eZjwFU/dsLKumyuuP3niueg2UAukXYF0E2AAoc82ZSSf9J0WQBinzHA=="
  "resolved" "https://registry.npmjs.org/type-fest/-/type-fest-2.19.0.tgz"
  "version" "2.19.0"

"type-fest@^4.8.3":
  "integrity" "sha512-yOGpmOAL7CkKe/91I5O3gPICmJNLJ1G4zFYVAsRHg7M64biSnPtRj0WNQt++bRkjYOqjWXrhnUw1utzmVErAdg=="
  "resolved" "https://registry.npmjs.org/type-fest/-/type-fest-4.26.1.tgz"
  "version" "4.26.1"

"uc.micro@^2.0.0":
  "integrity" "sha512-DffL94LsNOccVn4hyfRe5rdKa273swqeA5DJpMOeFmEn1wCDc7nAbbB0gXlgBCL7TNzeTv6G7XVWzan7iJtfig=="
  "resolved" "https://registry.npmjs.org/uc.micro/-/uc.micro-2.0.0.tgz"
  "version" "2.0.0"

"ufo@^1.3.2":
  "integrity" "sha512-Y7HYmWaFwPUmkoQCUIAYpKqkOf+SbVj/2fJJZ4RJMCfZp0rTGwRbzQD+HghfnhKOjL9E01okqz+ncJskGYfBNw=="
  "resolved" "https://registry.npmjs.org/ufo/-/ufo-1.5.3.tgz"
  "version" "1.5.3"

"unplugin@^1.1.0":
  "integrity" "sha512-d6Mhq8RJeGA8UfKCu54Um4lFA0eSaRa3XxdAJg8tIdxbu1ubW0hBCZUL7yI2uGyYCRndvbK8FLHzqy2XKfeMsg=="
  "resolved" "https://registry.npmjs.org/unplugin/-/unplugin-1.10.1.tgz"
  "version" "1.10.1"
  dependencies:
    "acorn" "^8.11.3"
    "chokidar" "^3.6.0"
    "webpack-sources" "^3.2.3"
    "webpack-virtual-modules" "^0.6.1"

"uri-js@^4.2.2":
  "integrity" "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg=="
  "resolved" "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "punycode" "^2.1.0"

"util-deprecate@^1.0.2":
  "integrity" "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw=="
  "resolved" "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"vanilla-colorful@0.7.2":
  "integrity" "sha512-z2YZusTFC6KnLERx1cgoIRX2CjPRP0W75N+3CC6gbvdX5Ch47rZkEMGO2Xnf+IEmi3RiFLxS18gayMA27iU7Kg=="
  "resolved" "https://registry.npmjs.org/vanilla-colorful/-/vanilla-colorful-0.7.2.tgz"
  "version" "0.7.2"

"vee-validate@^4.12.4":
  "integrity" "sha512-HlpR/6MJ92TW9f135umMZKUqdd/tFQTxLNSf2ImbU4Y/MlLVAUpF1l64VdjTOhbClAqPjCb5p/SqHDxLpUHXrw=="
  "resolved" "https://registry.npmjs.org/vee-validate/-/vee-validate-4.13.2.tgz"
  "version" "4.13.2"
  dependencies:
    "@vue/devtools-api" "^6.6.1"
    "type-fest" "^4.8.3"

"vite@^4.0.0 || ^5.0.0", "vite@^4.4.11":
  "integrity" "sha512-tBCZBNSBbHQkaGyhGCDUGqeo2ph8Fstyp6FMSvTtsXeZSPpSMGlviAOav2hxVTqFcx8Hj/twtWKsMJXNY0xI8w=="
  "resolved" "https://registry.npmjs.org/vite/-/vite-4.5.2.tgz"
  "version" "4.5.2"
  dependencies:
    "esbuild" "^0.18.10"
    "postcss" "^8.4.27"
    "rollup" "^3.27.1"
  optionalDependencies:
    "fsevents" "~2.3.2"

"vue-chartjs@^5.2.0":
  "integrity" "sha512-8XqX0JU8vFZ+WA2/knz4z3ThClduni2Nm0BMe2u0mXgTfd9pXrmJ07QBI+WAij5P/aPmPMX54HCE1seWL37ZdQ=="
  "resolved" "https://registry.npmjs.org/vue-chartjs/-/vue-chartjs-5.3.0.tgz"
  "version" "5.3.0"

"vue-cropperjs@^5.0.0":
  "integrity" "sha512-RhnC8O33uRZNkn74aiHZwNHnBJOXWlS4P6gsRI0lw4cZlWjKSCywZI9oSI9POlIPI6OYv30jvnHMXGch85tw7w=="
  "resolved" "https://registry.npmjs.org/vue-cropperjs/-/vue-cropperjs-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "cropperjs" "^1.5.6"

"vue-dataset@^3.6.1":
  "integrity" "sha512-uSKYAaSKv+V97FLWZn8JzG0VgG8bh6l0ZIDbXkBh5N/IlukZCzMXwm8vD+O0SS11+ivUflKC0IWaO0Gh6xqL4g=="
  "resolved" "https://registry.npmjs.org/vue-dataset/-/vue-dataset-3.6.1.tgz"
  "version" "3.6.1"
  dependencies:
    "vue" "^3.3.4"

"vue-demi@*":
  "integrity" "sha512-nMZBOwuzabUO0nLgIcc6rycZEebF6eeUfaiQx9+WSk8e29IbLvPU9feI6tqW4kTo3hvoYAJkMh8n8D0fuISphg=="
  "resolved" "https://registry.npmjs.org/vue-demi/-/vue-demi-0.14.10.tgz"
  "version" "0.14.10"

"vue-demi@^0.13.11":
  "integrity" "sha512-IR8HoEEGM65YY3ZJYAjMlKygDQn25D5ajNFNoKh9RSDMQtlzCxtfQjdQgv9jjK+m3377SsJXY8ysq8kLCZL25A=="
  "resolved" "https://registry.npmjs.org/vue-demi/-/vue-demi-0.13.11.tgz"
  "version" "0.13.11"

"vue-demi@^0.13.8":
  "integrity" "sha512-IR8HoEEGM65YY3ZJYAjMlKygDQn25D5ajNFNoKh9RSDMQtlzCxtfQjdQgv9jjK+m3377SsJXY8ysq8kLCZL25A=="
  "resolved" "https://registry.npmjs.org/vue-demi/-/vue-demi-0.13.11.tgz"
  "version" "0.13.11"

"vue-demi@>=0.14.10":
  "integrity" "sha512-nMZBOwuzabUO0nLgIcc6rycZEebF6eeUfaiQx9+WSk8e29IbLvPU9feI6tqW4kTo3hvoYAJkMh8n8D0fuISphg=="
  "resolved" "https://registry.npmjs.org/vue-demi/-/vue-demi-0.14.10.tgz"
  "version" "0.14.10"

"vue-demi@>=0.14.5":
  "integrity" "sha512-EOG8KXDQNwkJILkx/gPcoL/7vH+hORoBaKgGe+6W7VFMvCYJfmF2dGbvgDroVnI8LU7/kTu8mbjRZGBU1z9NTA=="
  "resolved" "https://registry.npmjs.org/vue-demi/-/vue-demi-0.14.7.tgz"
  "version" "0.14.7"

"vue-easy-lightbox@^1.16.0":
  "integrity" "sha512-Dcphgybo4W6OE8x2abdNuALUUSCwrTBq1Mubjxhyva4YGS6v5KDvGuq/bc6lWwPFNGIPmy6D83Xf2Pe39vnMCA=="
  "resolved" "https://registry.npmjs.org/vue-easy-lightbox/-/vue-easy-lightbox-1.18.0.tgz"
  "version" "1.18.0"

"vue-eslint-parser@^9.4.2":
  "integrity" "sha512-Ry9oiGmCAK91HrKMtCrKFWmSFWvYkpGglCeFAIqDdr9zdXmMMpJOmUJS7WWsW7fX81h6mwHmUZCQQ1E0PkSwYQ=="
  "resolved" "https://registry.npmjs.org/vue-eslint-parser/-/vue-eslint-parser-9.4.2.tgz"
  "version" "9.4.2"
  dependencies:
    "debug" "^4.3.4"
    "eslint-scope" "^7.1.1"
    "eslint-visitor-keys" "^3.3.0"
    "espree" "^9.3.1"
    "esquery" "^1.4.0"
    "lodash" "^4.17.21"
    "semver" "^7.3.6"

"vue-flatpickr-component@^11.0.3":
  "integrity" "sha512-rhYYCfKpPHMaqTYy/jUlzg2HOlmvSMtEUJ7uB5R1gZZfxyarE5h80WAO/vtbCxxgS030KcZywIQjZG4tIgW4xg=="
  "resolved" "https://registry.npmjs.org/vue-flatpickr-component/-/vue-flatpickr-component-11.0.4.tgz"
  "version" "11.0.4"
  dependencies:
    "flatpickr" "^4.6.13"

"vue-i18n@*", "vue-i18n@9":
  "integrity" "sha512-NlZ+e8rhGSGNk/Vfh4IUvlPRjljPCRslbNYgQmYZY+sLXZgahw8fylQguZU3e8ntJDvitfe40f8p3udOiKMS0A=="
  "resolved" "https://registry.npmjs.org/vue-i18n/-/vue-i18n-9.13.0.tgz"
  "version" "9.13.0"
  dependencies:
    "@intlify/core-base" "9.13.0"
    "@intlify/shared" "9.13.0"
    "@vue/devtools-api" "^6.5.0"

"vue-router@^4.2.5":
  "integrity" "sha512-DIUpKcyg4+PTQKfFPX88UWhlagBEBEfJ5A8XDXRJLUnZOvcpMF8o/dnL90vpVkGaPbjvXazV/rC1qBKrZlFugw=="
  "resolved" "https://registry.npmjs.org/vue-router/-/vue-router-4.2.5.tgz"
  "version" "4.2.5"
  dependencies:
    "@vue/devtools-api" "^6.5.0"

"vue-select@^4.0.0-beta.3":
  "integrity" "sha512-K+zrNBSpwMPhAxYLTCl56gaMrWZGgayoWCLqe5rWwkB8aUbAUh7u6sXjIR7v4ckp2WKC7zEEUY27g6h1MRsIHw=="
  "resolved" "https://registry.npmjs.org/vue-select/-/vue-select-4.0.0-beta.6.tgz"
  "version" "4.0.0-beta.6"

"vue-star-rating@^2.1.0":
  "integrity" "sha512-LnhuQPi4hkRg7egKls+iqtL1U0RTN2gQkdLsJN7acyynMsSc9epHLzk8McJS+bR0OzfG98zNFq7mxAyuSb/okQ=="
  "resolved" "https://registry.npmjs.org/vue-star-rating/-/vue-star-rating-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "vue" "^3.0.0"

"vue@^2.0.0 || >=3.0.0", "vue@^2.6.14 || ^3.3.0", "vue@^3", "vue@^3.0.0", "vue@^3.0.0-0 || ^2.6.0", "vue@^3.0.0-0 || ^2.7.0", "vue@^3.0.11", "vue@^3.2.0", "vue@^3.2.25", "vue@^3.2.31", "vue@^3.3.4", "vue@^3.4.26", "vue@>=3.0.0", "vue@3.5.11", "vue@3.x":
  "integrity" "sha512-/8Wurrd9J3lb72FTQS7gRMNQD4nztTtKPmuDuPuhqXmmpD6+skVjAeahNpVzsuky6Sy9gy7wn8UadqPtt9SQIg=="
  "resolved" "https://registry.npmjs.org/vue/-/vue-3.5.11.tgz"
  "version" "3.5.11"
  dependencies:
    "@vue/compiler-dom" "3.5.11"
    "@vue/compiler-sfc" "3.5.11"
    "@vue/runtime-dom" "3.5.11"
    "@vue/server-renderer" "3.5.11"
    "@vue/shared" "3.5.11"

"vue3-cookies@^1.0.6":
  "integrity" "sha512-a1UvVD0qIgxyOqjlSOwnLnqAnz8ASltugEv8yX+96i/WGZAN9fEDci7xO4HIWZE1uToUnRq9JnFhvfDCSo45OA=="
  "resolved" "https://registry.npmjs.org/vue3-cookies/-/vue3-cookies-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "vue" "^3.0.0"

"vue3-dnd@^2.0.4":
  "integrity" "sha512-YuniNQDgpVc/0Ygp8RBrcPNirUa1/j0kJ0w5+mPWQ1RtgR+rGGo2tM2NyaYB+a2is2Lledv8R56P0LHkCiYydw=="
  "resolved" "https://registry.npmjs.org/vue3-dnd/-/vue3-dnd-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "@react-dnd/invariant" "^3.0.1"
    "@react-dnd/shallowequal" "^3.0.1"
    "dnd-core" "^15.1.2"
    "fast-deep-equal" "^3.1.3"
    "vue-demi" "^0.13.8"

"w3c-keyname@^2.2.0":
  "integrity" "sha512-dpojBhNsCNN7T82Tm7k26A6G9ML3NkhDsnw9n/eoxSRlVBB4CEtIQ/KTCLI2Fwf3ataSXRhYFkQi3SlnFwPvPQ=="
  "resolved" "https://registry.npmjs.org/w3c-keyname/-/w3c-keyname-2.2.8.tgz"
  "version" "2.2.8"

"webpack-sources@^3.2.3":
  "integrity" "sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w=="
  "resolved" "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.2.3.tgz"
  "version" "3.2.3"

"webpack-virtual-modules@^0.6.1":
  "integrity" "sha512-poXpCylU7ExuvZK8z+On3kX+S8o/2dQ/SVYueKA0D4WEMXROXgY8Ez50/bQEUmvoSMMrWcrJqCHuhAbsiwg7Dg=="
  "resolved" "https://registry.npmjs.org/webpack-virtual-modules/-/webpack-virtual-modules-0.6.1.tgz"
  "version" "0.6.1"

"which@^2.0.1":
  "integrity" "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA=="
  "resolved" "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"wrappy@1":
  "integrity" "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ=="
  "resolved" "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  "version" "1.0.2"

"xml-name-validator@^4.0.0":
  "integrity" "sha512-ICP2e+jsHvAj2E2lIHxa5tjXRlKDJo4IdvPvCXbXQGdzSfmSpNVyIKMvoZHjDY9DP0zV17iI85o90vRFXNccRw=="
  "resolved" "https://registry.npmjs.org/xml-name-validator/-/xml-name-validator-4.0.0.tgz"
  "version" "4.0.0"

"yallist@^4.0.0":
  "integrity" "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="
  "resolved" "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz"
  "version" "4.0.0"

"yaml-eslint-parser@^1.2.2":
  "integrity" "sha512-pEwzfsKbTrB8G3xc/sN7aw1v6A6c/pKxLAkjclnAyo5g5qOh6eL9WGu0o3cSDQZKrTNk4KL4lQSwZW+nBkANEg=="
  "resolved" "https://registry.npmjs.org/yaml-eslint-parser/-/yaml-eslint-parser-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "eslint-visitor-keys" "^3.0.0"
    "lodash" "^4.17.21"
    "yaml" "^2.0.0"

"yaml@^2.0.0":
  "integrity" "sha512-pIXzoImaqmfOrL7teGUBt/T7ZDnyeGBWyXQBvOVhLkWLN37GXv8NMLK406UY6dS51JfcQHsmcW5cJ441bHg6Lg=="
  "resolved" "https://registry.npmjs.org/yaml/-/yaml-2.4.1.tgz"
  "version" "2.4.1"

"yocto-queue@^0.1.0":
  "integrity" "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q=="
  "resolved" "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz"
  "version" "0.1.0"

"yup@^1.3.3":
  "integrity" "sha512-wPbgkJRCqIf+OHyiTBQoJiP5PFuAXaWiJK6AmYkzQAh5/c2K9hzSApBZG5wV9KoKSePF7sAxmNSvh/13YHkFDg=="
  "resolved" "https://registry.npmjs.org/yup/-/yup-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "property-expr" "^2.0.5"
    "tiny-case" "^1.0.3"
    "toposort" "^2.0.2"
    "type-fest" "^2.19.0"
