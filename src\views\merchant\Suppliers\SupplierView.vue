<script setup>
import EIcon from '@/components/Elements/EIcon.vue'
import { ref, onMounted } from 'vue'
import {  useRoute } from 'vue-router'
import { supplierService } from '@/services/supplier.service'
import { useTemplateStore } from '@/stores/template'
import useAppRouter from '@/composables/useRouter'
import { useI18n } from 'vue-i18n'

const store = useTemplateStore()

const router = useAppRouter()
const route = useRoute()
const { id } = route.params
const {t} = useI18n();
const supplier = ref()
const title = ref('View supplier')

const apiGetItem = async () => {
  try {
    store.pageLoader({ mode: 'on' })
    const response = await supplierService.get(id)
    supplier.value = response.data
    store.pageLoader({ mode: 'off' })
  } catch (error) {
    console.log(error)
    store.pageLoader({ mode: 'off' })
  }
}

onMounted(async () => {
  try {
    await apiGetItem()
  } catch (error) {
    console.error('Error fetching data:', error)
  }
})
</script>

<template>
  <BasePageHeading
    :title="title"
    :go-back="true"
    :subtitle="t('pages.suppliers.labels.label_head_list')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{ t('pages.report.labels.manages') }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <router-link to="/merchant/suppliers">{{ t('pages.report.labels.list_suppliers') }}</router-link>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <span>{{ t('pages.report.labels.view_supplier') }}</span>
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="container">
    <div class="row justify-content-center py-sm-4 py-md-6">
      <div class="col-sm-10 col-md-8">
        <form @submit.prevent="onSubmit">
          <BaseBlock title="View Supplier">
            <template #options>
              <e-icon
                @click="() => router.back()"
                name="arrow-left"
                role="button"
                class="icon_arrow_left"
              />
            </template>

            <div class="row justify-content-center py-sm-3 py-md-5">
              <div class="col-sm-10 col-md-8">
                <div class="mb-4">
                  <label class="form-label" for="form-supplier-name">{{ t('pages.suppliers.fields.name_field') }}</label>
                  <div class="text_table">
                    {{ supplier?.name }}
                  </div>
                </div>

                <div class="mb-4">
                  <label class="form-label" for="form-supplier-web-address">{{ t('pages.suppliers.fields.web_address_field') }}</label>
                  <div class="text_table">
                    {{ supplier?.website }}
                  </div>
                </div>

                <div class="mb-4">
                  <label class="form-label" for="form-supplier-telephone">{{ t('pages.suppliers.fields.telephone_field') }}</label>
                  <div class="text_table">
                    {{ supplier?.telephone }}
                  </div>
                </div>

                <div class="mb-4">
                  <label class="form-label" for="form-supplier-email">{{ t('pages.suppliers.fields.email_field') }}</label>
                  <div class="text_table">
                    {{ supplier?.email }}
                  </div>
                </div>

                <div class="row mb-2">
                  <div class="col-md-5 mb-4 me-3">
                    <label class="form-label" for="form-supplier-postal-no">{{ t('pages.suppliers.fields.postal_no_field') }}</label>
                    <div class="text_table">
                      {{ supplier?.postal_code }}
                    </div>
                  </div>

                  <div class="col-md-5 mb-4 me-3">
                    <label class="form-label" for="form-supplier-country">{{ t('pages.suppliers.fields.country_field') }}</label>
                    <div class="text_table">
                      {{ supplier?.country }}
                    </div>
                  </div>
                </div>

                <div class="mb-4">
                  <label class="form-label" for="form-supplier-place">{{ t('pages.suppliers.fields.place_field') }}</label>
                  <div class="text_table">
                    {{ supplier?.place }}
                  </div>
                </div>

                <div class="mb-4">
                  <label class="form-label" for="form-supplier-id">{{ t('pages.report.fields.description') }}</label>
                  <div class="text_table">
                    {{ supplier?.description }}
                  </div>
                </div>

                <div class="mb-4" :style="{ textAlign: 'end' }">
                  <button
                    class="btn btn-sm btn-primary"
                    :style="{ color: '#fff' }"
                    @click="() => router.pushByPath(`/${id}/update`)"
                  >
                    {{ t('buttons.update') }}
                  </button>
                </div>
              </div>
            </div>
          </BaseBlock>
        </form>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.text_table {
  background-color: rgb(244, 244, 244);
  border: 1px solid #e9dddd;
  border-radius: 4px;
  padding: 10px;
  min-height: 48px;
  align-items: center;
  display: flex;
}

.icon_arrow_left {
  background-color: rgb(243, 235, 235);
  border-radius: 100%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
