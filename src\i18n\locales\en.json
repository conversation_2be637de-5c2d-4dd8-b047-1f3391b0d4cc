{"sign_in": "Sign In", "setting_theme": {"light": "Light", "dark": "Dark", "system": "System"}, "common": {"enter": "Enter...", "select_default": "Please select"}, "buttons": {"confirm": "Confirm", "update": "Update"}, "menu": {"apps": "Apps", "manages": "Manages", "dashboard": "Dashboard", "products": "Products", "categories": "Categories", "topping": "Toppings", "topping_groups": "Topping Groups", "topping_fav_groups": "Fav Topping Groups", "tables": "Tables", "kiosks": "Kiosks", "orders": "Orders", "customers": "Customers", "printers": "Printers", "units": "Units", "producers": "Producers", "suppliers": "Suppliers", "analytics": "ANALYTICS", "reports": "Reports", "sale_report": "Sales Report", "good_report": "Goods Report", "time_report": "Time Report", "sms_report": "SMS Report", "order_report": "Order Report", "tip_report": "Tip Report", "printer_logs": "Printer Logs", "roles": "Roles", "users": "Users", "transactions": "Transactions", "opening_hours": "Opening Hours", "daily_summary": "Daily Summary"}, "pages": {"dashboard": {"name": "Dashboard", "titles": {"dagens_salg": "Today's Sales", "sales": "Sales", "earnings": "Earnings", "this_week": "This week", "weekly": "Weekly", "monthly": "Monthly", "average": "Average", "this_month": "This Month", "sms": "SMS", "recent_transactions": "Recent Transactions", "recent_orders": "Recent Orders"}, "labels": {"totale_bestillinger": "Total Orders", "sent": "<PERSON><PERSON>", "update": "Last update 10 seconds ago", "show": "Showing {limit} out of {order} orders", "no_data_transaction": "No recent transactions", "no_data_order": "No recent orders"}, "fields": {"id": "ID", "amount": "Amount", "order": "Order", "status": "Status"}}, "products": {"name": "List Products", "titles": {"dataset": "DATASET", "add": "New Product", "add_new": "Add new product", "create": "Add product", "update": "Update product"}, "labels": {"ask_stock": "How long should it be out of stock", "label_head_list": "Easily view and manage all your products in one place.", "label_head_form": "Quickly add new products to your menu with ease.", "manage": "Manages", "generally": "Generally", "constituents": "Constituents", "remove_something_label": "REMOVE SOMETHING THAT IS INCLUDED IN THE PRODUCT", "add_favorite_label": "IMPORT FROM FAVORITE GROUPS", "choose_between_label": "POSSIBLE ADDITIONAL INGREDIENTS AND OPTIONS", "category_for_extras_label": "POSSIBLE ADDITIONAL INGREDIENTS AND OPTIONS", "change_with_label": "CHANGE WITH...", "select": "Select", "by_category": "By category", "no_printer": "No Printer", "name": "Name", "takeaway_price": "Takeaway Price", "dine_price": "<PERSON><PERSON>", "topping_group": "Topping Group", "product_category": "Product Category", "unit": "Unit", "type": "Type", "title_remove_item": "Remove something that is included in the product", "title_add_favorite_item": "Import from favorite groups", "title_category_item": "Possible additional ingredients and options", "change_with": "Change with...", "max_quantity": "Max quantity", "placeholder_title": "Enter title...", "required_title": "Title is required", "description": "Description", "placeholder_description": "Enter description...", "limit_total_selected": "Limit total selected", "placeholder_limit_total_selected": "Enter limit total selected...", "arrangement": "Arrangement", "takeaway_inc_tax": "Takeaway Inc Tax", "tax_takeaway": "Tax Takeaway", "dinein_inc_tax": "Dinein Inc Tax", "tax_dine": "Tax Dine", "action": "Action", "search_to_add": "Search to add", "select_constituents": "Select constituents", "search": "Search name...", "search_topping": "Search group and category..."}, "fields": {"titles": "Title", "update_product": "UPDATE PRODUCT {name}", "id": "ID", "product": "PRODUCT", "category": "CATEGORY", "in_stock": "IN STOCK", "printer": "PRINTER", "price": "PRICE", "action": "ACTION", "name": "Product name", "category_field": "Category", "item_no": "Item no", "preparation_time": "Preparation time", "barcode": "Barcode", "description": "Description", "upload": "File Input", "supplier": "Supplier", "producer": "Producer", "printer_field": "Printer", "unit": "Unit", "weight": "Weight", "kacl": "Kacl", "in_stock_field": "In stock", "on_sale": "On Sale", "popular": "Popular", "show_in_kiosk": "Show in kiosk", "show_in_dinein": "Show in dinein", "show_in_table_kiosk": "Show in table kiosk", "show_in_takeaway": "Show in takeaway", "vat_rate": "VAT Rate", "ordinary_price": "Ordinary Price", "ordinary_takeaway_price": "Ordinary takeaway price", "offer_price": "Offer Price", "offer_takeaway_price": "Offer takeaway price", "profit_rate": "Profit rate", "purchase_price": "Purchase price", "profit": "Profit", "cost_price": "Cost price", "price_ex_vat": "Price ex VAT", "price_inc_vat": "Price inc VAT", "active_offer_price": "Active offer price", "active_takeaway_price": "Active takeaway price", "takeaway_price": "Takeaway price", "title": "Title", "arrangement": "ARRANGEMENT", "max_quanity": "MAX QUANITY", "suggested": "Suggested", "suggest": "Suggest"}, "buttons": {"remove_something": "Remove Something", "add_favor": "Add from Favorites", "choose_between": "Choose <PERSON>", "category_for_extras": "Category For Extras", "change_with": "Change With", "search_to_add": "Search to add", "cancel": "Cancel", "confirm": "Confirm", "confirm_add": "Confirm & New", "upload": "Drop files here to upload"}}, "categories": {"name": "Categories", "titles": {"list": "List Categories", "create": "Add new category", "add": "Add category", "update": "Update category", "detail": "Update category {item}"}, "labels": {"manages": "Manages", "list_label": "View and manage all your product categories efficiently.", "form_label": "Easily create and add new categories to organize your products.", "dataset": "DATASET", "btn_add": "New category", "btn_confirm": "Confirm", "search": "Search ..."}, "fields": {"id": "ID", "priority": "PRIORITY", "category": "CATEGORY", "printer": "PRINTER", "action": "ACTION", "name": "Category name", "priority_field": "Priority", "printer_label": "Printer", "description_field": "Description", "minimal_view_field": "Minimalistic View"}, "placeholder": {"enter_name": "Enter Name...", "enter_priority": "Enter Priority...", "select_printer": "Open this select menu"}}, "toppings": {"name": "Toppings", "titles": {"list": "List Toppings", "create": "Add new topping", "update": "Update topping", "detail": "Update Topping {name}", "add": "Add Topping"}, "labels": {"label_head_list": "View and manage all your available toppings effortlessly.", "label_head_form": "Quickly add new toppings to enhance your menu options.", "new_topping": "New topping", "manages": "Manages", "btn_new": "New topping"}, "fields": {"id": "ID", "name": "Name", "topping_group": "Topping Group", "in_stock": "In Stock", "in_kiosk": "In Kiosk", "unit": "Unit", "suppliers": "Suppliers", "producer": "Producer", "topping_name": "Topping Name", "item_no": "Item No", "barcode": "Barcode", "description": "Description", "supplier": "Supplier", "purchase_price": "Purchase price", "vat_rate": "VAT Rate", "price": "Price", "show_in_kiosk": "Show in kiosk", "show_in_table_kiosk": "Show in table kiosk", "show_in_takeaway": "Show in takeaway", "confirm": "Confirm", "action": "Action"}, "placeholder": {"enter_name": "Enter Name...", "enter_item_no": "Enter your item-no..", "enter_description": "Textarea content"}}, "topping_group": {"name": "Topping Groups", "titles": {"list": "List topping groups", "create": "Add new topping group", "add": "New topping group", "update": "Update topping group", "detail": "Update topping group {item}", "view": "View topping group"}, "labels": {"confirm": "Confirm", "label_head_list": "Organize and manage your topping groups for better customization.", "label_head_form": "Easily create and add new topping groups to streamline your menu.", "add_topping_group": "ADD TOPPING GROUP", "manages": "Manages", "btn_confirm": "Confirm"}, "fields": {"id": "ID", "name": "NAME", "action": "ACTION", "name_field": "Name", "store_id": "Store Id"}, "placeholder": {"enter_name": "Enter Your Name..."}}, "fav_topping_group": {"name": "Fav Topping Groups", "titles": {"list": "List fav topping groups", "create": "Add new fav topping group", "add": "New fav topping group", "update": "Update fav topping group", "detail": "Update fav topping group {item}", "view": "View fav topping group"}, "labels": {"confirm": "Confirm", "label_head_list": "Organize and manage your fav topping groups for better customization.", "label_head_form": "Easily create and add new fav topping groups to streamline your menu.", "add_topping_group": "ADD TOPPING GROUP", "manages": "Manages", "btn_confirm": "Confirm"}, "fields": {"id": "ID", "name": "NAME", "action": "ACTION", "name_field": "Name", "type": "Type"}, "placeholder": {"enter_name": "Enter Your Name..."}, "types": {"remove_something": "Remove something", "category_extra": "Category extra", "change_with_at": "Change with at"}}, "tables": {"name": "Tables", "titles": {"list": "List tables", "add": "New Table", "create": "Add new table", "update": "Update table", "detail": "Update table {item}", "view": "View Table"}, "labels": {"label_head_list": "View and manage all your restaurant tables efficiently.", "label_head_form": "Quickly add new tables to your restaurant layout.", "manages": "Manages"}, "fields": {"id": "ID", "table_id": "Table Id", "table_name": "Table Name", "qr_code": "QR Code", "store_id": "Store Id", "action": "Action"}, "placeholder": {"enter_name": "Enter Your Name..."}}, "kiosk": {"name": "Kiosk", "titles": {"list": "List Kiosk", "add": "New kiosk", "create": "Add new kiosk", "update": "Update kiosk", "detail": "Update kiosk {item}", "view": "View kiosk"}, "labels": {"label_head_list": "Custom functionality to further enrich your tables.", "manages": "Manages", "btn_add_printer": "Add Printer"}, "fields": {"id": "ID", "kiosk_id": "KIOSK ID", "name": "NAME", "reception_printer": "RECEPTION PRINTER", "action": "ACTION", "kiosk_name": "Kiosk Name", "kiosk_id_field": "Kiosk ID", "receipt_printer": "Receipt Printer", "type": "Type", "dine_in": "<PERSON>e in", "takeaway": "Takeaway", "dine_in_takeaway": "Dine in & Takeaway"}, "placeholder": {"enter_kiosk_name": "Enter kiosk name...", "enter_kiosk_id": "Enter id kiosk..."}}, "order": {"name": "Orders", "titles": {"list": "List Orders", "show": "View Order", "detail": "Order #{item}"}, "labels": {"label_head_list": "Easily view and manage all customer orders in one place.", "search": "Search...", "select_user": "Select user...", "items": "Items", "item_subtotal": "Item <PERSON>", "mva_25": "MVA 25%", "mva": "MVA {item}%", "order_total": "Order Total", "customer_receipts": "Customer Receipts", "kitchen_receipts": "Kitchen Receipts", "bambora_receipts": "Capture Receipts", "transaction": "Transaction", "information": "Information", "manages": "Manages", "name": "Name", "email": "Email", "phone": "Phone", "table_no": "Table No.", "payment_capture": "Payment Captured", "refunded": "Refunded", "refund": "Refund", "status": "Status", "completed": "Completed", "uncompleted": "Uncompleted", "no_table_selected": "No table selected", "note": "Note", "type": "Type", "type_order": "Type Order", "order_date": "Order Date", "tip": "Tip", "asap": "Asap", "schedule": "Schedule", "marketing_accepted": "Marketing Accepted", "failure_reason": "Failure Reason", "go_to_original_order": "Go to Original Order", "go_to_refund_order": "Go to Refund Order"}, "fields": {"id": "id", "order": "ORDER", "customer": "CUSTOMER", "order_nr": "ORDER NR", "queue_nr": "QUEUE NR", "status": "STATUS", "created_at": "CREATED AT", "table_no": "TABLE NO.", "items": "ITEMS", "total": "TOTALS", "product": "Product", "unit_price": "Unit Price", "tax": "Tax", "qty": "Qty", "price": "Price", "order_table": "Order", "receipt_uuid": "Receipt UUID", "create_at": "Create At", "action": "Action", "view": "View", "payment_method": "Payment Method", "amount": "Amount", "date": "Date", "status_table": "Status", "marketing_accepted": "Marketing Accepted"}}, "customer": {"name": "Customers", "titles": {"list": "List Customers", "detail": "DETAIL CUSTOMER"}, "labels": {"manages": "Manages", "label_head_list": "View and manage all your customers' information efficiently."}, "fields": {"id": "Id", "name": "Name", "phone": "Phone", "email": "Email", "sid": "SID", "orders": "Orders", "customer": "CUSTOMER", "queue_nr": "QUEUE NR", "status": "STATUS", "create_at": "CREATED AT", "table_no": "TABLE NO.", "items": "ITEMS", "totals": "TOTALS", "marketing_accepted": "MARKETING ACCEPTED"}}, "printer": {"name": "Printer", "titles": {"list": "List Printers", "add": "New printer", "create": "Add new printer", "update": "Update printer", "detail": "UPDATE PRINTER {item}", "view": "View Printer"}, "labels": {"label_head_list": "Custom functionality to further enrich your tables.", "confirm": "Confirm", "manages": "Manages"}, "fields": {"id": "Id", "printer_id": "PRINTER ID", "printer": "PRINTER", "user_name": "USER NAME", "type": "TYPE", "action": "ACTION", "printer_name": "Printer Name", "printer_id_field": "Printer ID", "user_name_field": "UserName", "access_key": "Access Key", "type_field": "Type"}, "placeholder": {"enter_print_name": "Enter printer name...", "enter_print_id": "Enter id printer...", "enter_print_username": "Enter username printer", "enter_access_key": "Enter access key"}}, "unit": {"name": "Units", "titles": {"list": "List Units", "create": "Add new unit", "update": "Update Unit", "add": "Add Unit", "show": "View Unit", "detail": "Update Unit {item}"}, "labels": {"label_head_list": "View and manage all measurement units for your products.", "label_head_form": "Easily add new measurement units to your inventory system.", "btn_new_unit": "New unit", "btn_confirm": "Confirm", "btn_update": "Update", "manages": "Manages"}, "fields": {"id": "ID", "name": "NAME", "action": "ACTION", "name_field": "Name", "store_id": "Store Id"}, "placeholder": {"enter_name": "Enter Your Name..."}}, "producers": {"name": "Producers", "titles": {"list": "List Producers", "create": "Add new producer", "add": "ADD PRODUCER", "show": "View Producer", "new": "New Producer", "update": "Update Producer", "detail": "UPDATE PRODUCER {item}"}, "labels": {"label_head_list": "View and manage all your product producers efficiently.", "label_head_form": "Quickly add new producers to your database.", "btn_new": "New producer", "btn_confirm": "Confirm", "btn_update": "Update", "manages": "Manages"}, "fields": {"id": "ID", "name": "NAME", "action": "ACTION", "name_field": "Name", "store_id": "STORE ID", "web_address": "WEB ADDRESS", "description": "DESCRIPTION", "description_field": "Description", "web_address_field": "Web Address", "store_id_field": "Store Id"}, "placeholder": {"enter_name": "Enter Your Name...", "enter_web_address": "Enter your web address..", "enter_content": "Textarea content.."}, "validate": {"require_name": "Please enter name producer", "require_web_address": "Please enter web address producer", "require_content": "Please enter description"}}, "suppliers": {"name": "Suppliers", "titles": {"list": "Producers", "create": "Add new supplier", "add": "ADD SUPPLIER", "show": "View Supplier", "update": "Update supplier", "detail": "UPDATE SUPPLIER {item}"}, "labels": {"label_head_list": "View and manage all your suppliers seamlessly.", "label_head_form": "Easily add new suppliers to streamline your procurement process.", "btn_new": "New supplier", "btn_confirm": "Confirm", "btn_update": "Update", "search": "Search..."}, "fields": {"id": "ID", "name": "NAME", "action": "ACTION", "web_address": "WEB ADDRESS", "description": "DESCRIPTION", "telephone": "TELEPHONE", "email": "EMAIL", "postal_no": "POSTAL NO", "place": "PLACE", "country": "COUNTRY", "web_address_field": "Web Address", "description_field": "Description", "name_field": "Name", "telephone_field": "Telephone", "email_field": "Email", "customer_number": "Customer number", "account": "Account", "postal": "Postal", "place_field": "Place", "country_field": "Country", "payment_information": "Payment Information", "action_field": "Action", "address_field": "Address", "payment_field": "Payment information", "postal_no_field": "Postal no"}, "placeholder": {"telephone": "Enter telephone..", "postal": "postal no", "place": "place", "country": "country", "description": "Textarea content..", "payment_info": "Payment information"}, "validate": {"require_name": "Please enter name", "require_address": "Please enter address", "require_web_address": "Please enter web address", "require_telephone": "Please enter telephone", "require_email": "Please enter email", "require_customer_number": "Please enter customer number", "require_account": "Please enter customer number", "require_postal": "Please enter postal_code", "require_place": "Please enter place", "require_country": "Please enter country", "require_description": "Please enter description", "require_payment_info": "Please enter payment information"}}, "report": {"titles": {"sale_report": "Sales Report", "goods_report": "Goods Report", "time_report": "Time Report", "sms_report": "SMS Report", "tip_report": "Tips Report"}, "labels": {"label_head_list_sales": "Generate comprehensive reports on your sales performance and trends.", "label_head_list_good": "Access detailed reports on your inventory and product status.", "label_head_list_time": "View and analyze reports on operational times and efficiency.", "label_head_list_sms": "Get insights and analytics on your SMS communications and campaigns.", "label_head_list_order": "Review detailed reports on all customer orders and their statuses.", "chart": "Charts", "btn_export": "Export CSV", "btn_pdf": "Export PDF", "manages": "Manages", "list_suppliers": "List suppliers", "view_supplier": "View supplier", "this_week": "This Week", "select_kiosk": "Select kiosk", "select_table": "Select table"}, "fields": {"chart": "Charts", "period": "PERIOD", "period_field": "Period", "description": "Description", "total_orders": "Total Orders", "no_of_items": "Total Item", "no_of_sales_ex_vat": "Sales Ex VAT", "sales_inc_vat": "Sales inc. VAT", "total": "Total", "product": "Product", "type": "Type", "total_sales": "Total Sales", "unit_price": "Avarage price including VAT", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "sms_sent": "SMS Sent", "date": "Date", "payment_method": "Payment Method", "source": "Source", "amount": "Amount", "tips": "Tips"}, "placeholder": {"date_range": "Select Date Range", "seach": "Search..."}}, "printer_logs": {"name": "PrinterLogs", "titles": {"list": "List printer logs"}, "fields": {"id": "ID", "job_id": "Job Id", "order_id": "Order Id", "created_at": "Created At", "printer": "Printer"}, "placeholder": {"search": "Search..."}}, "roles": {"name": "Roles", "titles": {"list": "List Roles", "add": "New Role", "create": "Add Role", "update": "Update Role", "detail": "Update Role {item}", "add_new": "Add new role"}, "labels": {"label_head_list": "Custom functionality to further enrich your roles.", "btn_confirm": "Confirm", "manage": "Manages", "name": "Name"}, "fields": {"id": "ID", "name": "Name", "permission": "Permission", "action": "Action"}, "placeholder": {"search": "Search...", "name": "Enter name..", "permission": "Permissions is required"}, "validate": {"require_name": "Please enter name"}}, "user": {"name": "Users", "titles": {"list": "List users", "add": "New user", "create": "Add new user", "update": "Update user", "detail": "Update user {item}", "new_password": "New Password", "confirm_new_password": "Confirm New Password", "view_user": "View user"}, "labels": {"label_head_list": "View and manage all users who have access to your system.", "label_head_form": "Easily add new users to your system with appropriate permissions.", "btn_confirm": "Confirm", "btn_change_password": "Change Password", "manage": "Manages"}, "fields": {"id": "Id", "name": "Name", "role": "Role", "email": "Email", "action": "Action", "user_name": "User name", "password": "Password"}, "placeholder": {"seach": "Search...", "select": "Select...", "new_password": "New password ...", "confirm_password": "Confirm new password ...", "name": "Enter your name..", "email": "Email...", "password": "Password..."}, "validate": {"require_new_password": "Please provide a new password", "require_confirm_password": "Passwords do not match.", "require_name": "Please enter name", "require_role": "Please enter roles", "require_email": "Please enter a valid email address", "require_password": "Please provide a password"}}, "transaction": {"name": "Transactions", "titles": {"list": "List Transactions"}, "label": {"manage": "Manages", "label_head_list": "View and manage all financial transactions within your system."}, "fields": {"id": "Id", "payment_ref": "Payment Ref", "method": "Method", "ammount": "Ammount", "sid": "SID", "order": "Order", "status": "Status"}, "placeholder": {"search": "Search..."}}, "daily_summary": {"name": "Daily Summary", "titles": {"list": "List Daily Summary"}, "label": {"manage": "Manages", "label_head_list": "View and manage all financial daily summary within your system."}, "fields": {"date": "Date", "payment_method": "Payment Method", "total_order": "Total Orders", "no_of_items": "No. of Items", "net_sales": "Net Sales (NOK)", "vat_15%": "VAT 15% (NOK)", "vat_25%": "VAT 25% (NOK)", "sales_vat": "Sales Inc. VAT (NOK)", "total_vips": "Total Vipps", "total_cash": "Total Cash", "total_stripe": "Total Stripe", "total_terminal": "Total Terminal"}, "placeholder": {"search": "Search..."}}, "opening_hours": {"name": "Opening Hours", "titles": {"list": "List Opening Hours"}, "labels": {"label_head_list": "Set and manage the opening hours of your restaurant.", "btn_config": "Ordinary Opening Hours", "btn_cancel": "Cancel", "btn_confirm": "Confirm", "btn_reset": "Reset", "manage": "Manages", "list": "List openingHours", "action": "Action"}, "fields": {"date": "Date", "day": "Day", "open_at": "Open At", "close_at": "Close At", "closed": "Closed", "action": "Action"}}, "settings": {"name": "Settings", "titles": {"list": "List of Settings"}, "fields": {"id": "ID", "created_at": "Created At", "key": "Key", "value in": "Value In", "value out": "Value Out"}}, "footer": {"previous": "Previous", "next": "Next"}, "placeholder": {"search": "Search...", "empty-list": "List empty, please create new one", "source": "Source", "enter": "Enter name..", "enter_order": "Enter order..", "ative_rush_mode": "Activate Rush Mode", "confirm": "Confirm", "cancel": "Cancel", "hide-during-rush-mode": "Hide during rush mode", "rush-mode": "Rush mode", "takeaway-kiosk": "Takeaway & Kiosk"}}}