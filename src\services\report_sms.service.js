import { http } from "./Base/base.service";

export const reportSMSService = {
  async getList(query) {
    return await http.get("/report/sms", {
      params: query,
    });
  },
  async export(query) {
    return await http.get("/report/export-csv-sms", {
      params: query,
    });
  },

  async exportPDF(query) {
    return await http.get("/report/export-pdf-sms", {
      params: query,
      responseType: "blob"
    });
  }
};
