import { http } from "./Base/base.service";

export const reportTimeService = {
  async getList(query) {
    return await http.get("/report/time", {
      params: query,
    });
  },

  async export(query) {
    return await http.get("/report/export-csv-time", {
      params: query,
    });
  },

  async exportPDF(query) {
    return await http.get("/report/export-pdf-time", {
      params: query,
      responseType: "blob"
    });
  }
};
