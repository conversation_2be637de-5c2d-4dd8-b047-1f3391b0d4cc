<script setup>
import { reactive, onMounted, ref } from 'vue'
import {
  Dataset,
  DatasetItem,
  DatasetInfo,
  DatasetPager,
  DatasetSearch,
  DatasetShow
} from 'vue-dataset'
import EListEmpty from '@/components/Elements/EListEmpty.vue'
import { useTemplateStore } from '@/stores/template'
import EButton from '@/components/Elements/EButton.vue'
import {roleService} from "@/services/role.service";
import Swal from "sweetalert2";
import useAppRouter from '@/composables/useRouter'
import { useI18n } from 'vue-i18n'

let toast = Swal.mixin({
  buttonsStyling: false,
  target: "#page-container",
  customClass: {
    confirmButton: "btn btn-success m-1",
    cancelButton: "btn btn-danger m-1",
    input: "form-control",
  },
});

const store = useTemplateStore()

const router = useAppRouter()
const {t} = useI18n()
// Helper variables
const cols = reactive([
  {
    name: t('pages.roles.fields.id'),
    field: 'id',
    sort: ''
  },
  {
    name: t('pages.roles.fields.name'),
    field: 'name',
    sort: ''
  },
  {
    name: t('pages.roles.fields.permission'),
    field: 'permission',
    sort: ''
  },
])

const listRoles = ref([])

const onFetchList = async () => {
  try {
    store.pageLoader({ mode: 'on' })
    const response = await roleService.getList()
    if (!response?.error) {
      listRoles.value = response?.data || []
    }
    store.pageLoader({ mode: 'off' })
  } catch (error) {
    console.log(error)
    store.pageLoader({ mode: 'off' })
  }
}

// Apply a few Bootstrap 5 optimizations
onMounted(async () => {
  // Remove labels from
  document.querySelectorAll('#datasetLength label').forEach((el) => {
    el.remove()
  })

  // Replace select classes
  let selectLength = document.querySelector('#datasetLength select')

  if (selectLength) {
    selectLength.classList = ''
    selectLength.classList.add('form-select')
    selectLength.style.width = '80px'
  }

  await onFetchList()
})

const onOpenDeleteConfirm = (id) => {
  toast
      .fire({
        title: "Are you sure?",
        text: "You will not be able to recover this role!",
        icon: "warning",
        showCancelButton: true,
        customClass: {
          confirmButton: "btn btn-danger m-1",
          cancelButton: "btn btn-info m-1",
        },
        confirmButtonText: "Yes, delete!",
        html: false,
        preConfirm: () => {
          return roleService.delete(id)
        },
      })
      .then((result) => {
        if (result.value && !result.value?.error) {
          toast.fire(
              "Deleted!",
              "Role has been deleted.",
              "success"
          );
          onFetchList()
        } else if (result.dismiss === "cancel") {
          toast.fire("Cancelled", "Role is safe", "error");
        }
      });
}
</script>

<template>
  <BasePageHeading
    :title="t('pages.roles.name')"
    :subtitle="t('pages.roles.labels.label_head_list')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{ t('pages.roles.labels.manage') }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">{{ t('pages.roles.titles.list') }}</li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <BaseBlock :title="t('pages.roles.name')">
      <!-- <template #options>
        <e-button
          type="info"
          size="sm"
          @click="() => router.pushByName({ name: 'merchant-roles-form' })"
          ><i class="fa fa-plus opacity-50 me-1"></i>{{ t('pages.roles.titles.add') }}</e-button
        >
      </template> -->
      <Dataset
        v-slot="{ ds }"
        :ds-data="listRoles"
        :ds-search-in="[
          'name',
        ]"
      >
        <div class="row" :data-page-count="ds.dsPagecount">
          <div id="datasetLength" class="col-md-8 py-2">
            <DatasetShow />
          </div>
          <div class="col-md-4 py-2">
            <DatasetSearch :ds-search-placeholder="t('pages.roles.placeholder.search')" />
          </div>
        </div>
        <hr />
        <div class="row" v-if="listRoles?.length">
          <div class="col-md-12">
            <div class="table-responsive">
              <table class="table table-striped mb-0">
                <thead>
                <tr>
                  <th
                      v-for="th in cols"
                      :key="th.field"
                  >
                    {{ th.name }}
                  </th>
                  <!-- <th class="text-end" scope="col">{{ t('pages.roles.fields.action') }}</th> -->
                </tr>
                </thead>
                <DatasetItem tag="tbody" class="fs-sm">
                  <template #default="{ row }">
                    <tr>
                      <td style="min-width: 50px">
                        {{ row.id }}
                      </td>
                      <td>{{ row.name }}</td>
                      <td>{{ row.permissions?.map(item => item.name)?.join(', ') }}</td>
                      <!-- <td class="text-end">
                        <div class="btn-group">
                          <button type="button" class="btn btn-sm btn-alt-secondary">
                            <i
                                class="fa fa-fw fa-pencil-alt"
                                @click="() => router.pushByPath(`/roles/form/${row.id}`)"
                            ></i>
                          </button>
                          <button
                              type="button"
                              class="btn btn-sm btn-alt-secondary"
                              data-bs-toggle="modal"
                              data-bs-target="#modal-delete"
                              @click="onOpenDeleteConfirm(row.id)"
                          >
                            <i class="fa fa-fw fa-times"></i>
                          </button>
                        </div>
                      </td> -->
                    </tr>
                  </template>
                </DatasetItem>
              </table>
            </div>
          </div>
        </div>
        <EListEmpty v-else />
        <div class="d-flex flex-md-row flex-column justify-content-between align-items-center">
          <DatasetInfo class="py-3 fs-sm" />
          <DatasetPager class="flex-wrap py-3 fs-sm" />
        </div>
      </Dataset>
    </BaseBlock>
  </div>
</template>

<style lang="scss" scoped>
.gg-select {
  box-sizing: border-box;
  position: relative;
  display: block;
  transform: scale(1);
  width: 22px;
  height: 22px;
}
.gg-select::after,
.gg-select::before {
  content: '';
  display: block;
  box-sizing: border-box;
  position: absolute;
  width: 8px;
  height: 8px;
  left: 7px;
  transform: rotate(-45deg);
}
.gg-select::before {
  border-left: 2px solid;
  border-bottom: 2px solid;
  bottom: 4px;
  opacity: 0.3;
}
.gg-select::after {
  border-right: 2px solid;
  border-top: 2px solid;
  top: 4px;
  opacity: 0.3;
}
th.sort {
  cursor: pointer;
  user-select: none;
  &.asc {
    .gg-select::after {
      opacity: 1;
    }
  }
  &.desc {
    .gg-select::before {
      opacity: 1;
    }
  }
}
</style>
