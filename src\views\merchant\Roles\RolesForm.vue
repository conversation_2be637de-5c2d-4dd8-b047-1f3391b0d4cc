<script setup>
import EIcon from '@/components/Elements/EIcon.vue'
import {  useRoute } from 'vue-router'
import { computed, ref, onMounted } from 'vue'
import useVuelidate from '@vuelidate/core'
import {required} from '@vuelidate/validators'
import { useTemplateStore } from '@/stores/template'
import {roleService} from "@/services/role.service";
import {permissionService} from "@/services/permission.service";
import useAppRouter from '@/composables/useRouter'
import { useI18n } from 'vue-i18n'

const store = useTemplateStore()

const route = useRoute()
const router = useAppRouter()

const id = route.params?.id
const {t}  = useI18n()
const roleDetail = ref()

let formData = ref({
  name: null,
  permissions: []
})

const rules = computed(() => {
  return {
    name: {required},
    permissions: {required}
  }
})

let v$ = useVuelidate(rules, formData.value)

async function onSubmit() {
  try {
    const result = await v$.value.$validate()
    if (!result) return
    store.pageLoader({ mode: 'on' })
    const response = id ? await roleService.update(id, formData.value) : await roleService.create(formData.value)
    if (!response?.error) {
      await router.pushByPath('/merchant/roles')
    }
  } catch (e) {
    console.log(e)
    store.pageLoader({ mode: 'off' })
  }
}

const fetchRoleDetail = async () => {
  try {
    store.pageLoader({ mode: 'on' })
    const response = await roleService.get(id)
    if (!response?.error) {
      roleDetail.value = response.data
      formData.value.name = roleDetail.value?.name
      formData.value.permissions = roleDetail.value?.permissions?.map(item => item?.name)
    }
    v$ = useVuelidate(rules, formData.value)
    store.pageLoader({ mode: 'off' })
  } catch (error) {
    console.log(error)
    store.pageLoader({ mode: 'off' })
  }
}

const listPermissions = ref([])
const fetchListPermissions = async () => {
  try {
    const response = await permissionService.getList()
    if (!response?.error) {
      listPermissions.value = response.data || []
    }
  } catch (error) {
    console.log(error)
  }
}

onMounted(async () => {
  try {
    await fetchListPermissions()
    if (id) {
      await fetchRoleDetail()
    }
  } catch (error) {
    console.error('Error fetching data:', error)
  }
})
</script>

<template>
  <BasePageHeading
    :title="id ? t('pages.roles.titles.update') : t('pages.roles.titles.add_new')"
    :go-back="true"
    subtitle="Custom functionality to further enrich your suppliers."
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{ t('pages.roles.labels.manage') }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <router-link to="/merchant/roles">{{ t('pages.roles.titles.list') }}</router-link>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <span>{{ id ? t('pages.roles.titles.update') : t('pages.roles.titles.add_new') }}</span>
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <div class="row justify-content-start">
      <div>
        <form @submit.prevent="onSubmit">
          <BaseBlock :title="id ? `${t('pages.roles.titles.update')} ${roleDetail?.name}` : t('pages.roles.titles.create')">
            <template #options>
              <e-icon
                @click="() => router.back()"
                name="arrow-left"
                role="button"
                class="icon_arrow_left"
              />
            </template>

            <div class="row justify-content-center py-sm-1 py-md-2">
              <div>
                <div class="row mb-2">
                  <div class="col-12 mb-4">
                    <label class="form-label" for="form-name"
                      >{{ t('pages.roles.labels.name') }}<span class="text-danger">*</span></label
                    >
                    <input
                      type="text"
                      class="form-control"
                      id="form-name"
                      :placeholder="t('pages.roles.placeholder.name')"
                      :class="{
                        'is-invalid': v$.name.$errors.length
                      }"
                      v-model="formData.name"
                      @blur="v$.name.$touch"
                    />
                    <div v-if="v$.name.$errors.length" class="invalid-feedback animated fadeIn">
                      {{ t('pages.roles.validate.require_name') }}
                    </div>
                  </div>

                  <div class="col-12 mb-4">
                    <label class="form-label" for="form-permissions"
                    >{{ t('pages.roles.fields.permission') }}<span class="text-danger">*</span></label
                    >
                    <div class="d-flex align-items-center justify-content-start gap-4">
                      <div class="form-check" v-for="per in listPermissions" :key="per.id">
                        <input
                            class="form-check-input"
                            type="checkbox"
                            :value="per.name"
                            :id="per.id"
                            v-model="formData.permissions"
                            @change="v$.permissions.$touch"
                            :class="{
                              'is-invalid': v$.permissions.$errors.length
                            }"
                        />
                        <label class="form-check-label fs-sm" :for="per.id">{{per.name}}</label>
                      </div>
                    </div>
                    <div v-if="v$.permissions.$errors.length" class="invalid-feedback animated fadeIn">
                      {{ t('pages.roles.placeholder.permission') }}
                    </div>
                  </div>
                </div>

                <div class="mb-4" :style="{ textAlign: 'end' }">
                  <button type="submit" class="btn btn-sm btn-primary" :style="{ color: '#fff' }">
                    {{ t('buttons.confirm') }}
                  </button>
                </div>
              </div>
            </div>
          </BaseBlock>
        </form>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.icon_arrow_left {
  background-color: rgb(243, 235, 235);
  border-radius: 100%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
