<script setup>
import EIcon from "@/components/Elements/EIcon.vue";
import EButton from "@/components/Elements/EButton.vue";
import { ref, onMounted, computed, onBeforeUnmount } from "vue";
import useVuelidate from "@vuelidate/core";
import { minLength, required } from "@vuelidate/validators";
import { producerService } from "@/services/producer.service";
import { categoryService } from "@/services/category.service";
import { supplierService } from "@/services/supplier.service";
import { favToppingGroupService } from "@/services/favToppingGroup.service";
import ProductsDndConstituentRemoveItem from "@/views/merchant/Products/Components/ProductsDndConstituentRemoveItem.vue";
// import ProductsDndConstituentChooseItem
//   from "@/views/merchant/Products/Components/ProductsDndConstituentChooseItem.vue";
import { printerService } from "@/services/printer.service";
import { useTemplateStore } from "@/stores/template";
import ProductsDndConstituentCategoryItem from "@/views/merchant/Products/Components/ProductsDndConstituentCategoryItem.vue";
import ProductsDndConstituentChangeItem from "@/views/merchant/Products/Components/ProductsDndConstituentChangeItem.vue";
import useNotify from "@/composables/useNotify";
import { unitService } from "@/services/unit.service";
import useAuth from "@/composables/useAuth";
import { useI18n } from "vue-i18n";
import useAppRouter from "@/composables/useRouter";
import { useRoute } from "vue-router";
import { restaurantService } from "@/services/restaurant.service";
import { storeData } from "@/stores/storeData";
import { scrollTo } from "@/stores/scollItemInlist";

// import Dropzone from "dropzone";

const store = useTemplateStore();
const scrollStore = scrollTo();
const dataFetch = storeData();
const { setNotify } = useNotify();
const router = useRoute();
const route = useAppRouter();
const { t } = useI18n();
const id = router.params?.id;
const title = ref(t("pages.fav_topping_group.titles.create"));
const productDetail = ref();
const detail = ref([]);
const dropzone = ref(null);
// const mockFile = ref(null);
const isSubmitForm = ref(false);
const typeSubmit = ref();
onMounted(async () => {
  store.pageLoader({ mode: "on" });
  if (router.query?.id) {
    await onFetchProductDetail(router.query?.id);
  }
  if (id) {
    title.value = t("pages.fav_topping_group.titles.update");
    await onFetchProductDetail(id);
  }
  await Promise.all([
    onFetchListCategories(),
    onFetchListSuppliers(),
    onFetchListProducers(),
    onFetchListPrinters(),
    onFetchListUnit(),
    onFetchRestaurantDetail(),
  ]);
  store.pageLoader({ mode: "off" });

  // Dropzone
  // dropzone.value = new Dropzone("#dropzoneForm", {
  //   url: "https://httpbin.org/post",
  //   maxFiles: 1,
  //   acceptedFiles: "image/*",
  //   addRemoveLinks: true,
  //   dictDefaultMessage: `${t("pages.products.buttons.upload")}`,
  //   init: function () {
  //     let myDropzone = this;

  //     if (formData.value.thumbnail) {
  //       mockFile.value = { name: formData.value.name, size: 12345 };
  //       let callback = null; // Optional callback when it's done
  //       let crossOrigin = null; // Added to the `img` tag for crossOrigin handling
  //       let resizeThumbnail = false; // Tells Dropzone whether it should resize the image first
  //       myDropzone.displayExistingFile(
  //         mockFile.value,
  //         formData.value.thumbnail,
  //         callback,
  //         crossOrigin,
  //         resizeThumbnail
  //       );
  //     }
  //   },
  // });
  // dropzone.value.on("addedfile", (file) => {
  //   if (mockFile.value) {
  //     dropzone.value.removeFile(mockFile.value);
  //     mockFile.value = null;
  //   }
  //   if (
  //     typeof formData.value.thumbnail === "object" &&
  //     formData.value.thumbnail !== null
  //   ) {
  //     dropzone.value.removeFile(formData.value.thumbnail);
  //   }
  //   formData.value.thumbnail = file;
  // });
  // dropzone.value.on("removedfile", () => {
  //   formData.value.thumbnail = null;
  // });
});

onBeforeUnmount(() => {
  if (dropzone.value !== null) {
    dropzone.value.destroy();
  }
});

const optionsUnit = ref([]);
const onFetchListUnit = async () => {
  const response = await unitService.getList();
  if (!response?.error) {
    const newArray = response.data?.data.map((item) => ({
      value: item.id,
      text: item.name,
    }));
    optionsUnit.value = [{ value: null, text: "Please select" }, ...newArray];
  }
};
const initialValue = ref({
  title: "",
  description: "",
  type: "",
  customFoods: [],
});
const formData = ref({ ...initialValue.value });

const isCheckItem = ref(false);
const isCheckRequried = (value) => {
  if (value?.length) {
    const item = value?.find((itm) => itm?.title === "");
    if (item) {
      isCheckItem.value = true;
    }
    return true;
  }
  return true;
};
const rules = computed(() => {
  const allRules = {
    name: {
      required,
      minLength: minLength(3),
    },
    category_id: {
      required,
    },
    purchase_price: {},
    ordinary_profit: {},
    offer_profit: {},
    takeaway_purchase_price: {},
    takeaway_ordinary_profit: {},
    takeaway_offer_profit: {},
    customFoods: {
      isCheckRequried,
    },
  };
  if (formData.value.is_takeaway_price) {
    return allRules;
  } else {
    let _tmp = Object.assign({}, allRules);
    delete _tmp.takeaway_purchase_price;
    delete _tmp.takeaway_ordinary_profit;
    delete _tmp.takeaway_offer_profit;
    return _tmp;
  }
});
let v$ = useVuelidate(rules, formData);

const listCategories = ref([]);
const onFetchListCategories = async () => {
  const response = await categoryService.getList({
    limit: -1,
  });
  if (!response.error) {
    listCategories.value = response?.data?.data || [];
  }
};
const listSuppliers = ref([]);
const onFetchListSuppliers = async () => {
  const response = await supplierService.getList();
  if (!response.error) {
    listSuppliers.value = response?.data?.data || [];
  }
};
const listProducers = ref([]);
const onFetchListProducers = async () => {
  const response = await producerService.getList();
  if (!response.error) {
    listProducers.value = response?.data?.data || [];
  }
};
const listPrinters = ref([]);
const onFetchListPrinters = async () => {
  const response = await printerService.getList();
  if (!response.error) {
    listPrinters.value = response?.data?.data || [];
  }
};

// async function handleSubmitAndAdd(){
//   console.log("1")
// };

async function onSubmit() {
  isSubmitForm.value = true;
  // const result = await v$.value.$validate();
  // if (!result) {
  //   return;
  // }
  try {
    const isCheckRequire = formData.value.customFoods?.find(
      (itm) => itm?.title === ""
    );
    if (isCheckRequire) {
      return;
    }
    const payload = new FormData();
    payload.append("title", formData.value.customFoods[0].title);
    payload.append(
      "description",
      formData.value.customFoods[0].description || ""
    );
    payload.append(
      "limit_total",
      formData.value.customFoods[0].limit_total || ""
    );
    payload.append("type", formData.value.customFoods[0].custom_type);
    console.log("formData.value.customFoods", formData.value.customFoods);

    const _customFoods =
      formData.value.customFoods[0].selectedItem?.map((item) => {
        return {
          constituentable_id: item?.id,
          constituentable_source: item?.type,
          takeaway_inc_vat_price:
            item?.takeaway_inc_vat_price === 0
              ? "0"
              : item?.takeaway_inc_vat_price || null,
          takeaway_vat_rate: item?.takeaway_vat_rate,
          vat_rate: item?.vat_rate,
          dinein_inc_vat_price:
            item?.dinein_inc_vat_price === 0
              ? "0"
              : item?.dinein_inc_vat_price || null,
          max_quantity:
            formData.value.customFoods[0].custom_type === 3
              ? item?.max_quantity
              : "",
          type:
            formData.value.customFoods[0].custom_type === 4 ? item?._type : "",
        };
      }) || [];
    console.log(_customFoods);
    payload.append("detail", JSON.stringify(_customFoods));
    if (id) {
      payload.append("_method", "PATCH");
    }
    const response = id
      ? await favToppingGroupService.update(id, payload)
      : await favToppingGroupService.create(payload);
    if (!response?.error) {
      if (typeSubmit.value === "confirm") {
        scrollStore.getElement(id);
        dataFetch.setData([], "merchant-fav-topping-groups-list");
        dataFetch.setTotal(0, "merchant-fav-topping-groups-list");
        return await route.pushByName({
          name: "merchant-fav-topping-groups-list",
        });
      } else {
        formData.value = { ...initialValue.value };
        dropzone.value.destroy();
        v$.value.$reset();
      }
    }
    store.pageLoader({ mode: "off" });
    if (typeSubmit.value === "confirm") {
      v$.value.$reset();
    }
    setNotify({
      title: "Success",
      message: response?.message,
      type: "success",
    });
  } catch (e) {
    setNotify({
      title: "Error",
      message: e?.message,
    });
  }
}

const previewThumb = ref();

const onAddCustomItem = (type) => {
  formData.value.customFoods = [
    {
      title: "",
      description: "",
      limit_total: "",
      custom_type: type,
      selectedItem: [],
    },
  ];
};

const onRemoveCustomFood = (index) => {
  formData.value.customFoods.splice(index, 1);
};

const onFetchProductDetail = async (id) => {
  try {
    const response = await favToppingGroupService.getDetail(id);
    if (!response.error) {
      productDetail.value = response?.data || [];
      if (productDetail.value) {
        detail.value.push({
          title: productDetail.value.title,
          description: productDetail.value.description,
          limit_total: productDetail.value.limit_total,
          custom_type: productDetail.value.type,
          selectedItem: productDetail.value.detail || [],
        });
      }
      formData.value = {
        ...productDetail.value,
        title: productDetail.value?.title || "",
        created_at: productDetail.value?.created_at || "",
        description: productDetail.value?.description || "",
        limit_total: productDetail.value?.limit_total || "",
        id: productDetail.value?.id || "",
        store_id: productDetail.value?.store_id || "",
        type: productDetail.value?.type || "",
        updated_at: productDetail.value?.updated_at || "",
      };
      if (detail.value?.length) {
        formData.value.customFoods = detail.value?.map((item) => {
          return {
            title: item?.title,
            description: item?.description,
            limit_total: item?.limit_total,
            custom_type: item?.custom_type,
            selectedItem: item?.selectedItem?.map((cons) => ({
              id: cons?.constituentable_id,
              constituentable_source: cons?.type,
              name: cons?.name,
              is_takeaway_price: cons?.is_takeaway_price,
              takeaway_inc_vat_price:
                cons?.takeaway_inc_vat_price === 0
                  ? 0
                  : cons?.takeaway_inc_vat_price ||
                    cons?.takeaway_ordinary_price,
              takeaway_inc_vat_price_v2:
                cons?.takeaway_inc_vat_price === 0
                  ? 0
                  : cons?.takeaway_inc_vat_price ||
                    cons?.takeaway_ordinary_price,
              dinein_inc_vat_price:
                cons?.dinein_inc_vat_price === 0
                  ? 0
                  : cons?.dinein_inc_vat_price || cons?.price,
              takeaway_vat_rate: cons?.takeaway_vat_rate,
              vat_rate: cons?.vat_rate,
              price: cons?.price,
              max_quantity: cons?.max_quantity,
              type: cons?.constituentable_source,
              _type: cons?.type,
              unit: {
                name: cons?.unit,
              },
            })),
          };
        });
      }

      console.log("formData.value.customFoods", formData.value.customFoods);

      v$ = useVuelidate(rules, formData);
    } else {
      productDetail.value = null;
    }
    console.log("formData", formData);
    console.log("productDetail", productDetail);
  } catch (e) {
    console.log(e);
  }
};

const listVatRate = [
  {
    label: "0%",
    value: 0,
  },
  {
    label: "15%",
    value: 15,
  },
  {
    label: "25%",
    value: 25,
  },
];
const purchasePriceOrdinary = computed(
  () =>
    (formData?.value?.purchase_price || 0) +
    (formData?.value?.ordinary_profit || 0)
);
const profitRateOrdinary = computed(() =>
  (
    (formData?.value?.ordinary_profit / (purchasePriceOrdinary.value || 1)) *
    100
  ).toFixed(0)
);
const vatOrdinary = computed(
  () => (purchasePriceOrdinary.value * formData?.value?.vat_rate) / 100
);
const purchasePriceVatOrdinary = computed(
  () => purchasePriceOrdinary?.value + vatOrdinary?.value
);

const purchasePriceOffer = computed(
  () =>
    (formData?.value?.purchase_price || 0) +
    (formData?.value?.offer_profit || 0)
);
const profitRateOffer = computed(() =>
  (
    (formData?.value?.offer_profit / (purchasePriceOffer.value || 1)) *
    100
  ).toFixed(0)
);
const vatOffer = computed(
  () => (purchasePriceOffer.value * formData?.value?.vat_rate) / 100
);
const purchasePriceVatOffer = computed(
  () => purchasePriceOffer?.value + vatOffer?.value
);

const purchasePriceOrdinaryTakeaway = computed(
  () =>
    (formData?.value?.takeaway_purchase_price || 0) +
    (formData?.value?.takeaway_ordinary_profit || 0)
);
const profitRateOrdinaryTakeaway = computed(() =>
  (
    (formData?.value?.takeaway_ordinary_profit /
      (purchasePriceOrdinaryTakeaway.value || 1)) *
    100
  ).toFixed(0)
);
const vatOrdinaryTakeaway = computed(
  () =>
    (purchasePriceOrdinaryTakeaway.value * formData?.value?.takeaway_vat_rate) /
    100
);
const purchasePriceVatOrdinaryTakeaway = computed(
  () => purchasePriceOrdinaryTakeaway?.value + vatOrdinaryTakeaway?.value
);

const purchasePriceOfferTakeaway = computed(
  () =>
    (formData?.value?.takeaway_purchase_price || 0) +
    (formData?.value?.takeaway_offer_profit || 0)
);
const profitRateOfferTakeaway = computed(() =>
  (
    (formData?.value?.takeaway_offer_profit /
      (purchasePriceOfferTakeaway.value || 1)) *
    100
  ).toFixed(0)
);
const vatOfferTakeaway = computed(
  () =>
    (purchasePriceOfferTakeaway.value * formData?.value?.takeaway_vat_rate) /
    100
);
const purchasePriceVatOfferTakeaway = computed(
  () => purchasePriceOfferTakeaway?.value + vatOfferTakeaway?.value
);

const onSwapCustomFood = (index, step) => {
  if (index === formData.value.customFoods?.length - 1 && step === 1) return;
  [
    formData.value.customFoods[index],
    formData.value.customFoods[index + step],
  ] = [
    formData.value.customFoods[index + step],
    formData.value.customFoods[index],
  ];
};

const { userInfo, userPlanId } = useAuth();
const listPlans = ref([]);
const onFetchRestaurantDetail = async () => {
  const response = await restaurantService.getDetail(userInfo.value.store_id);
  if (!response.error) {
    listPlans.value = response?.data.plans || [];
  }
};

const userPlansCustomFood = computed(() =>
  listPlans.value?.find((item) => item.id === userPlanId.CUSTOM_FOOD)
); // 1: custom foods

function isDecimal(num) {
  return num % 1 !== 0;
}
const parseNumber = (input) => {
  if (!input || Number(input) === 0) return null;
  return isDecimal
    ? parseFloat(((input * 100) / 100).toFixed(2))
    : Number(input);
};

// const roundToTwoDecimalPlaces = (number) => {
//   if (Number.isFinite(number) && !Number.isInteger(number)) {
//     return Number(number.toFixed(2));
//   } else {
//     return number;
//   }
// }

const handleSubmit = (type) => {
  typeSubmit.value = type;
};

const handleInput = (value, type) => {
  const parseVal = parseNumber(value);
  switch (type) {
    case "ordinary_profit": {
      const newPriceExVat =
        (formData.value.purchase_price || 0) + (parseVal || 0);
      const vat = (newPriceExVat * formData.value.vat_rate) / 100;
      const newPriceIncVat = newPriceExVat + vat;
      if (formData.value.ordinary_price_ex_vat !== parseNumber(newPriceExVat)) {
        formData.value.ordinary_price_ex_vat = parseNumber(newPriceExVat) || 0;
      }
      if (formData.value.ordinary_price !== parseNumber(newPriceIncVat)) {
        formData.value.ordinary_price = parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "ordinary_price_ex_vat": {
      const newProfit = (parseVal || 0) - (formData.value?.purchase_price || 0);
      const vat = (parseVal * formData?.value?.vat_rate) / 100;

      const newPriceIncVat = parseVal + vat;
      if (formData.value.ordinary_profit !== newProfit) {
        formData.value.ordinary_profit =
          parseNumber(newProfit) >= 0 ? newProfit : 0;
      }
      if (formData.value.ordinary_price !== parseNumber(newPriceIncVat)) {
        formData.value.ordinary_price = parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "ordinary_price": {
      const vat = formData?.value?.vat_rate
        ? parseVal - parseVal / (1 + formData?.value?.vat_rate / 100)
        : 0;
      const newOrdinaryPriceExVat = parseVal - vat;
      const newOrdinaryProfit =
        newOrdinaryPriceExVat - (formData.value.purchase_price || 0);
      if (
        formData.value.ordinary_price_ex_vat !==
        parseNumber(newOrdinaryPriceExVat)
      ) {
        formData.value.ordinary_price_ex_vat =
          parseNumber(newOrdinaryPriceExVat) || 0;
      }
      if (formData.value.ordinary_profit !== parseNumber(newOrdinaryProfit)) {
        formData.value.ordinary_profit =
          parseNumber(newOrdinaryProfit) >= 0 &&
          parseNumber(newOrdinaryProfit) !== null
            ? parseNumber(newOrdinaryProfit)
            : 0;
      }
      break;
    }
    case "offer_profit": {
      const newPriceExVat =
        (formData.value.purchase_price || 0) + (parseVal || 0);
      const vat = (newPriceExVat * formData.value.vat_rate) / 100;
      const newPriceIncVat = newPriceExVat + vat;
      if (formData.value.offer_price_ex_vat !== parseNumber(newPriceExVat)) {
        formData.value.offer_price_ex_vat = parseNumber(newPriceExVat) || 0;
      }
      if (formData.value.offer_price !== parseNumber(newPriceIncVat)) {
        formData.value.offer_price = parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "offer_price_ex_vat": {
      const newProfit = (parseVal || 0) - (formData.value?.purchase_price || 0);
      const vat = (parseVal * formData?.value?.vat_rate) / 100;
      const newPriceIncVat = parseVal + vat;
      if (formData.value.offer_profit !== parseNumber(newProfit)) {
        formData.value.offer_profit =
          parseNumber(newProfit) >= 0 && parseNumber(newProfit) !== null
            ? parseNumber(newProfit)
            : 0;
      }
      if (formData.value.offer_price !== parseNumber(newPriceIncVat)) {
        formData.value.offer_price = parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "offer_price": {
      const vat = formData?.value?.vat_rate
        ? parseVal - parseVal / (1 + formData?.value?.vat_rate / 100)
        : 0;
      const newOrdinaryPriceExVat = parseVal - vat;
      const newOrdinaryProfit =
        newOrdinaryPriceExVat - (formData.value.purchase_price || 0);
      if (
        formData.value.offer_price_ex_vat !== parseNumber(newOrdinaryPriceExVat)
      ) {
        formData.value.offer_price_ex_vat =
          parseNumber(newOrdinaryPriceExVat) || 0;
      }
      if (formData.value.offer_profit !== parseNumber(newOrdinaryProfit)) {
        formData.value.offer_profit =
          parseNumber(newOrdinaryProfit) >= 0 &&
          parseNumber(newOrdinaryProfit) !== null
            ? parseNumber(newOrdinaryProfit)
            : 0;
      }
      break;
    }
  }
};

const handleInputTakeAway = (value, type) => {
  const parseVal = parseNumber(value);
  switch (type) {
    case "takeaway_ordinary_profit": {
      const newPriceExVat =
        (formData.value.takeaway_purchase_price || 0) + (parseVal || 0);
      const vat = (newPriceExVat * formData.value.takeaway_vat_rate) / 100;
      const newPriceIncVat = newPriceExVat + vat;
      if (formData.value.takeaway_price_ex_vat !== parseNumber(newPriceExVat)) {
        formData.value.takeaway_price_ex_vat = parseNumber(newPriceExVat) || 0;
      }
      if (
        formData.value.takeaway_ordinary_price !== parseNumber(newPriceIncVat)
      ) {
        formData.value.takeaway_ordinary_price =
          parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "takeaway_price_ex_vat": {
      const newProfit =
        (parseVal || 0) - (formData.value?.takeaway_purchase_price || 0);
      const vat = (parseVal * formData?.value?.takeaway_vat_rate) / 100;
      const newPriceIncVat = parseVal + vat;
      if (formData.value.takeaway_ordinary_profit !== newProfit) {
        formData.value.takeaway_ordinary_profit =
          parseNumber(newProfit) >= 0 && parseNumber(newProfit) !== null
            ? parseNumber(newProfit)
            : 0;
      }
      if (
        formData.value.takeaway_ordinary_price !== parseNumber(newPriceIncVat)
      ) {
        formData.value.takeaway_ordinary_price =
          parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "takeaway_ordinary_price": {
      const vat = formData?.value?.takeaway_vat_rate
        ? parseVal - parseVal / (1 + formData?.value?.takeaway_vat_rate / 100)
        : 0;
      const newOrdinaryPriceExVat = parseVal - vat;
      const newOrdinaryProfit =
        newOrdinaryPriceExVat - (formData.value.takeaway_purchase_price || 0);
      if (
        formData.value.takeaway_price_ex_vat !==
        parseNumber(newOrdinaryPriceExVat)
      ) {
        formData.value.takeaway_price_ex_vat =
          parseNumber(newOrdinaryPriceExVat) || 0;
      }
      if (
        formData.value.takeaway_ordinary_profit !==
        parseNumber(newOrdinaryProfit)
      ) {
        formData.value.takeaway_ordinary_profit =
          parseNumber(newOrdinaryProfit) >= 0 &&
          parseNumber(newOrdinaryProfit) !== null
            ? parseNumber(newOrdinaryProfit)
            : 0;
      }
      break;
    }
    case "takeaway_offer_profit": {
      const newPriceExVat =
        (formData.value.takeaway_purchase_price || 0) + (parseVal || 0);
      const vat = (newPriceExVat * formData.value.takeaway_vat_rate) / 100;
      const newPriceIncVat = newPriceExVat + vat;
      if (formData.value.takeaway_offer_ex_vat !== parseNumber(newPriceExVat)) {
        formData.value.takeaway_offer_ex_vat = parseNumber(newPriceExVat) || 0;
      }
      if (formData.value.takeaway_offer_price !== parseNumber(newPriceIncVat)) {
        formData.value.takeaway_offer_price = parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "takeaway_offer_ex_vat": {
      const newProfit =
        (parseVal || 0) - (formData.value?.takeaway_purchase_price || 0);
      const vat = (parseVal * formData?.value?.takeaway_vat_rate) / 100;
      const newPriceIncVat = parseVal + vat;
      if (formData.value.takeaway_offer_profit !== newProfit) {
        formData.value.takeaway_offer_profit =
          parseNumber(newProfit) >= 0 && parseNumber(newProfit) !== null
            ? parseNumber(newProfit)
            : 0;
      }
      if (formData.value.takeaway_offer_price !== parseNumber(newPriceIncVat)) {
        formData.value.takeaway_offer_price = parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "takeaway_offer_price": {
      const vat = formData?.value?.takeaway_vat_rate
        ? parseVal - parseVal / (1 + formData?.value?.takeaway_vat_rate / 100)
        : 0;
      const newOrdinaryPriceExVat = parseVal - vat;
      const newOrdinaryProfit =
        newOrdinaryPriceExVat - (formData.value.takeaway_purchase_price || 0);
      if (
        formData.value.takeaway_offer_ex_vat !==
        parseNumber(newOrdinaryPriceExVat)
      ) {
        formData.value.takeaway_offer_ex_vat =
          parseNumber(newOrdinaryPriceExVat) || 0;
      }
      if (
        formData.value.takeaway_offer_profit !== parseNumber(newOrdinaryProfit)
      ) {
        formData.value.takeaway_offer_profit =
          parseNumber(newOrdinaryProfit) >= 0 &&
          parseNumber(newOrdinaryProfit) !== null
            ? parseNumber(newOrdinaryProfit)
            : 0;
      }
      break;
    }
  }
};

const handleInputIsTakeAway = (checked) => {
  formData.value.is_takeaway_price = checked;
  if (checked) {
    if (
      formData.value.is_takeaway_price &&
      !formData.value.takeaway_purchase_price
    ) {
      formData.value.takeaway_purchase_price = formData.value.purchase_price;
    }
    if (
      formData.value.is_takeaway_price &&
      !parseNumber(formData.value.takeaway_ordinary_price)
    ) {
      const parseVal = parseNumber(formData.value.ordinary_price);
      formData.value.takeaway_ordinary_price = parseVal;
      const vat = formData?.value?.takeaway_vat_rate
        ? parseVal - parseVal / (1 + formData?.value?.takeaway_vat_rate / 100)
        : 0;
      const newOrdinaryPriceExVat = parseVal - vat;
      const newOrdinaryProfit =
        newOrdinaryPriceExVat - (formData.value.takeaway_purchase_price || 0);
      if (
        formData.value.takeaway_price_ex_vat !==
        parseNumber(newOrdinaryPriceExVat)
      ) {
        formData.value.takeaway_price_ex_vat =
          parseNumber(newOrdinaryPriceExVat) || 0;
      }
      if (
        formData.value.takeaway_ordinary_profit !==
        parseNumber(newOrdinaryProfit)
      ) {
        formData.value.takeaway_ordinary_profit =
          parseNumber(newOrdinaryProfit) >= 0
            ? parseNumber(newOrdinaryProfit)
            : 0;
      }
    }
    if (
      formData.value.is_takeaway_price &&
      !parseNumber(formData.value.takeaway_offer_price)
    ) {
      const parseVal = parseNumber(formData.value.offer_price);
      formData.value.takeaway_offer_price = parseVal;
      const vat = formData?.value?.takeaway_vat_rate
        ? parseVal - parseVal / (1 + formData?.value?.takeaway_vat_rate / 100)
        : 0;
      const newOrdinaryPriceExVat = parseVal - vat;
      const newOrdinaryProfit =
        newOrdinaryPriceExVat - (formData.value.takeaway_purchase_price || 0);
      if (
        formData.value.takeaway_offer_ex_vat !==
        parseNumber(newOrdinaryPriceExVat)
      ) {
        formData.value.takeaway_offer_ex_vat =
          parseNumber(newOrdinaryPriceExVat) || 0;
      }
      if (
        formData.value.takeaway_offer_profit !== parseNumber(newOrdinaryProfit)
      ) {
        formData.value.takeaway_offer_profit =
          parseNumber(newOrdinaryProfit) >= 0
            ? parseNumber(newOrdinaryProfit)
            : 0;
      }
    }
  }
};
</script>

<template>
  <BasePageHeading
    :title="title"
    :go-back="true"
    :subtitle="t('pages.fav_topping_group.labels.label_head_form')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.products.labels.manage")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page" @click="route.back()">
            <a href="javascript:void(0)" class="link-fx">{{
              t("pages.fav_topping_group.name")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <span>{{
              id
                ? t("pages.fav_topping_group.titles.update")
                : t("pages.fav_topping_group.titles.create")
            }}</span>
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <div class="row">
      <div class="col-sm-12 col-md-12">
        <form @submit.prevent="onSubmit">
          <BaseBlock
            :title="
              id
                ? t(`pages.fav_topping_group.titles.update`, {
                    name: productDetail?.title,
                  })
                : t('pages.fav_topping_group.titles.create')
            "
          >
            <template #options>
              <e-icon
                @click="
                  () => {
                    route.back();
                    dataFetch.setData([], 'merchant-fav-topping-groups-list');
                    dataFetch.setTotal(0, 'merchant-fav-topping-groups-list');
                  }
                "
                name="arrow-left"
                role="button"
                class="icon_arrow_left"
              />
            </template>

            <div class="block-content tab-content">
              <div>
                <div class="overflow-x-auto">
                  <div
                    class="d-flex align-center justify-content-start space-x-3"
                    style="width: max-content"
                  >
                    <e-button
                      type="danger"
                      size="sm"
                      @click="onAddCustomItem(1)"
                    >
                      {{ t("pages.products.buttons.remove_something") }}
                    </e-button>
                    <!--                    <e-button type="secondary" size="sm" @click="onAddCustomItem(2)">-->
                    <!--                      {{ t('pages.products.buttons.choose_between') }}-->
                    <!--                    </e-button>-->
                    <e-button type="info" size="sm" @click="onAddCustomItem(3)">
                      {{ t("pages.products.buttons.category_for_extras") }}
                    </e-button>
                    <e-button
                      type="warning"
                      size="sm"
                      @click="onAddCustomItem(4)"
                    >
                      {{ t("pages.products.buttons.change_with") }}
                    </e-button>
                  </div>
                </div>
                <hr />
                <template
                  v-for="(item, index) in formData.customFoods"
                  :key="index"
                >
                  <products-dnd-constituent-remove-item
                    :isSubmit="isSubmitForm"
                    :indexing="index"
                    v-if="item.custom_type === 1"
                    v-model:title="item.title"
                    v-model:description="item.description"
                    v-model:items="item.selectedItem"
                    @remove="onRemoveCustomFood"
                    @move="onSwapCustomFood"
                  />
                  <!--                  <products-dnd-constituent-choose-item :indexing="index" v-if="item.custom_type === 2" v-model:title="item.title" v-model:description="item.description" v-model:items="item.selectedItem" @remove="onRemoveCustomFood" @move="onSwapCustomFood" />-->
                  <products-dnd-constituent-category-item
                    :isSubmit="isSubmitForm"
                    :indexing="index"
                    v-if="item.custom_type === 3"
                    v-model:title="item.title"
                    v-model:description="item.description"
                    v-model:limit_total="item.limit_total"
                    v-model:items="item.selectedItem"
                    @remove="onRemoveCustomFood"
                    @move="onSwapCustomFood"
                  />
                  <products-dnd-constituent-change-item
                    :isSubmit="isSubmitForm"
                    :indexing="index"
                    v-if="item.custom_type === 4"
                    v-model:title="item.title"
                    v-model:description="item.description"
                    v-model:items="item.selectedItem"
                    @remove="onRemoveCustomFood"
                    @move="onSwapCustomFood"
                  />
                </template>
              </div>
            </div>
            <div
              class="my-4"
              :style="{
                textAlign: 'end',
                display: 'flex',
                gap: '5px',
                justifyContent: 'end',
              }"
            >
              <button
                type="submit"
                class="btn btn-sm btn-primary"
                @click="handleSubmit('confirm')"
                :style="{ color: '#fff' }"
              >
                {{ t("pages.products.buttons.confirm") }}
              </button>
              <!-- <button
                v-if="!id"
                type="submit"
                class="btn btn-sm btn-primary"
                @click="handleSubmit('confirm_add')"
                :style="{ color: '#fff' }"
              >
                {{ t("pages.products.buttons.confirm_add") }}
              </button> -->
            </div>
          </BaseBlock>
        </form>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
@import "dropzone/dist/dropzone.css";
@import "@/assets/scss/vendor/dropzone";
.block-content {
  padding-inline: 10px;
}

.icon_arrow_left {
  background-color: rgb(243, 235, 235);
  border-radius: 100%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.dz-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>
