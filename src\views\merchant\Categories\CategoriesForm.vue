<script setup>
import EIcon from "@/components/Elements/EIcon.vue";
import { useRoute } from "vue-router";
import { reactive, computed, ref, onMounted } from "vue";
import useVuelidate from "@vuelidate/core";
import { required } from "@vuelidate/validators";
import { categoryService } from "@/services/category.service";
import { printerService } from "@/services/printer.service";
import { useTemplateStore } from "@/stores/template";
import useNotify from "@/composables/useNotify";
import useAppRouter from "@/composables/useRouter";
import { storeData } from "@/stores/storeData";
import { useI18n } from "vue-i18n";
import { scrollTo } from "@/stores/scollItemInlist";

const { t } = useI18n();
const route = useRoute();
const router = useAppRouter();
const store = useTemplateStore();
const { setNotify } = useNotify();
const id = route.params?.id;
const typeSubmit = ref();
const dataFetch = storeData();
const scrollStore = scrollTo();

const category = ref();
const errorMessage = ref();

let state = reactive({
  name: null,
  description: null,
  printer_id: null,
  minimal_view: false,
});

const rules = computed(() => {
  return {
    name: {
      required,
    },
    printer_id: {
      required,
    },
  };
});

let v$ = useVuelidate(rules, state);

async function onSubmit() {
  const result = await v$.value.$validate();

  if (!result) {
    return;
  }

  const payload = {
    name: state.name,
    type: "product",
    printer_id: state.printer_id === "no_printer" ? "" : state.printer_id,
    minimal_view: state.minimal_view,
  };

  if (state.description) payload.description = state.description;

  try {
    store.pageLoader({ mode: "on" });
    const response = id
      ? await categoryService.edit(id, payload)
      : await categoryService.create(payload);
    if (!response?.error) {
      if (typeSubmit.value === "confirm") {
        scrollStore.getElement(id);
        dataFetch.setData([], "merchant-categories-list");
        dataFetch.setTotal(0, "merchant-categories-list");
        await router.pushByPath("/categories");
      } else {
        state.name = null;
        state.description = null;
        state.printer_id = null;
        state.minimal_view = false;
        v$ = useVuelidate(rules, state);
      }
      setNotify({
        title: "Success",
        message: id ? "Category update success" : "Category create success",
        type: "success",
      });
    }
  } catch (e) {
    console.log(e);
    setNotify({
      title: "Error",
      message: e?.message,
    });
  } finally {
    store.pageLoader({ mode: "off" });
  }

  // perform async actions
}

const listPrinters = ref([]);
const onFetchListPrinters = async () => {
  try {
    const response = await printerService.getList();
    if (!response?.error) {
      listPrinters.value = response?.data?.data || [];
    }
  } catch (e) {
    console.log(e);
  }
};

const onFetchCategoryDetail = async (id) => {
  const response = await categoryService.getDetail(id);
  if (!response.error) {
    category.value = response?.data || null;
    state.name = category.value?.name || null;
    state.minimal_view = category.value?.minimal_view;
    state.description = category.value?.description || null;
    state.printer_id =
      (category.value?.printer === null
        ? "no_printer"
        : category.value?.printer?.id) || null;
    v$ = useVuelidate(rules, state);
  } else {
    category.value = null;
  }
};

onMounted(async () => {
  try {
    store.pageLoader({ mode: "on" });
    await onFetchListPrinters();
    if (id) {
      await onFetchCategoryDetail(id);
    }
  } catch (error) {
    console.error("Error fetching data:", error);
  } finally {
    store.pageLoader({ mode: "off" });
  }
});

const handleSubmit = (type) => {
  typeSubmit.value = type;
};
</script>

<template>
  <BasePageHeading
    :title="
      id
        ? t('pages.categories.titles.update')
        : t('pages.categories.titles.create')
    "
    :go-back="true"
    :subtitle="t('pages.categories.labels.form_label')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.categories.labels.manages")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <router-link to="/merchant/categories">{{
              t("pages.categories.titles.list")
            }}</router-link>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <span>{{
              id
                ? t("pages.categories.titles.update")
                : t("pages.categories.titles.create")
            }}</span>
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <div class="row justify-content-center">
      <div class="col-sm-12 col-md-8">
        <form @submit.prevent="onSubmit">
          <BaseBlock
            :title="
              id
                ? t('pages.categories.titles.detail', { item: category?.name })
                : t('pages.categories.titles.add')
            "
          >
            <template #options>
              <e-icon
                @click="
                  () => {
                    router.back();
                    dataFetch.setData([], 'merchant-categories-list');
                    dataFetch.setTotal(0, 'merchant-categories-list');
                  }
                "
                name="arrow-left"
                role="button"
                class="icon_arrow_left"
              />
            </template>

            <div class="row justify-content-center py-sm-3 py-md-5">
              <div class="col-sm-10 col-md-8">
                <div class="mb-4">
                  <label class="form-label" for="block-form-name"
                    >{{ t("pages.categories.fields.name")
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    type="text"
                    class="form-control"
                    id="block-form-name"
                    name="block-form-name"
                    :placeholder="t('pages.categories.placeholder.enter_name')"
                    :class="{
                      'is-invalid': v$.name.$errors.length,
                    }"
                    v-model="state.name"
                    @blur="v$.name.$touch"
                  />
                  <div
                    v-if="v$.name.$errors.length"
                    class="invalid-feedback animated fadeIn"
                  >
                    Please enter name
                  </div>
                </div>

                <div class="mb-4">
                  <label class="form-label" for="block-printer_id"
                    >{{ t("pages.categories.fields.printer_label")
                    }}<span class="text-danger">*</span></label
                  >
                  <select
                    class="form-select"
                    id="block-printer_id"
                    v-model="state.printer_id"
                    :class="{
                      'is-invalid': v$.printer_id.$errors.length,
                    }"
                    @blur="v$.printer_id.$touch"
                  >
                    <option selected disabled :value="null">
                      {{ t("pages.categories.placeholder.select_printer") }}
                    </option>
                    <option value="no_printer">
                      {{ t("pages.products.labels.no_printer") }}
                    </option>
                    <option
                      v-for="prt in listPrinters"
                      :key="prt?.id"
                      :value="prt?.id"
                    >
                      {{ prt?.name }}
                    </option>
                  </select>
                  <div
                    v-if="v$.printer_id.$errors.length"
                    class="invalid-feedback animated fadeIn"
                  >
                    Please enter printer
                  </div>
                </div>

                <div class="mb-4">
                  <label class="form-label" for="block-form-description">{{
                    t("pages.categories.fields.description_field")
                  }}</label>
                  <input
                    type="text"
                    class="form-control"
                    id="block-form-description"
                    placeholder="Enter description"
                    v-model="state.description"
                  />
                </div>

                <div class="mb-4">
                  <label class="form-label" for="block-form-description">{{
                    t("pages.categories.fields.minimal_view_field")
                  }}</label>
                  <div class="form-check form-switch">
                    <input
                      :style="{ opacity: 1 }"
                      class="form-check-input"
                      type="checkbox"
                      v-model="state.minimal_view"
                      :checked="state.minimal_view"
                    />
                  </div>
                </div>

                <div
                  class="my-4"
                  :style="{
                    textAlign: 'end',
                    display: 'flex',
                    gap: '5px',
                    justifyContent: 'end',
                  }"
                >
                  <button
                    type="submit"
                    class="btn btn-sm btn-primary"
                    @click="handleSubmit('confirm')"
                    :style="{ color: '#fff' }"
                  >
                    {{ t("pages.categories.labels.btn_confirm") }}
                  </button>
                  <button
                    v-if="!id"
                    type="submit"
                    class="btn btn-sm btn-primary"
                    @click="handleSubmit('confirm_add')"
                    :style="{ color: '#fff' }"
                  >
                    {{ t("pages.products.buttons.confirm_add") }}
                  </button>
                </div>
              </div>
            </div>
          </BaseBlock>
        </form>
      </div>
    </div>

    <div
      id="toast_alert"
      class="toast fade hide"
      data-delay="4000"
      role="alert"
      aria-live="assertive"
      aria-atomic="true"
    >
      <div class="toast-header">
        <i class="si si-bubble text-primary me-2"></i>
        <strong class="me-auto">Title</strong>
        <small class="text-muted">just now</small>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="toast"
          aria-label="Close"
        ></button>
      </div>
      <div class="toast-body">
        {{ errorMessage }}
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.icon_arrow_left {
  background-color: rgb(243, 235, 235);
  border-radius: 100%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
import { setLabels } from 'vue-chartjs/dist/utils'
