<script setup>
import EIcon from "@/components/Elements/EIcon.vue";
import EButton from "@/components/Elements/EButton.vue";
import { ref, onMounted, computed, onBeforeUnmount } from "vue";
import useVuelidate from "@vuelidate/core";
import { minLength, required } from "@vuelidate/validators";
import { producerService } from "@/services/producer.service";
import { categoryService } from "@/services/category.service";
import { supplierService } from "@/services/supplier.service";
import { useDebounceFn } from "@vueuse/core";
import { scrollTo } from "@/stores/scollItemInlist";
import { productService } from "@/services/product.service";
import ProductsDndConstituentRemoveItem from "@/views/merchant/Products/Components/ProductsDndConstituentRemoveItem.vue";
// import ProductsDndConstituentChooseItem
//   from "@/views/merchant/Products/Components/ProductsDndConstituentChooseItem.vue";
import { printerService } from "@/services/printer.service";
import { useTemplateStore } from "@/stores/template";
import ProductsDndConstituentCategoryItem from "@/views/merchant/Products/Components/ProductsDndConstituentCategoryItem.vue";
import ProductsDndConstituentChangeItem from "@/views/merchant/Products/Components/ProductsDndConstituentChangeItem.vue";
import useNotify from "@/composables/useNotify";
import { unitService } from "@/services/unit.service";
import useAuth from "@/composables/useAuth";
import { useI18n } from "vue-i18n";
import useAppRouter from "@/composables/useRouter";
import { useRoute } from "vue-router";
import { restaurantService } from "@/services/restaurant.service";
import { common } from "@/stores/storeCommon";
import { storeData } from "@/stores/storeData";

import { sortBy } from "lodash";
import EModal from "@/components/Elements/EModal.vue";
import { Dataset, DatasetInfo, DatasetItem, DatasetPager } from "vue-dataset";

import Dropzone from "dropzone";

const store = useTemplateStore();
const dataFetch = storeData();
const scrollStore = scrollTo();
const { setNotify } = useNotify();
const router = useRoute();
const route = useAppRouter();
const { t } = useI18n();
const id = router.params?.id;
const title = ref(t("pages.products.titles.add_new"));
const productDetail = ref();
const dropzone = ref(null);
const mockFile = ref(null);
const commonStore = common();
const isSubmitForm = ref(false);
const typeSubmit = ref();
const visibleModal = ref(false);
onMounted(async () => {
  store.pageLoader({ mode: "on" });
  if (router.query?.id) {
    await onFetchProductDetail(router.query?.id);
  }
  if (id) {
    title.value = t("pages.products.titles.update");
    await onFetchProductDetail(id);
  }
  await Promise.all([
    onFetchListCategories(),
    onFetchListSuppliers(),
    onFetchListProducers(),
    onFetchListPrinters(),
    onFetchListUnit(),
    onFetchRestaurantDetail(),
  ]);
  store.pageLoader({ mode: "off" });

  // Dropzone
  dropzone.value = new Dropzone("#dropzoneForm", {
    url: "https://httpbin.org/post",
    maxFiles: 1,
    acceptedFiles: "image/*",
    addRemoveLinks: true,
    dictDefaultMessage: `${t("pages.products.buttons.upload")}`,
    init: function () {
      let myDropzone = this;

      if (formData.value.thumbnail) {
        mockFile.value = { name: formData.value.name, size: 12345 };
        let callback = null; // Optional callback when it's done
        let crossOrigin = null; // Added to the `img` tag for crossOrigin handling
        let resizeThumbnail = false; // Tells Dropzone whether it should resize the image first
        myDropzone.displayExistingFile(
          mockFile.value,
          formData.value.thumbnail,
          callback,
          crossOrigin,
          resizeThumbnail
        );
      }
    },
  });
  dropzone.value.on("addedfile", (file) => {
    if (mockFile.value) {
      dropzone.value.removeFile(mockFile.value);
      mockFile.value = null;
    }
    if (
      typeof formData.value.thumbnail === "object" &&
      formData.value.thumbnail !== null
    ) {
      dropzone.value.removeFile(formData.value.thumbnail);
    }
    formData.value.thumbnail = file;
  });
  dropzone.value.on("removedfile", () => {
    formData.value.thumbnail = null;
  });
});

onBeforeUnmount(() => {
  dropzone.value.destroy();
});
const cols = ref([
  {
    name: t("pages.products.labels.name"),
    field: "name",
    sort: "",
  },
  {
    name: t("pages.products.labels.type"),
    field: "type",
    sort: "",
  },
]);
const listToppings = ref([]);
const listToppingsToGetData = ref([]);
const isFirstLoadForm = ref(0);
const onFetchListToppings = async (query) => {
  const response = await productService.getListFavor(query);
  if (!response?.error) {
    if (isFirstLoadForm.value === 0) {
      listToppingsToGetData.value = response.data.data;
    }
    isFirstLoadForm.value += 1;
    listToppings.value = response.data.data;
    selectedToppingId.value = [];
    for (let j = 0; j < formData.value.customFoods.length; j++) {
      for (let i = 0; i < listToppingsToGetData.value.length; i++) {
        if (
          formData.value.customFoods[j].global_custom_id ===
          listToppingsToGetData.value[i].id
        ) {
          selectedToppingId.value.push(
            formData.value.customFoods[j].global_custom_id
          );
        }
      }
    }
  }
};

const selectedToppingId = ref([]);

const onSubmitSelectedTopping = async () => {
  console.log("selectedToppingId", selectedToppingId.value);
  let _customFoods = [];
  for (let i = 0; i < selectedToppingId.value.length; i++) {
    _customFoods.push(selectedToppingId.value[i]);
  }

  for (let j = 0; j < _customFoods.length; j++) {
    for (let i = 0; i < formData.value.customFoods.length; i++) {
      if (_customFoods[j] === formData.value.customFoods[i].global_custom_id) {
        _customFoods = _customFoods.filter((data) => {
          return data !== formData.value.customFoods[i].global_custom_id;
        });
      }
    }
  }

  for (let j = 0; j < _customFoods.length; j++) {
    for (let i = 0; i < listToppings.value.length; i++) {
      if (_customFoods[j] === listToppings.value[i].id) {
        formData.value.customFoods.push({
          title: listToppings.value[i].title,
          description: listToppings.value[i].description,
          custom_type: listToppings.value[i].type,
          limit_total: listToppings.value[i].limit_total,
          selectedItem: listToppings.value[i]?.detail?.map((gg) => ({
            constituentable_id: gg.constituentable_id,
            constituentable_source: gg.constituentable_source,
            dinein_inc_vat_price: gg.dinein_inc_vat_price,
            is_takeaway_price: gg.is_takeaway_price,
            max_quantity: gg.max_quantity,
            name: gg.name,
            price: gg.price,
            takeaway_inc_vat_price: gg.takeaway_inc_vat_price,
            takeaway_ordinary_price: gg.takeaway_ordinary_price,
            takeaway_vat_rate: gg.takeaway_vat_rate,
            thumbnail: gg.thumbnail,
            type: gg.type,
            unit: gg.unit,
            vat_rate: gg.vat_rate,
            _type: gg.type,
          })),
          disabledForm: true,
          global_custom_id: listToppings.value[i].id,
        });
      }
    }
  }

  formData.value.customFoods = formData.value.customFoods.filter(
    (item) =>
      !item.global_custom_id ||
      selectedToppingId.value.includes(item.global_custom_id)
  );
};
const searchKey = ref("");
const onSearchConstituents = useDebounceFn((e) => {
  onFetchListToppings({
    search: searchKey.value,
  });
}, 250);
const openModal = async () => {
  await onFetchListToppings({
    search: searchKey.value,
  });
  visibleModal.value = true;
};

const optionsUnit = ref([]);
const onFetchListUnit = async () => {
  const response = await unitService.getList();
  if (!response?.error) {
    const newArray = response.data?.data.map((item) => ({
      value: item.id,
      text: item.name,
    }));
    optionsUnit.value = [{ value: null, text: "Please select" }, ...newArray];
  }
};
const initialValue = ref({
  name: "",
  category_id: null,
  item_no: "",
  preparation_time: "",
  code: "",
  description: "",
  thumbnail: null,
  supplier_id: null,
  producer_id: null,
  in_stock: true,
  on_sale: true,
  show_in_kiosk: true,
  show_in_table_kiosk: true,
  show_in_dinein: true,
  hide_in_rush: false,
  show_in_takeaway: true,
  printer_id: null,
  printer_type: null,
  purchase_price: 0,
  vat_rate: 25,
  ordinary_profit: 0,
  offer_profit: 0,
  active_offer_price: false,
  is_takeaway_price: false,
  takeaway_purchase_price: null,
  takeaway_vat_rate: 15,
  takeaway_ordinary_profit: null,
  takeaway_offer_profit: null,
  takeaway_active_offer_price: false,
  weight: null,
  kacl: null,
  unit_id: null,
  is_popular: false,
  ordinary_price: null,
  ordinary_price_ex_vat: null,
  offer_price: null,
  offer_price_ex_vat: null,
  takeaway_ordinary_price: null,
  takeaway_price_ex_vat: null,
  takeaway_offer_price: null,
  takeaway_offer_ex_vat: null,
  customFoods: [],
});
const formData = ref({ ...initialValue.value });

const checkType = (type) => {
  switch (type) {
    case 1:
      return t("pages.fav_topping_group.types.remove_something");
    case 3:
      return t("pages.fav_topping_group.types.category_extra");
    case 4:
      return t("pages.fav_topping_group.types.change_with_at");
  }
};

const isCheckItem = ref(false);
const isCheckRequried = (value) => {
  if (value?.length) {
    const item = value?.find((itm) => itm?.title === "");
    if (item) {
      isCheckItem.value = true;
    }
    return true;
  }
  return true;
};
const rules = computed(() => {
  const allRules = {
    name: {
      required,
      minLength: minLength(3),
    },
    category_id: {
      required,
    },
    purchase_price: {},
    ordinary_profit: {},
    offer_profit: {},
    takeaway_purchase_price: {},
    takeaway_ordinary_profit: {},
    takeaway_offer_profit: {},
    customFoods: {
      isCheckRequried,
    },
  };
  if (formData.value.is_takeaway_price) {
    return allRules;
  } else {
    let _tmp = Object.assign({}, allRules);
    delete _tmp.takeaway_purchase_price;
    delete _tmp.takeaway_ordinary_profit;
    delete _tmp.takeaway_offer_profit;
    return _tmp;
  }
});
let v$ = useVuelidate(rules, formData);

const listCategories = ref([]);
const onFetchListCategories = async () => {
  const response = await categoryService.getList({
    limit: -1,
  });
  if (!response.error) {
    listCategories.value = response?.data?.data || [];
  }
};
const listSuppliers = ref([]);
const onFetchListSuppliers = async () => {
  const response = await supplierService.getList();
  if (!response.error) {
    listSuppliers.value = response?.data?.data || [];
  }
};
const listProducers = ref([]);
const onFetchListProducers = async () => {
  const response = await producerService.getList();
  if (!response.error) {
    listProducers.value = response?.data?.data || [];
  }
};
const listPrinters = ref([]);
const onFetchListPrinters = async () => {
  const response = await printerService.getList();
  if (!response.error) {
    listPrinters.value = response?.data?.data || [];
  }
};

async function onSubmit() {
  isSubmitForm.value = true;
  const result = await v$.value.$validate();
  if (!result) {
    return;
  }
  try {
    const isCheckRequire = formData.value.customFoods?.find(
      (itm) => itm?.title === ""
    );
    if (isCheckRequire) {
      return;
    }
    const payload = new FormData();
    const _formData = {
      ...formData.value,
      printer_id:
        formData?.value?.printer_id === "no_printer"
          ? ""
          : formData?.value?.printer_id,
      takeaway_price_ex_vat: formData.value?.takeaway_price_ex_vat || 0,
      takeaway_offer_ex_vat: formData.value?.takeaway_offer_ex_vat || 0,
      unit_id: formData.value?.unit_id || null,
      purchase_price: formData.value?.purchase_price || 0,
      takeaway_purchase_price: formData.value.takeaway_purchase_price || 0,
    };
    if (formData.value.printer_id) {
      _formData.printer_type = 1;
    } else {
      _formData.printer_type = 2;
    }
    _formData.purchase_price = _formData.purchase_price
      ? _formData.purchase_price
      : 0;
    _formData.takeaway_purchase_price = _formData.takeaway_purchase_price
      ? _formData.takeaway_purchase_price
      : 0;
    _formData.ordinary_price = _formData.ordinary_price
      ? _formData.ordinary_price
      : 0;
    _formData.offer_price = _formData.offer_price ? _formData.offer_price : 0;
    _formData.takeaway_ordinary_price = _formData.takeaway_ordinary_price
      ? _formData.takeaway_ordinary_price
      : 0;
    _formData.takeaway_offer_price = _formData.takeaway_offer_price
      ? _formData.takeaway_offer_price
      : 0;
    _formData.ordinary_price_ex_vat = _formData.ordinary_price_ex_vat
      ? _formData.ordinary_price_ex_vat
      : 0;
    _formData.takeaway_ordinary_profit = _formData.takeaway_ordinary_profit
      ? _formData.takeaway_ordinary_profit
      : 0;
    _formData.offer_price_ex_vat = _formData.offer_price_ex_vat
      ? _formData.offer_price_ex_vat
      : 0;
    _formData.takeaway_offer_ex_vat = _formData.takeaway_offer_ex_vat
      ? _formData.takeaway_offer_ex_vat
      : 0;
    _formData.takeaway_offer_profit = _formData.takeaway_offer_profit
      ? _formData.takeaway_offer_profit
      : 0;
    _formData.offer_profit = _formData.offer_profit
      ? _formData.offer_profit
      : 0;
    console.log("_formData", _formData);
    if (formData.value.weight === null || formData.value.weight === "") {
      delete _formData.weight;
    }
    if (formData.value.kacl === null || formData.value.kacl === "") {
      delete _formData.kacl;
    }
    if (!formData.value.is_takeaway_price) {
      delete _formData.customFoods;
      delete _formData.takeaway_ordinary_profit;
      delete _formData.takeaway_offer_profit;
      delete _formData.takeaway_ordinary_price;
      delete _formData.takeaway_offer_price;
      delete _formData.takeaway_purchase_price;
    } else {
      payload.append(
        "takeaway_purchase_price",
        formData.value.takeaway_purchase_price || 0
      );
    }
    payload.append("purchase_price", formData.value.purchase_price || 0);

    const propertiesToCheck = ["purchase_price", "takeaway_purchase_price"];
    propertiesToCheck.forEach((prop) => {
      if (!_formData[prop]) delete _formData[prop];
    });
    for (let key in _formData) {
      const value = _formData[key];
      let valueAppend = null;
      if (value === true) {
        valueAppend = 1;
      } else if (value === false) {
        valueAppend = 0;
      } else if (value === null) {
        valueAppend = "";
      } else {
        valueAppend = value;
      }
      payload.append(key, valueAppend);
    }
    console.log("formData.value.customFoods", formData.value.customFoods);
    const _customFoods =
      formData.value.customFoods?.map((item) => {
        return {
          title: item?.title,
          global_custom_id: item?.global_custom_id,
          description: item?.description,
          custom_type: item?.custom_type,
          hide_in_rush: item?.hide_in_rush ? 1 : 0,
          limit_total: item?.limit_total,
          constituents: item?.selectedItem?.map((gg) => ({
            constituentable_id: item?.global_custom_id
              ? gg.constituentable_id
              : gg?.id,
            constituentable_source: item?.global_custom_id
              ? gg.constituentable_source
              : gg?.type,
            takeaway_inc_vat_price:
              gg?.takeaway_inc_vat_price === 0
                ? "0"
                : gg?.takeaway_inc_vat_price || null,
            takeaway_vat_rate: gg?.takeaway_vat_rate,
            vat_rate: gg?.vat_rate,
            dinein_inc_vat_price:
              gg?.dinein_inc_vat_price === 0
                ? "0"
                : gg?.dinein_inc_vat_price || null,
            max_quantity: item?.custom_type === 3 ? gg?.max_quantity : "",
            type: item?.custom_type === 4 ? gg?._type : "",
          })),
        };
      }) || [];
    console.log(_customFoods);

    payload.append("custom_products", JSON.stringify(_customFoods));
    if (id) {
      payload.append("_method", "PATCH");
    }
    const response = id
      ? await productService.update(id, payload)
      : await productService.create(payload);
    if (!response?.error) {
      if (typeSubmit.value === "confirm") {
        scrollStore.getElement(id);
        dataFetch.setData([], "merchant-products-list");
        dataFetch.setTotal(0, "merchant-products-list");
        return await route.pushByName({ name: "merchant-products-list" });
      } else {
        formData.value = { ...initialValue.value };
        dropzone.value.destroy();
        v$.value.$reset();
      }
    }
    store.pageLoader({ mode: "off" });
    if (typeSubmit.value === "confirm") {
      v$.value.$reset();
    }
    setNotify({
      title: "Success",
      message: response?.message,
      type: "success",
    });
  } catch (e) {
    setNotify({
      title: "Error",
      message: e?.message,
    });
  }
}

const previewThumb = ref();

const onAddCustomItem = (type) => {
  console.log("formData.value.customFoods", formData.value.customFoods);
  formData.value.customFoods.push({
    title: "",
    description: "",
    custom_type: type,
    selectedItem: [],
  });
};

const onRemoveCustomFood = (index, global_custom_id) => {
  formData.value.customFoods.splice(index, 1);
  if (global_custom_id) {
    selectedToppingId.value = selectedToppingId.value.filter((data) => {
      return data !== global_custom_id;
    });
  }
  console.log("index", index);
};

const onFetchProductDetail = async (id) => {
  try {
    const response = await productService.getDetail(id);
    if (!response.error) {
      productDetail.value = response?.data || [];
      formData.value = {
        ...productDetail.value,
        name: productDetail.value?.name || "",
        category_id: productDetail.value?.category_id || null,
        item_no: productDetail.value?.item_no || "",
        preparation_time: productDetail.value?.preparation_time || "",
        code: productDetail.value?.code || "",
        description: productDetail.value?.description || "",
        thumbnail: productDetail.value?.thumbnail || null,
        supplier_id: productDetail.value?.supplier_id || null,
        producer_id: productDetail.value?.producer_id || null,
        in_stock: productDetail.value?.in_stock,
        on_sale: productDetail.value?.on_sale,
        show_in_kiosk: productDetail.value?.show_in_kiosk,
        show_in_dinein: productDetail.value?.show_in_dinein,
        show_in_table_kiosk: productDetail.value?.show_in_table_kiosk,
        show_in_takeaway: productDetail.value?.show_in_takeaway,
        printer_id:
          productDetail.value?.printer_type === 1
            ? productDetail.value?.printer_id === null
              ? "no_printer"
              : productDetail.value?.printer_id
            : null,
        printer_type: productDetail.value?.printer_type,
        purchase_price: productDetail.value?.purchase_price
          ? Number(productDetail.value?.purchase_price)
          : null,
        vat_rate: productDetail.value?.vat_rate,
        ordinary_profit: productDetail.value?.ordinary_profit
          ? Number(productDetail.value?.ordinary_profit)
          : null,
        ordinary_price: productDetail.value.ordinary_price,
        offer_profit: productDetail.value?.offer_profit
          ? Number(productDetail.value?.offer_profit)
          : null,
        active_offer_price: productDetail.value?.active_offer_price,
        is_takeaway_price: productDetail.value?.is_takeaway_price,
        takeaway_purchase_price: productDetail.value?.takeaway_purchase_price
          ? Number(productDetail.value?.takeaway_purchase_price)
          : null,
        takeaway_vat_rate: productDetail.value?.takeaway_vat_rate,
        takeaway_ordinary_profit: productDetail.value?.takeaway_ordinary_profit
          ? Number(productDetail.value?.takeaway_ordinary_profit)
          : null,
        takeaway_offer_profit: productDetail.value?.takeaway_offer_profit
          ? Number(productDetail.value?.takeaway_offer_profit)
          : null,
        takeaway_ordinary_price: productDetail.value?.takeaway_ordinary_price,
        takeaway_offer_price: productDetail.value?.takeaway_offer_price,
        takeaway_active_offer_price:
          productDetail.value?.takeaway_active_offer_price,
        offer_price_ex_vat: productDetail.value?.offer_price_ex_vat,
        offer_price: productDetail.value?.offer_price,
        takeaway_offer_ex_vat: productDetail.value?.takeaway_offer_ex_vat,
        weight: productDetail.value?.weight
          ? Number(productDetail.value?.weight)
          : null,
        kacl: productDetail.value?.kacl
          ? Number(productDetail.value?.kacl)
          : null,
        unit_id: productDetail.value?.unit?.id,
        is_popular: productDetail.value?.is_popular,
        ordinary_price_ex_vat: productDetail.value?.ordinary_price_ex_vat,
      };
      if (productDetail.value?.thumbnail) {
        previewThumb.value = productDetail.value?.thumbnail;
      }
      if (productDetail.value?.custom_products?.length) {
        formData.value.customFoods = productDetail.value?.custom_products?.map(
          (item) => {
            return {
              title: item?.title,
              description: item?.description,
              limit_total: item?.limit_total,
              global_custom_id: item?.global_custom_id || null,
              disabledForm: item?.global_custom_id ? true : false,
              hide_in_rush: item?.hide_in_rush,
              custom_type: item?.type,
              selectedItem: item?.constituents?.map((cons) => ({
                id: cons?.constituentable_id,
                constituentable_id: cons?.constituentable_id,
                constituentable_source: cons?.constituentable_type,
                name: cons?.name,
                is_takeaway_price: cons?.is_takeaway_price,
                takeaway_ordinary_price: cons?.takeaway_ordinary_price,
                takeaway_inc_vat_price:
                  cons?.takeaway_inc_vat_price === 0
                    ? 0
                    : cons?.takeaway_inc_vat_price ||
                      cons?.takeaway_ordinary_price,
                takeaway_inc_vat_price_v2:
                  cons?.takeaway_inc_vat_price === 0
                    ? 0
                    : cons?.takeaway_inc_vat_price ||
                      cons?.takeaway_ordinary_price,
                dinein_inc_vat_price:
                  cons?.dinein_inc_vat_price === 0
                    ? 0
                    : cons?.dinein_inc_vat_price || cons?.price,
                takeaway_vat_rate: cons?.takeaway_vat_rate,
                vat_rate: cons?.vat_rate,
                price: cons?.price,
                max_quantity: cons?.max_quantity,
                type: cons?.constituentable_type,
                _type: cons?.type,
                unit: {
                  name: cons?.unit,
                },
              })),
            };
          }
        );
      } else {
        formData.value.customFoods = [];
      }
      v$ = useVuelidate(rules, formData);
    } else {
      productDetail.value = null;
    }
  } catch (e) {
    console.log(e);
  }
};

const listVatRate = [
  {
    label: "0%",
    value: 0,
  },
  {
    label: "15%",
    value: 15,
  },
  {
    label: "25%",
    value: 25,
  },
];
const purchasePriceOrdinary = computed(
  () =>
    (formData?.value?.purchase_price || 0) +
    (formData?.value?.ordinary_profit || 0)
);
const profitRateOrdinary = computed(() =>
  (
    (formData?.value?.ordinary_profit / (purchasePriceOrdinary.value || 1)) *
    100
  ).toFixed(0)
);
const vatOrdinary = computed(
  () => (purchasePriceOrdinary.value * formData?.value?.vat_rate) / 100
);
const purchasePriceVatOrdinary = computed(
  () => purchasePriceOrdinary?.value + vatOrdinary?.value
);

const purchasePriceOffer = computed(
  () =>
    (formData?.value?.purchase_price || 0) +
    (formData?.value?.offer_profit || 0)
);
const profitRateOffer = computed(() =>
  (
    (formData?.value?.offer_profit / (purchasePriceOffer.value || 1)) *
    100
  ).toFixed(0)
);
const vatOffer = computed(
  () => (purchasePriceOffer.value * formData?.value?.vat_rate) / 100
);
const purchasePriceVatOffer = computed(
  () => purchasePriceOffer?.value + vatOffer?.value
);

const purchasePriceOrdinaryTakeaway = computed(
  () =>
    (formData?.value?.takeaway_purchase_price || 0) +
    (formData?.value?.takeaway_ordinary_profit || 0)
);
const profitRateOrdinaryTakeaway = computed(() =>
  (
    (formData?.value?.takeaway_ordinary_profit /
      (purchasePriceOrdinaryTakeaway.value || 1)) *
    100
  ).toFixed(0)
);
const vatOrdinaryTakeaway = computed(
  () =>
    (purchasePriceOrdinaryTakeaway.value * formData?.value?.takeaway_vat_rate) /
    100
);
const purchasePriceVatOrdinaryTakeaway = computed(
  () => purchasePriceOrdinaryTakeaway?.value + vatOrdinaryTakeaway?.value
);

const purchasePriceOfferTakeaway = computed(
  () =>
    (formData?.value?.takeaway_purchase_price || 0) +
    (formData?.value?.takeaway_offer_profit || 0)
);
const profitRateOfferTakeaway = computed(() =>
  (
    (formData?.value?.takeaway_offer_profit /
      (purchasePriceOfferTakeaway.value || 1)) *
    100
  ).toFixed(0)
);
const vatOfferTakeaway = computed(
  () =>
    (purchasePriceOfferTakeaway.value * formData?.value?.takeaway_vat_rate) /
    100
);
const purchasePriceVatOfferTakeaway = computed(
  () => purchasePriceOfferTakeaway?.value + vatOfferTakeaway?.value
);

const onSwapCustomFood = (index, step) => {
  if (index === formData.value.customFoods?.length - 1 && step === 1) return;
  [
    formData.value.customFoods[index],
    formData.value.customFoods[index + step],
  ] = [
    formData.value.customFoods[index + step],
    formData.value.customFoods[index],
  ];
};

const { userInfo, userPlanId } = useAuth();
const listPlans = ref([]);
const onFetchRestaurantDetail = async () => {
  const response = await restaurantService.getDetail(userInfo.value.store_id);
  if (!response.error) {
    listPlans.value = response?.data.plans || [];
  }
};

const userPlansCustomFood = computed(() =>
  listPlans.value?.find((item) => item.id === userPlanId.CUSTOM_FOOD)
); // 1: custom foods

function isDecimal(num) {
  return num % 1 !== 0;
}
const parseNumber = (input) => {
  if (!input || Number(input) === 0) return null;
  return isDecimal
    ? parseFloat(((input * 100) / 100).toFixed(2))
    : Number(input);
};

// const roundToTwoDecimalPlaces = (number) => {
//   if (Number.isFinite(number) && !Number.isInteger(number)) {
//     return Number(number.toFixed(2));
//   } else {
//     return number;
//   }
// }

const handleSubmit = (type) => {
  typeSubmit.value = type;
};

const handleInput = (value, type) => {
  const parseVal = parseNumber(value);
  switch (type) {
    case "ordinary_profit": {
      const newPriceExVat =
        (formData.value.purchase_price || 0) + (parseVal || 0);
      const vat = (newPriceExVat * formData.value.vat_rate) / 100;
      const newPriceIncVat = newPriceExVat + vat;
      if (formData.value.ordinary_price_ex_vat !== parseNumber(newPriceExVat)) {
        formData.value.ordinary_price_ex_vat = parseNumber(newPriceExVat) || 0;
      }
      if (formData.value.ordinary_price !== parseNumber(newPriceIncVat)) {
        formData.value.ordinary_price = parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "ordinary_price_ex_vat": {
      const newProfit = (parseVal || 0) - (formData.value?.purchase_price || 0);
      const vat = (parseVal * formData?.value?.vat_rate) / 100;

      const newPriceIncVat = parseVal + vat;
      if (formData.value.ordinary_profit !== newProfit) {
        formData.value.ordinary_profit =
          parseNumber(newProfit) >= 0 ? newProfit : 0;
      }
      if (formData.value.ordinary_price !== parseNumber(newPriceIncVat)) {
        formData.value.ordinary_price = parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "ordinary_price": {
      const vat = formData?.value?.vat_rate
        ? parseVal - parseVal / (1 + formData?.value?.vat_rate / 100)
        : 0;
      const newOrdinaryPriceExVat = parseVal - vat;
      const newOrdinaryProfit =
        newOrdinaryPriceExVat - (formData.value.purchase_price || 0);
      if (
        formData.value.ordinary_price_ex_vat !==
        parseNumber(newOrdinaryPriceExVat)
      ) {
        formData.value.ordinary_price_ex_vat =
          parseNumber(newOrdinaryPriceExVat) || 0;
      }
      if (formData.value.ordinary_profit !== parseNumber(newOrdinaryProfit)) {
        formData.value.ordinary_profit =
          parseNumber(newOrdinaryProfit) >= 0 &&
          parseNumber(newOrdinaryProfit) !== null
            ? parseNumber(newOrdinaryProfit)
            : 0;
      }
      break;
    }
    case "offer_profit": {
      const newPriceExVat =
        (formData.value.purchase_price || 0) + (parseVal || 0);
      const vat = (newPriceExVat * formData.value.vat_rate) / 100;
      const newPriceIncVat = newPriceExVat + vat;
      if (formData.value.offer_price_ex_vat !== parseNumber(newPriceExVat)) {
        formData.value.offer_price_ex_vat = parseNumber(newPriceExVat) || 0;
      }
      if (formData.value.offer_price !== parseNumber(newPriceIncVat)) {
        formData.value.offer_price = parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "offer_price_ex_vat": {
      const newProfit = (parseVal || 0) - (formData.value?.purchase_price || 0);
      const vat = (parseVal * formData?.value?.vat_rate) / 100;
      const newPriceIncVat = parseVal + vat;
      if (formData.value.offer_profit !== parseNumber(newProfit)) {
        formData.value.offer_profit =
          parseNumber(newProfit) >= 0 && parseNumber(newProfit) !== null
            ? parseNumber(newProfit)
            : 0;
      }
      if (formData.value.offer_price !== parseNumber(newPriceIncVat)) {
        formData.value.offer_price = parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "offer_price": {
      const vat = formData?.value?.vat_rate
        ? parseVal - parseVal / (1 + formData?.value?.vat_rate / 100)
        : 0;
      const newOrdinaryPriceExVat = parseVal - vat;
      const newOrdinaryProfit =
        newOrdinaryPriceExVat - (formData.value.purchase_price || 0);
      if (
        formData.value.offer_price_ex_vat !== parseNumber(newOrdinaryPriceExVat)
      ) {
        formData.value.offer_price_ex_vat =
          parseNumber(newOrdinaryPriceExVat) || 0;
      }
      if (formData.value.offer_profit !== parseNumber(newOrdinaryProfit)) {
        formData.value.offer_profit =
          parseNumber(newOrdinaryProfit) >= 0 &&
          parseNumber(newOrdinaryProfit) !== null
            ? parseNumber(newOrdinaryProfit)
            : 0;
      }
      break;
    }
  }
};

const handleInputTakeAway = (value, type) => {
  const parseVal = parseNumber(value);
  switch (type) {
    case "takeaway_ordinary_profit": {
      const newPriceExVat =
        (formData.value.takeaway_purchase_price || 0) + (parseVal || 0);
      const vat = (newPriceExVat * formData.value.takeaway_vat_rate) / 100;
      const newPriceIncVat = newPriceExVat + vat;
      if (formData.value.takeaway_price_ex_vat !== parseNumber(newPriceExVat)) {
        formData.value.takeaway_price_ex_vat = parseNumber(newPriceExVat) || 0;
      }
      if (
        formData.value.takeaway_ordinary_price !== parseNumber(newPriceIncVat)
      ) {
        formData.value.takeaway_ordinary_price =
          parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "takeaway_price_ex_vat": {
      const newProfit =
        (parseVal || 0) - (formData.value?.takeaway_purchase_price || 0);
      const vat = (parseVal * formData?.value?.takeaway_vat_rate) / 100;
      const newPriceIncVat = parseVal + vat;
      if (formData.value.takeaway_ordinary_profit !== newProfit) {
        formData.value.takeaway_ordinary_profit =
          parseNumber(newProfit) >= 0 && parseNumber(newProfit) !== null
            ? parseNumber(newProfit)
            : 0;
      }
      if (
        formData.value.takeaway_ordinary_price !== parseNumber(newPriceIncVat)
      ) {
        formData.value.takeaway_ordinary_price =
          parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "takeaway_ordinary_price": {
      const vat = formData?.value?.takeaway_vat_rate
        ? parseVal - parseVal / (1 + formData?.value?.takeaway_vat_rate / 100)
        : 0;
      const newOrdinaryPriceExVat = parseVal - vat;
      const newOrdinaryProfit =
        newOrdinaryPriceExVat - (formData.value.takeaway_purchase_price || 0);
      if (
        formData.value.takeaway_price_ex_vat !==
        parseNumber(newOrdinaryPriceExVat)
      ) {
        formData.value.takeaway_price_ex_vat =
          parseNumber(newOrdinaryPriceExVat) || 0;
      }
      if (
        formData.value.takeaway_ordinary_profit !==
        parseNumber(newOrdinaryProfit)
      ) {
        formData.value.takeaway_ordinary_profit =
          parseNumber(newOrdinaryProfit) >= 0 &&
          parseNumber(newOrdinaryProfit) !== null
            ? parseNumber(newOrdinaryProfit)
            : 0;
      }
      break;
    }
    case "takeaway_offer_profit": {
      const newPriceExVat =
        (formData.value.takeaway_purchase_price || 0) + (parseVal || 0);
      const vat = (newPriceExVat * formData.value.takeaway_vat_rate) / 100;
      const newPriceIncVat = newPriceExVat + vat;
      if (formData.value.takeaway_offer_ex_vat !== parseNumber(newPriceExVat)) {
        formData.value.takeaway_offer_ex_vat = parseNumber(newPriceExVat) || 0;
      }
      if (formData.value.takeaway_offer_price !== parseNumber(newPriceIncVat)) {
        formData.value.takeaway_offer_price = parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "takeaway_offer_ex_vat": {
      const newProfit =
        (parseVal || 0) - (formData.value?.takeaway_purchase_price || 0);
      const vat = (parseVal * formData?.value?.takeaway_vat_rate) / 100;
      const newPriceIncVat = parseVal + vat;
      if (formData.value.takeaway_offer_profit !== newProfit) {
        formData.value.takeaway_offer_profit =
          parseNumber(newProfit) >= 0 && parseNumber(newProfit) !== null
            ? parseNumber(newProfit)
            : 0;
      }
      if (formData.value.takeaway_offer_price !== parseNumber(newPriceIncVat)) {
        formData.value.takeaway_offer_price = parseNumber(newPriceIncVat) || 0;
      }
      break;
    }
    case "takeaway_offer_price": {
      const vat = formData?.value?.takeaway_vat_rate
        ? parseVal - parseVal / (1 + formData?.value?.takeaway_vat_rate / 100)
        : 0;
      const newOrdinaryPriceExVat = parseVal - vat;
      const newOrdinaryProfit =
        newOrdinaryPriceExVat - (formData.value.takeaway_purchase_price || 0);
      if (
        formData.value.takeaway_offer_ex_vat !==
        parseNumber(newOrdinaryPriceExVat)
      ) {
        formData.value.takeaway_offer_ex_vat =
          parseNumber(newOrdinaryPriceExVat) || 0;
      }
      if (
        formData.value.takeaway_offer_profit !== parseNumber(newOrdinaryProfit)
      ) {
        formData.value.takeaway_offer_profit =
          parseNumber(newOrdinaryProfit) >= 0 &&
          parseNumber(newOrdinaryProfit) !== null
            ? parseNumber(newOrdinaryProfit)
            : 0;
      }
      break;
    }
  }
};

const handleInputIsTakeAway = (checked) => {
  formData.value.is_takeaway_price = checked;
  if (checked) {
    if (
      formData.value.is_takeaway_price &&
      !formData.value.takeaway_purchase_price
    ) {
      formData.value.takeaway_purchase_price = formData.value.purchase_price;
    }
    if (
      formData.value.is_takeaway_price &&
      !parseNumber(formData.value.takeaway_ordinary_price)
    ) {
      const parseVal = parseNumber(formData.value.ordinary_price);
      formData.value.takeaway_ordinary_price = parseVal;
      const vat = formData?.value?.takeaway_vat_rate
        ? parseVal - parseVal / (1 + formData?.value?.takeaway_vat_rate / 100)
        : 0;
      const newOrdinaryPriceExVat = parseVal - vat;
      const newOrdinaryProfit =
        newOrdinaryPriceExVat - (formData.value.takeaway_purchase_price || 0);
      if (
        formData.value.takeaway_price_ex_vat !==
        parseNumber(newOrdinaryPriceExVat)
      ) {
        formData.value.takeaway_price_ex_vat =
          parseNumber(newOrdinaryPriceExVat) || 0;
      }
      if (
        formData.value.takeaway_ordinary_profit !==
        parseNumber(newOrdinaryProfit)
      ) {
        formData.value.takeaway_ordinary_profit =
          parseNumber(newOrdinaryProfit) >= 0
            ? parseNumber(newOrdinaryProfit)
            : 0;
      }
    }
    if (
      formData.value.is_takeaway_price &&
      !parseNumber(formData.value.takeaway_offer_price)
    ) {
      const parseVal = parseNumber(formData.value.offer_price);
      formData.value.takeaway_offer_price = parseVal;
      const vat = formData?.value?.takeaway_vat_rate
        ? parseVal - parseVal / (1 + formData?.value?.takeaway_vat_rate / 100)
        : 0;
      const newOrdinaryPriceExVat = parseVal - vat;
      const newOrdinaryProfit =
        newOrdinaryPriceExVat - (formData.value.takeaway_purchase_price || 0);
      if (
        formData.value.takeaway_offer_ex_vat !==
        parseNumber(newOrdinaryPriceExVat)
      ) {
        formData.value.takeaway_offer_ex_vat =
          parseNumber(newOrdinaryPriceExVat) || 0;
      }
      if (
        formData.value.takeaway_offer_profit !== parseNumber(newOrdinaryProfit)
      ) {
        formData.value.takeaway_offer_profit =
          parseNumber(newOrdinaryProfit) >= 0
            ? parseNumber(newOrdinaryProfit)
            : 0;
      }
    }
  }
};
</script>

<template>
  <BasePageHeading
    :title="title"
    :go-back="true"
    :subtitle="t('pages.products.labels.label_head_form')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.products.labels.manage")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <router-link to="/merchant/products">{{
              t("pages.products.name")
            }}</router-link>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            <span>{{
              id
                ? t("pages.products.titles.update")
                : t("pages.products.titles.create")
            }}</span>
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <div class="row">
      <div class="col-sm-12 col-md-12">
        <form @submit.prevent="onSubmit">
          <BaseBlock
            :title="
              id
                ? t(`pages.products.fields.update_product`, {
                    name: productDetail?.name,
                  })
                : t('pages.products.titles.create')
            "
          >
            <template #options>
              <e-icon
                @click="
                  () => {
                    route.back();
                    dataFetch.setData([], 'merchant-products-list');
                    dataFetch.setTotal(0, 'merchant-products-list');
                  }
                "
                name="arrow-left"
                role="button"
                class="icon_arrow_left"
              />
            </template>

            <ul class="nav nav-tabs nav-tabs-block" role="tablist">
              <li class="nav-item">
                <button
                  class="nav-link active"
                  id="btabs-static-home-tab"
                  data-bs-toggle="tab"
                  data-bs-target="#btabs-static-home"
                  role="tab"
                  type="button"
                  aria-controls="btabs-static-home"
                  aria-selected="true"
                >
                  {{ t("pages.products.labels.generally") }}
                </button>
              </li>
              <li class="nav-item" v-if="userPlansCustomFood?.active">
                <button
                  class="nav-link"
                  id="btabs-static-profile-tab"
                  data-bs-toggle="tab"
                  data-bs-target="#btabs-static-profile"
                  role="tab"
                  type="button"
                  aria-controls="btabs-static-profile"
                  aria-selected="false"
                >
                  {{ t("pages.products.labels.constituents") }}
                </button>
              </li>
            </ul>

            <div class="block-content tab-content">
              <div
                class="tab-pane active"
                id="btabs-static-home"
                role="tabpanel"
                aria-labelledby="btabs-static-home-tab"
                tabindex="0"
              >
                <div class="row">
                  <div class="col-12 col-md-8">
                    <div class="mb-4">
                      <label class="form-label" for="val-name"
                        >{{ t("pages.products.fields.name") }}
                        <span class="text-danger">*</span></label
                      >
                      <input
                        type="text"
                        id="val-name"
                        class="form-control"
                        :class="{
                          'is-invalid': v$.name.$errors.length,
                        }"
                        v-model="formData.name"
                        @blur="v$.name.$touch"
                        placeholder="Enter..."
                      />
                      <div
                        v-if="v$.name.$errors.length"
                        class="invalid-feedback animated fadeIn"
                      >
                        Name is required
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-md-4">
                    <div class="mb-4">
                      <label class="form-label" for="val-category_id"
                        >{{ t("pages.products.fields.category_field")
                        }}<span class="text-danger">*</span></label
                      >
                      <select
                        v-model="formData.category_id"
                        class="form-select"
                        id="val-category_id"
                        name="category_id"
                        :class="{
                          'is-invalid': v$.category_id.$errors.length,
                        }"
                      >
                        <option :value="null" disabled>
                          {{ t("pages.products.labels.select") }}
                        </option>
                        <option
                          v-for="cate in listCategories"
                          :key="cate?.id"
                          :value="cate?.id"
                        >
                          {{ cate?.name }}
                        </option>
                      </select>
                      <div
                        v-if="v$.category_id.$errors.length"
                        class="invalid-feedback animated fadeIn"
                      >
                        Category is required
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-md-5">
                    <div class="mb-4">
                      <label class="form-label" for="val-item_no">{{
                        t("pages.products.fields.item_no")
                      }}</label>
                      <input
                        type="number"
                        id="val-item_no"
                        class="form-control"
                        v-model="formData.item_no"
                        placeholder="Enter..."
                        step="0.01"
                      />
                    </div>
                  </div>
                  <div class="col-12 col-md-3">
                    <div class="mb-4">
                      <label class="form-label" for="val-preparation_time">{{
                        t("pages.products.fields.preparation_time")
                      }}</label>
                      <input
                        type="number"
                        id="val-preparation_time"
                        class="form-control"
                        v-model="formData.preparation_time"
                        placeholder="Enter..."
                        step="0.01"
                      />
                    </div>
                  </div>
                  <div class="col-12 col-md-4">
                    <div class="mb-4">
                      <label class="form-label" for="val-code">{{
                        t("pages.products.fields.barcode")
                      }}</label>
                      <input
                        type="text"
                        id="val-code"
                        class="form-control"
                        v-model="formData.code"
                        placeholder="Enter..."
                      />
                    </div>
                  </div>
                  <div class="col-12 col-md-8">
                    <div class="mb-4">
                      <label class="form-label" for="val-description">{{
                        t("pages.products.fields.description")
                      }}</label>
                      <textarea
                        v-model="formData.description"
                        class="form-control"
                        id="val-description"
                        rows="7"
                        placeholder="Enter..."
                      ></textarea>
                    </div>
                  </div>
                  <div class="col-12 col-md-4">
                    <div class="mb-4">
                      <label class="form-label" for="example-file-input">{{
                        t("pages.products.fields.upload")
                      }}</label>
                      <form id="dropzoneForm" class="dropzone"></form>
                    </div>
                  </div>
                  <div class="col-12 col-md-4">
                    <div class="mb-4">
                      <label class="form-label" for="val-supplier_id">{{
                        t("pages.products.fields.supplier")
                      }}</label>
                      <select
                        v-model="formData.supplier_id"
                        class="form-select"
                        id="val-supplier_id"
                      >
                        <option :value="null" disabled>
                          {{ t("pages.products.labels.select") }}
                        </option>
                        <option
                          v-for="cate in listSuppliers"
                          :key="cate?.id"
                          :value="cate?.id"
                        >
                          {{ cate?.name }}
                        </option>
                      </select>
                    </div>
                  </div>
                  <div class="col-12 col-md-4">
                    <div class="mb-4">
                      <label class="form-label" for="val-producer_id">{{
                        t("pages.products.fields.producer")
                      }}</label>
                      <select
                        v-model="formData.producer_id"
                        class="form-select"
                        id="val-producer_id"
                      >
                        <option :value="null" disabled>
                          {{ t("pages.products.labels.select") }}
                        </option>
                        <option
                          v-for="cate in listProducers"
                          :key="cate?.id"
                          :value="cate?.id"
                        >
                          {{ cate?.name }}
                        </option>
                      </select>
                    </div>
                  </div>
                  <div class="col-12 col-md-4">
                    <div class="mb-4">
                      <label class="form-label" for="val-printer_id"
                        >{{ t("pages.products.fields.printer_field")
                        }}<span class="text-danger">*</span></label
                      >
                      <select
                        v-model="formData.printer_id"
                        class="form-select"
                        id="val-printer_id"
                      >
                        <option :value="null">
                          {{ t("pages.products.labels.by_category") }}
                        </option>
                        <option value="no_printer">
                          {{ t("pages.products.labels.no_printer") }}
                        </option>
                        <option
                          v-for="cate in listPrinters"
                          :key="cate?.id"
                          :value="cate?.id"
                        >
                          {{ cate?.name }}
                        </option>
                      </select>
                    </div>
                  </div>
                  <div class="col-sm-8 col-md-4 mb-4">
                    <label class="form-label" for="val-unit-id">{{
                      t("pages.products.fields.unit")
                    }}</label>
                    <select
                      id="val-unit-id"
                      class="form-select"
                      v-model="formData.unit_id"
                    >
                      <option
                        v-for="(item, index) in optionsUnit"
                        :value="item.value"
                        :key="`item-${index}`"
                      >
                        {{ item.text }}
                      </option>
                    </select>
                  </div>
                  <div class="col-12 col-md-4">
                    <div class="mb-4">
                      <label class="form-label" for="val-weight">{{
                        t("pages.products.fields.weight")
                      }}</label>
                      <input
                        type="number"
                        id="val-weight"
                        class="form-control"
                        v-model="formData.weight"
                        :placeholder="t('common.enter')"
                        step="0.01"
                      />
                    </div>
                  </div>
                  <div class="col-12 col-md-4">
                    <div class="mb-4">
                      <label class="form-label" for="val-kacl">{{
                        t("pages.products.fields.kacl")
                      }}</label>
                      <input
                        type="number"
                        id="val-kacl"
                        class="form-control"
                        v-model="formData.kacl"
                        :placeholder="t('common.enter')"
                      />
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-12 col-md-3">
                    <div class="space-y-2 mb-4">
                      <div class="form-check form-switch">
                        <input
                          v-model="formData.in_stock"
                          class="form-check-input"
                          type="checkbox"
                          id="val-in_stock"
                        />
                        <label class="form-check-label" for="val-in_stock">{{
                          t("pages.products.fields.in_stock_field")
                        }}</label>
                      </div>
                      <div class="form-check form-switch">
                        <input
                          v-model="formData.on_sale"
                          class="form-check-input"
                          type="checkbox"
                          id="val-on_sale"
                        />
                        <label class="form-check-label" for="val-on_sale">{{
                          t("pages.products.fields.on_sale")
                        }}</label>
                      </div>
                      <div class="form-check form-switch">
                        <input
                          v-model="formData.is_popular"
                          class="form-check-input"
                          type="checkbox"
                          id="val-is_popular"
                        />
                        <label class="form-check-label" for="val-is_popular">{{
                          t("pages.products.fields.popular")
                        }}</label>
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-md-4">
                    <div class="space-y-2 mb-4">
                      <div class="form-check form-switch">
                        <input
                          v-model="formData.show_in_kiosk"
                          class="form-check-input"
                          type="checkbox"
                          id="val-show_in_kiosk"
                        />
                        <label
                          class="form-check-label"
                          for="val-show_in_kiosk"
                          >{{ t("pages.products.fields.show_in_kiosk") }}</label
                        >
                      </div>
                      <div class="form-check form-switch">
                        <input
                          v-model="formData.show_in_table_kiosk"
                          class="form-check-input"
                          type="checkbox"
                          id="val-show_in_table_kiosk"
                        />
                        <label
                          class="form-check-label"
                          for="val-show_in_table_kiosk"
                          >{{
                            t("pages.products.fields.show_in_table_kiosk")
                          }}</label
                        >
                      </div>
                      <div class="form-check form-switch">
                        <input
                          v-model="formData.show_in_takeaway"
                          class="form-check-input"
                          type="checkbox"
                          id="val-show_in_takeaway"
                        />
                        <label
                          class="form-check-label"
                          for="val-show_in_takeaway"
                          >{{
                            t("pages.products.fields.show_in_takeaway")
                          }}</label
                        >
                      </div>
                      <div class="form-check form-switch">
                        <input
                          v-model="formData.show_in_dinein"
                          class="form-check-input"
                          type="checkbox"
                          id="val-show_in_dinein"
                        />
                        <label
                          class="form-check-label"
                          for="val-show_in_dinein"
                          >{{
                            t("pages.products.fields.show_in_dinein")
                          }}</label
                        >
                      </div>
                    </div>
                  </div>
                  <hr class="mb-5" />
                  <div class="row bg-gray-lighter px-2 py-3 rounded-2 mx-auto">
                    <div class="col-12 col-md-10 overflow-x-auto">
                      <div class="row" style="min-width: 800px">
                        <div class="col-2 mb-2"></div>
                        <div class="col-2 mb-2"></div>
                        <div class="col-3 mb-2">
                          <div class="">
                            <label class="form-label" for="val-costPrice">{{
                              t("pages.products.fields.purchase_price")
                            }}</label>
                            <input
                              type="number"
                              id="val-costPrice"
                              class="form-control"
                              v-model="formData.purchase_price"
                              :placeholder="t('common.enter')"
                              step="0.01"
                            />
                          </div>
                        </div>
                        <div class="col-3 mb-4">
                          <div class="">
                            <label class="form-label" for="val-costPrice">{{
                              t("pages.products.fields.cost_price")
                            }}</label>
                            <input
                              type="number"
                              id="val-costPrice"
                              class="form-control"
                              :value="formData.purchase_price"
                              disabled
                              :placeholder="t('common.enter')"
                              step="0.01"
                            />
                          </div>
                        </div>
                        <div class="col-2 mb-4">
                          <div class="">
                            <label class="form-label" for="val-vatRate">{{
                              t("pages.products.fields.vat_rate")
                            }}</label>
                            <select
                              id="val-vatRate"
                              class="form-select"
                              v-model="formData.vat_rate"
                            >
                              <option
                                v-for="vat in listVatRate"
                                :key="vat.label"
                                :value="vat.value"
                              >
                                {{ vat.label }}
                              </option>
                            </select>
                          </div>
                        </div>
                        <div class="col-2 mb-2">
                          {{ t("pages.products.fields.ordinary_price") }}
                        </div>
                        <div class="col-2 mb-2">
                          <div class="">
                            <label
                              class="form-label"
                              for="val-profitRateOrdinary"
                            >
                              {{
                                t("pages.products.fields.profit_rate")
                              }}</label
                            >
                            <input
                              type="number"
                              id="val-profitRateOrdinary"
                              class="form-control"
                              :value="profitRateOrdinary"
                              disabled
                              step="0.01"
                            />
                          </div>
                        </div>
                        <div class="col-3 mb-2">
                          <div class="">
                            <label
                              class="form-label"
                              for="val-profitOrdinary"
                              >{{ t("pages.products.fields.profit") }}</label
                            >
                            <input
                              type="number"
                              id="val-profitOrdinary"
                              class="form-control"
                              placeholder="0"
                              v-model="formData.ordinary_profit"
                              :class="{
                                'is-invalid': v$.ordinary_profit.$errors.length,
                              }"
                              @blur="v$.ordinary_profit.$touch"
                              @input="
                                (e) =>
                                  handleInput(
                                    e?.target?.value,
                                    'ordinary_profit'
                                  )
                              "
                              step="0.01"
                            />
                          </div>
                        </div>
                        <div class="col-3 mb-2">
                          <div class="">
                            <label
                              class="form-label"
                              for="val-purchasePriceExVat"
                              >{{
                                t("pages.products.fields.price_ex_vat")
                              }}</label
                            >
                            <input
                              type="number"
                              id="val-purchasePriceExVat"
                              class="form-control"
                              placeholder="0"
                              v-model="formData.ordinary_price_ex_vat"
                              @input="
                                (e) =>
                                  handleInput(
                                    e?.target?.value,
                                    'ordinary_price_ex_vat'
                                  )
                              "
                              step="0.01"
                            />
                          </div>
                        </div>
                        <div class="col-2 mb-2">
                          <div class="">
                            <label
                              class="form-label"
                              for="val-purchasePriceOrdinarys"
                              >{{
                                t("pages.products.fields.price_inc_vat")
                              }}</label
                            >
                            <input
                              type="number"
                              id="val-purchasePriceOrdinarys"
                              class="form-control"
                              placeholder="0"
                              v-model="formData.ordinary_price"
                              @input="
                                (e) =>
                                  handleInput(
                                    e?.target?.value,
                                    'ordinary_price'
                                  )
                              "
                              step="0.01"
                            />
                          </div>
                        </div>
                        <div class="col-2 mb-2">
                          {{ t("pages.products.fields.offer_price") }}
                        </div>
                        <div class="col-2 mb-2">
                          <div class="">
                            <input
                              type="number"
                              id="val-profitRateOffer"
                              class="form-control"
                              :value="profitRateOffer"
                              disabled
                              step="0.01"
                            />
                          </div>
                        </div>
                        <div class="col-3 mb-2">
                          <div class="">
                            <input
                              type="number"
                              id="val-profitOffer"
                              class="form-control"
                              v-model="formData.offer_profit"
                              placeholder="0"
                              @input="
                                (e) =>
                                  handleInput(e?.target?.value, 'offer_profit')
                              "
                              step="0.01"
                            />
                          </div>
                        </div>
                        <div class="col-3 mb-2">
                          <div class="">
                            <input
                              type="number"
                              id="val-purchasePriceOfferExVat"
                              class="form-control"
                              placeholder="0"
                              v-model="formData.offer_price_ex_vat"
                              @input="
                                (e) =>
                                  handleInput(
                                    e?.target?.value,
                                    'offer_price_ex_vat'
                                  )
                              "
                              step="0.01"
                            />
                          </div>
                        </div>
                        <div class="col-2 mb-2">
                          <div class="">
                            <input
                              type="number"
                              id="val-purchasePriceVatOffers"
                              class="form-control"
                              placeholder="0"
                              v-model="formData.offer_price"
                              @input="
                                (e) =>
                                  handleInput(e?.target?.value, 'offer_price')
                              "
                              step="0.01"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-12 col-md-2">
                      <div class="">
                        <label class="form-label" for="val-isActiveOfferPrice">
                          {{
                            t("pages.products.fields.active_offer_price")
                          }}</label
                        >
                        <div class="form-check form-switch">
                          <input
                            v-model="formData.active_offer_price"
                            class="form-check-input"
                            type="checkbox"
                            id="val-isActiveOfferPrice"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-12 my-3">
                      <div class="form-check form-switch">
                        <input
                          v-model="formData.is_takeaway_price"
                          @input="
                            (e) =>
                              handleInputIsTakeAway(
                                e?.target?.checked,
                                'is_takeaway_price'
                              )
                          "
                          class="form-check-input"
                          type="checkbox"
                          id="val-isTakeawayPrice"
                        />
                        <label
                          class="form-check-label"
                          for="val-isTakeawayPrice"
                          >{{
                            t("pages.products.fields.active_takeaway_price")
                          }}</label
                        >
                      </div>
                    </div>
                  </div>
                  <div
                    class="row bg-gray-lighter px-2 py-3 rounded-2 mx-auto"
                    v-if="formData.is_takeaway_price"
                  >
                    <div class="col-12 col-md-10 overflow-x-auto">
                      <div class="row" style="min-width: 800px">
                        <div class="col-2 mb-2"></div>
                        <div class="col-2 mb-2"></div>
                        <div class="col-3 mb-2">
                          <div class="">
                            <label class="form-label" for="val-costPrice1">{{
                              t("pages.products.fields.purchase_price")
                            }}</label>
                            <input
                              type="number"
                              id="val-costPrice1"
                              class="form-control"
                              v-model="formData.takeaway_purchase_price"
                              :placeholder="t('common.enter')"
                              @input="
                                (e) =>
                                  handleInputTakeAway(
                                    e.target.value,
                                    'takeaway_purchase_price'
                                  )
                              "
                              step="0.01"
                            />
                          </div>
                        </div>
                        <div class="col-3 mb-4">
                          <div class="">
                            <label class="form-label" for="val-costPrice2">{{
                              t("pages.products.fields.cost_price")
                            }}</label>
                            <input
                              type="number"
                              id="val-costPrice2"
                              class="form-control"
                              :value="formData.takeaway_purchase_price"
                              disabled
                              :placeholder="t('common.enter')"
                              step="0.01"
                            />
                          </div>
                        </div>
                        <div class="col-2 mb-4">
                          <div class="">
                            <label class="form-label" for="val-vatRate">{{
                              t("pages.products.fields.vat_rate")
                            }}</label>
                            <select
                              id="val-vatRate"
                              class="form-select"
                              v-model="formData.takeaway_vat_rate"
                            >
                              <option
                                v-for="vat in listVatRate"
                                :key="vat.label"
                                :value="vat.value"
                              >
                                {{ vat.label }}
                              </option>
                            </select>
                          </div>
                        </div>
                        <div class="col-2 mb-2">
                          {{ t("pages.products.fields.takeaway_price") }}
                        </div>
                        <div class="col-2 mb-2">
                          <div class="">
                            <label
                              class="form-label"
                              for="val-profitRateOrdinary"
                              >{{
                                t("pages.products.fields.profit_rate")
                              }}</label
                            >
                            <input
                              type="number"
                              id="val-profitRateOrdinary"
                              class="form-control"
                              :value="profitRateOrdinaryTakeaway"
                              disabled
                              step="0.01"
                            />
                          </div>
                        </div>
                        <div class="col-3 mb-2">
                          <div class="">
                            <label
                              class="form-label"
                              for="val-profitOrdinary"
                              >{{ t("pages.products.fields.profit") }}</label
                            >
                            <input
                              type="number"
                              id="val-profitOrdinary"
                              class="form-control"
                              placeholder="0"
                              v-model="formData.takeaway_ordinary_profit"
                              @input="
                                (e) =>
                                  handleInputTakeAway(
                                    e.target.value,
                                    'takeaway_ordinary_profit'
                                  )
                              "
                              :class="{
                                'is-invalid':
                                  v$.takeaway_ordinary_profit &&
                                  v$.takeaway_ordinary_profit.$errors.length,
                              }"
                              @blur="
                                v$.takeaway_ordinary_profit &&
                                  v$.takeaway_ordinary_profit.$touch
                              "
                              step="0.01"
                            />
                          </div>
                        </div>
                        <div class="col-3 mb-2">
                          <div class="">
                            <label
                              class="form-label"
                              for="val-purchasePriceOrdinary"
                              >{{
                                t("pages.products.fields.price_ex_vat")
                              }}</label
                            >
                            <input
                              type="number"
                              id="val-purchasePriceOrdinary"
                              class="form-control"
                              placeholder="0"
                              @input="
                                (e) =>
                                  handleInputTakeAway(
                                    e.target.value,
                                    'takeaway_price_ex_vat'
                                  )
                              "
                              v-model="formData.takeaway_price_ex_vat"
                              step="0.01"
                            />
                          </div>
                        </div>
                        <div class="col-2 mb-2">
                          <div class="">
                            <label
                              class="form-label"
                              for="val-purchasePriceOrdinaryTakeAway"
                              >{{
                                t("pages.products.fields.price_inc_vat")
                              }}</label
                            >
                            <input
                              type="number"
                              id="val-purchasePriceOrdinaryTakeAway"
                              class="form-control"
                              placeholder="0"
                              @input="
                                (e) =>
                                  handleInputTakeAway(
                                    e.target.value,
                                    'takeaway_ordinary_price'
                                  )
                              "
                              v-model="formData.takeaway_ordinary_price"
                              step="0.01"
                            />
                          </div>
                        </div>
                        <div class="col-2 mb-2">
                          {{ t("pages.products.fields.offer_takeaway_price") }}
                        </div>
                        <div class="col-2 mb-2">
                          <div class="">
                            <input
                              type="number"
                              id="val-profitRateOffer"
                              class="form-control"
                              :value="profitRateOfferTakeaway"
                              disabled
                              step="0.01"
                            />
                          </div>
                        </div>
                        <div class="col-3 mb-2">
                          <div class="">
                            <input
                              type="number"
                              id="val-profitOffer"
                              class="form-control"
                              placeholder="0"
                              @input="
                                (e) =>
                                  handleInputTakeAway(
                                    e.target.value,
                                    'takeaway_offer_profit'
                                  )
                              "
                              v-model="formData.takeaway_offer_profit"
                              step="0.01"
                            />
                          </div>
                        </div>
                        <div class="col-3 mb-2">
                          <div class="">
                            <input
                              type="number"
                              id="val-purchasePriceOffer"
                              class="form-control"
                              placeholder="0"
                              @input="
                                (e) =>
                                  handleInputTakeAway(
                                    e.target.value,
                                    'takeaway_offer_ex_vat'
                                  )
                              "
                              v-model="formData.takeaway_offer_ex_vat"
                              step="0.01"
                            />
                          </div>
                        </div>
                        <div class="col-2 mb-2">
                          <div class="">
                            <input
                              type="number"
                              id="val-purchasePriceVatOfferTakeAway"
                              class="form-control"
                              placeholder="0"
                              @input="
                                (e) =>
                                  handleInputTakeAway(
                                    e.target.value,
                                    'takeaway_offer_price'
                                  )
                              "
                              v-model="formData.takeaway_offer_price"
                              step="0.01"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-12 col-md-2">
                      <div class="">
                        <label class="form-label" for="val-isActiveOfferPrice">
                          {{
                            t("pages.products.fields.active_offer_price")
                          }}</label
                        >
                        <div class="form-check form-switch">
                          <input
                            v-model="formData.takeaway_active_offer_price"
                            class="form-check-input"
                            type="checkbox"
                            id="val-isActiveOfferPrice"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                v-if="userPlansCustomFood?.active"
                class="tab-pane"
                id="btabs-static-profile"
                role="tabpanel"
                aria-labelledby="btabs-static-profile-tab"
                tabindex="0"
              >
                <div class="overflow-x-auto">
                  <div
                    class="d-flex align-center justify-content-start space-x-3"
                    style="width: max-content"
                  >
                    <e-button
                      type="danger"
                      size="sm"
                      @click="onAddCustomItem(1)"
                    >
                      {{ t("pages.products.buttons.remove_something") }}
                    </e-button>
                    <!--                    <e-button type="secondary" size="sm" @click="onAddCustomItem(2)">-->
                    <!--                      {{ t('pages.products.buttons.choose_between') }}-->
                    <!--                    </e-button>-->
                    <e-button type="info" size="sm" @click="onAddCustomItem(3)">
                      {{ t("pages.products.buttons.category_for_extras") }}
                    </e-button>
                    <e-button
                      type="warning"
                      size="sm"
                      @click="onAddCustomItem(4)"
                    >
                      {{ t("pages.products.buttons.change_with") }}
                    </e-button>
                    <e-button
                      type="success"
                      size="sm"
                      @click="openModal"
                      :bs-target="`#modal-list-favor`"
                      bs-toggle="modal"
                    >
                      {{ t("pages.products.buttons.add_favor") }}
                    </e-button>
                  </div>
                </div>
                <hr />
                <template
                  v-for="(item, index) in formData.customFoods"
                  :key="index"
                >
                  <products-dnd-constituent-remove-item
                    :isSubmit="isSubmitForm"
                    :isCheckTitle="isCheckItem"
                    :isCheck="!!formData.is_takeaway_price"
                    :indexing="index"
                    :global_custom_id="item.global_custom_id"
                    v-if="item.custom_type === 1"
                    v-model:title="item.title"
                    v-model:description="item.description"
                    v-model:hide_in_rush="item.hide_in_rush"
                    v-model:items="item.selectedItem"
                    v-model:disabledForm="item.disabledForm"
                    @remove="onRemoveCustomFood"
                    @move="onSwapCustomFood"
                  />
                  <!--                  <products-dnd-constituent-choose-item :indexing="index" v-if="item.custom_type === 2" v-model:title="item.title" v-model:description="item.description" v-model:items="item.selectedItem" @remove="onRemoveCustomFood" @move="onSwapCustomFood" />-->
                  <products-dnd-constituent-category-item
                    :isSubmit="isSubmitForm"
                    :isCheckTitle="isCheckItem"
                    :isCheck="!!formData.is_takeaway_price"
                    :indexing="index"
                    :global_custom_id="item.global_custom_id"
                    v-if="item.custom_type === 3"
                    v-model:title="item.title"
                    v-model:hide_in_rush="item.hide_in_rush"
                    v-model:limit_total="item.limit_total"
                    v-model:description="item.description"
                    v-model:items="item.selectedItem"
                    v-model:disabledForm="item.disabledForm"
                    @remove="onRemoveCustomFood"
                    @move="onSwapCustomFood"
                  />
                  <products-dnd-constituent-change-item
                    :isSubmit="isSubmitForm"
                    :isCheckTitle="isCheckItem"
                    :isCheck="!!formData.is_takeaway_price"
                    v-model:hide_in_rush="item.hide_in_rush"
                    :indexing="index"
                    :global_custom_id="item.global_custom_id"
                    v-if="item.custom_type === 4"
                    v-model:title="item.title"
                    v-model:description="item.description"
                    v-model:items="item.selectedItem"
                    v-model:disabledForm="item.disabledForm"
                    @remove="onRemoveCustomFood"
                    @move="onSwapCustomFood"
                  />
                </template>
              </div>
            </div>
            <div
              class="my-4"
              :style="{
                textAlign: 'end',
                display: 'flex',
                gap: '5px',
                justifyContent: 'end',
              }"
            >
              <button
                type="submit"
                class="btn btn-sm btn-primary"
                @click="handleSubmit('confirm')"
                :style="{ color: '#fff' }"
              >
                {{ t("pages.products.buttons.confirm") }}
              </button>
              <button
                v-if="!id"
                type="submit"
                class="btn btn-sm btn-primary"
                @click="handleSubmit('confirm_add')"
                :style="{ color: '#fff' }"
              >
                {{ t("pages.products.buttons.confirm_add") }}
              </button>
            </div>
          </BaseBlock>
        </form>
      </div>
    </div>
    <e-modal
      :id="`modal-list-favor`"
      :title="t('pages.products.labels.select_constituents')"
      style="--bs-modal-width: 800px"
      @confirm="onSubmitSelectedTopping"
    >
      <Dataset v-slot="{ ds }" :ds-data="listToppings" :ds-search-in="['name']">
        <div class="row" :data-page-count="ds.dsPagecount">
          <div class="col-md-4 py-2">
            <input
              type="text"
              placeholder="Search..."
              class="form-control"
              @keyup="onSearchConstituents"
              v-model="searchKey"
            />
            <!-- <DatasetSearch
              :ds-search-placeholder="t('pages.products.labels.search')"
            /> -->
          </div>
        </div>
        <hr />
        <div class="row" v-if="listToppings?.length">
          <div class="col-md-12">
            <div class="table-responsive">
              <table class="table mb-0">
                <thead>
                  <tr>
                    <th
                      v-for="th in cols"
                      :key="th.field"
                      :class="['sort', th.sort]"
                    >
                      {{ th.name }}
                    </th>
                    <th style="width: 80px"></th>
                  </tr>
                </thead>
                <DatasetItem tag="tbody" class="fs-sm">
                  <template #default="{ row }">
                    <tr>
                      <td>{{ row.title }}</td>
                      <td>{{ checkType(row.type) }}</td>
                      <td>
                        <input
                          class="form-check-input"
                          type="checkbox"
                          :value="row?.id"
                          :id="row?.id"
                          v-model="selectedToppingId"
                        />
                      </td>
                    </tr>
                  </template>
                </DatasetItem>
              </table>
            </div>
          </div>
        </div>
        <EListEmpty v-else />
        <div
          class="d-flex flex-md-row flex-column justify-content-between align-items-center"
        >
          <DatasetInfo class="py-3 fs-sm" />
          <DatasetPager class="flex-wrap py-3 fs-sm" />
        </div>
      </Dataset>
    </e-modal>
  </div>
</template>

<style lang="scss">
@import "dropzone/dist/dropzone.css";
@import "@/assets/scss/vendor/dropzone";
.block-content {
  padding-inline: 10px;
}

.icon_arrow_left {
  background-color: rgb(243, 235, 235);
  border-radius: 100%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.dz-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>
