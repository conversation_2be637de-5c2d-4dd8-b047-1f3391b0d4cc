import { http } from "./Base/base.service";

export const dailySummaryService = {
  async getList(query) {
    return await http.get("/accounting/revenue", {
      params: query,
    });
  },

  async export(query) {
    return await http.get("/accounting/export-csv-revenue", {
      params: query,
    });
  },

  async exportPDF(query) {
    return await http.get("/accounting/export-pdf-revenue", {
      params: query,
      responseType: "blob",
    });
  },
};
