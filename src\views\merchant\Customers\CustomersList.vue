<script setup>
import { reactive, computed, onMounted, ref, watch } from "vue";
import { Dataset, DatasetItem, DatasetInfo, DatasetShow } from "vue-dataset";
import { customerService } from "@/services/customer.service";
import EListEmpty from "@/components/Elements/EListEmpty.vue";
import { useTemplateStore } from "@/stores/template";
import EModal from "@/components/Elements/EModal.vue";
import ModalDetailCustomer from "./ModalDetailCustomer.vue";
import { useDebounceFn } from "@vueuse/core";
import { orderService } from "@/services/order.service";
import { useI18n } from "vue-i18n";
import { storeData } from "@/stores/storeData";

const limit = ref(10);
const currentPage = ref(1);
const totalPage = ref(1);
const total = ref();
const visible = ref(true);
const store = useTemplateStore();
const dataFetch = storeData();
const search = ref();
const listOrders = ref();
const { t, locale } = useI18n();
// Helper variables
const cols = reactive([
  {
    name: t("pages.customer.fields.id"),
    field: "id",
    sort: "",
  },
  {
    name: t("pages.customer.fields.name"),
    field: "name",
    sort: "",
  },
  {
    name: t("pages.customer.fields.phone"),
    field: "phone",
    sort: "",
  },
  {
    name: t("pages.customer.fields.email"),
    field: "email",
    sort: "",
  },
  {
    name: t("pages.customer.fields.sid"),
    field: "sid",
    sort: "",
  },
  {
    name: t("pages.customer.fields.orders"),
    field: "orders",
    sort: "",
  },
  {
    name: t("pages.customer.fields.marketing_accepted"),
    field: "marketing_accepted",
    sort: "",
  },
]);
const updateCols = () => {
  cols[0].name = t("pages.customer.fields.id");
  cols[1].name = t("pages.customer.fields.name");
  cols[2].name = t("pages.customer.fields.phone");
  cols[3].name = t("pages.customer.fields.email");
  cols[4].name = t("pages.customer.fields.sid");
  cols[5].name = t("pages.customer.fields.orders");
};

// Watch for language changes
watch(locale, () => {
  updateCols();
});

// Sort by functionality
const sortBy = computed(() => {
  return cols.reduce((acc, o) => {
    if (o.sort) {
      o.sort === "asc" ? acc.push(o.field) : acc.push("-" + o.field);
    }
    return acc;
  }, []);
});

// On sort th click
function onSort(event, i) {
  let toset;
  const sortEl = cols[i];

  if (!event.shiftKey) {
    cols.forEach((o) => {
      if (o.field !== sortEl.field) {
        o.sort = "";
      }
    });
  }

  if (!sortEl.sort) {
    toset = "asc";
  }

  if (sortEl.sort === "desc") {
    toset = event.shiftKey ? "" : "asc";
  }

  if (sortEl.sort === "asc") {
    toset = "desc";
  }

  sortEl.sort = toset;
}

const listCustomers = ref([]);

const onFetchList = async () => {
  try {
    store.pageLoader({ mode: "on" });
    const response = await customerService.getList({
      limit: limit.value,
      page: currentPage.value,
      search: search?.value,
    });

    if (!response?.error) {
      listCustomers.value = response.data?.data || [];
      totalPage.value = response.data.last_page;
      total.value = response.data.total;
    }
    store.pageLoader({ mode: "off" });
  } catch (error) {
    console.log(error);
    store.pageLoader({ mode: "off" });
  }
};
const handleChangeSearch = useDebounceFn((e) => {
  search.value = e?.target?.value;
  onFetchList();
}, 500);

watch(limit, async () => {
  currentPage.value = 1;
  await onFetchList();
});

// Apply a few Bootstrap 5 optimizations
onMounted(async () => {
  if (dataFetch.storeData?.["merchant-customers-list"]?.length > 0) {
    await (listCustomers.value =
      dataFetch.storeData?.["merchant-customers-list"]);
    total.value = dataFetch.total?.["merchant-customers-list"];
    store.pageLoader({ mode: "off" });
  } else {
    await onFetchList();
  }

  // Remove labels from
  document.querySelectorAll("#datasetLength label").forEach((el) => {
    el.remove();
  });

  // Replace select classes
  let selectLength = document.querySelector("#datasetLength select");

  if (selectLength) {
    selectLength.classList = "";
    selectLength.classList.add("form-select");
    selectLength.style.width = "80px";
  }
});

const handleOpenModal = async (id) => {
  store.pageLoader({ mode: "on" });
  const response = await orderService.getList({
    customer_id: id,
    limit: -1,
  });
  store.pageLoader({ mode: "off" });
  if (!response?.error) {
    listOrders.value = response?.data?.data || [];
  }
};
const ModalOrdersRef = ref();
const onCloseModalOrder = () => {
  ModalOrdersRef.value.closeModal();
};
</script>

<template>
  <BasePageHeading
    :title="t('pages.customer.name')"
    :subtitle="t('pages.customer.labels.label_head_list')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.customer.labels.manages")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            {{ t("pages.customer.titles.list") }}
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <BaseBlock :title="t('pages.customer.name')">
      <Dataset
        v-slot="{ ds }"
        :ds-data="listCustomers"
        :ds-sortby="sortBy"
        :ds-search-in="['id', 'name', 'phone', 'email', 'sid', 'orders']"
      >
        <div class="row" :data-page-count="ds.dsPagecount">
          <div id="datasetLength" class="col-md-8 py-2">
            <DatasetShow v-show="false" :dsShowEntries="100" />
            <div class="form-inline">
              <select class="form-select" style="width: 80px" v-model="limit">
                <option :value="5">5</option>
                <option :value="10">10</option>
                <option :value="25">25</option>
                <option :value="50">50</option>
                <option :value="100">100</option>
              </select>
            </div>
          </div>
          <div class="col-md-3 py-2">
            <input
              type="text"
              class="form-control"
              id="form-name"
              :placeholder="t('pages.placeholder.enter')"
              @input="handleChangeSearch"
            />
          </div>
        </div>
        <hr />
        <div class="row" v-if="listCustomers?.length">
          <div class="col-md-12">
            <div class="table-responsive">
              <table class="table mb-0">
                <thead>
                  <tr>
                    <th
                      v-for="(th, index) in cols"
                      :key="th.field"
                      :class="['sort', th.sort]"
                      @click="onSort($event, index)"
                    >
                      {{ th.name }} <i class="gg-select float-end"></i>
                    </th>
                  </tr>
                </thead>
                <DatasetItem tag="tbody" class="fs-sm">
                  <template #default="{ row }">
                    <tr>
                      <td style="min-width: 50px">
                        <button
                          @click="handleOpenModal(row?.id)"
                          data-bs-toggle="modal"
                          data-bs-target="#modal-orders"
                          data-target=".bd-example-modal-lg"
                          type="button"
                          class="btn btn-link"
                        >
                          {{ row.id }}
                        </button>
                      </td>
                      <td style="min-width: 150px">
                        <a
                          href="javascript:void(0)"
                          @click="handleOpenModal(row?.id)"
                          data-bs-toggle="modal"
                          data-bs-target="#modal-orders"
                          data-target=".bd-example-modal-lg"
                          type="button"
                          >{{ row.name }}</a
                        >
                      </td>
                      <td style="min-width: 150px">{{ row.phone }}</td>
                      <td style="min-width: 150px">{{ row.email }}</td>
                      <td style="min-width: 100px">{{ row.sid }}</td>
                      <td style="min-width: 80px">{{ row.orders_count }}</td>
                      <td
                        v-if="row.marketing_accepted"
                        style="min-width: 100px; color: green"
                      >
                        <i class="fas fa-check-square"></i>
                      </td>
                      <td v-else style="color: red; min-width: 100px">
                        <i class="fas fa-times"></i>
                      </td>
                    </tr>
                  </template>
                </DatasetItem>
              </table>
            </div>
          </div>
        </div>
        <EListEmpty v-else />
        <div
          class="d-flex flex-md-row flex-column justify-content-between align-items-center"
        >
          <DatasetInfo class="py-3 fs-sm" />
          <el-pagination
            v-if="visible"
            v-model:current-page="currentPage"
            @current-change="onFetchList"
            background
            v-model:page-size="limit"
            layout="prev, pager, next"
            :prev-text="t('pages.footer.previous')"
            :next-text="t('pages.footer.next')"
            :total="total"
          />
        </div>
      </Dataset>
    </BaseBlock>
  </div>
  <EModal
    id="modal-orders"
    :title="t('pages.customer.titles.detail')"
    size="modal-xl"
    :cancel-text="t('pages.opening_hours.labels.btn_cancel')"
    :hidden-button-ok="true"
    ref="ModalOrdersRef"
  >
    <template v-slot:childrenComponent>
      <ModalDetailCustomer
        :list-order="listOrders"
        @close="onCloseModalOrder"
      />
    </template>
  </EModal>
</template>

<style lang="scss" scoped>
.gg-select {
  box-sizing: border-box;
  position: relative;
  display: block;
  transform: scale(1);
  width: 22px;
  height: 22px;
}
.gg-select::after,
.gg-select::before {
  content: "";
  display: block;
  box-sizing: border-box;
  position: absolute;
  width: 8px;
  height: 8px;
  left: 7px;
  transform: rotate(-45deg);
}
.gg-select::before {
  border-left: 2px solid;
  border-bottom: 2px solid;
  bottom: 4px;
  opacity: 0.3;
}
.gg-select::after {
  border-right: 2px solid;
  border-top: 2px solid;
  top: 4px;
  opacity: 0.3;
}
th.sort {
  cursor: pointer;
  user-select: none;
  &.asc {
    .gg-select::after {
      opacity: 1;
    }
  }
  &.desc {
    .gg-select::before {
      opacity: 1;
    }
  }
}
</style>
