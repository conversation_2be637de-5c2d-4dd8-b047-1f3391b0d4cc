<script setup>
import { reactive, computed, onMounted, ref, watch } from "vue";
import { Dataset, DatasetItem, DatasetInfo, DatasetShow } from "vue-dataset";
import { toppingGroupService } from "@/services/toppingGroup.service";
import EModal from "@/components/Elements/EModal.vue";
import EListEmpty from "@/components/Elements/EListEmpty.vue";
import { useTemplateStore } from "@/stores/template";
import { useDebounceFn } from "@vueuse/core";
import EButton from "@/components/Elements/EButton.vue";
import Swal from "sweetalert2";
import { useI18n } from "vue-i18n";
import useAppRouter from "@/composables/useRouter";
import { storeData } from "@/stores/storeData";
import { scrollTo } from "@/stores/scollItemInlist";
import { searchValue } from "@/stores/searchData";

const store = useTemplateStore();
const dataFetch = storeData();
const { t, locale } = useI18n();
const scrollStore = scrollTo();
const searchStore = searchValue();
let toast = Swal.mixin({
  buttonsStyling: false,
  target: "#page-container",
  customClass: {
    confirmButton: "btn btn-success m-1",
    cancelButton: "btn btn-danger m-1",
    input: "form-control",
  },
});
const router = useAppRouter();
const search = ref();
// Helper variables
const cols = reactive([
  {
    name: t("pages.topping_group.fields.id"),
    field: "id",
    sort: "",
  },
  {
    name: t("pages.topping_group.fields.name"),
    field: "name",
    sort: "",
  },
]);

const updateCols = () => {
  cols[0].name = t("pages.topping_group.fields.id");
  cols[1].name = t("pages.topping_group.fields.name");
};

// Watch for language changes
watch(locale, () => {
  updateCols();
});
// Sort by functionality
const sortBy = computed(() => {
  return cols.reduce((acc, o) => {
    if (o.sort) {
      o.sort === "asc" ? acc.push(o.field) : acc.push("-" + o.field);
    }
    return acc;
  }, []);
});

// On sort th click
// function onSort(event, i) {
//   let toset
//   const sortEl = cols[i]

//   if (!event.shiftKey) {
//     cols.forEach((o) => {
//       if (o.field !== sortEl.field) {
//         o.sort = ''
//       }
//     })
//   }

//   if (!sortEl.sort) {
//     toset = 'asc'
//   }

//   if (sortEl.sort === 'desc') {
//     toset = event.shiftKey ? '' : 'asc'
//   }

//   if (sortEl.sort === 'asc') {
//     toset = 'desc'
//   }

//   sortEl.sort = toset
// }

const idToppingGroupDelete = ref();
const listToppingGroups = ref([]);
const limit = ref(10);
const currentPage = ref(1);
const total = ref();
const visible = ref(true);

const onFetchList = async () => {
  try {
    store.pageLoader({ mode: "on" });
    searchStore.setValueSearch(search?.value);
    scrollStore.setPage(currentPage.value);
    const response = await toppingGroupService.getList({
      page: currentPage.value,
      limit: limit.value,
      search: search?.value,
    });
    if (!response?.error) {
      listToppingGroups.value = response.data?.data || [];
      total.value = response.data.total;
    }
    store.pageLoader({ mode: "off" });
  } catch (error) {
    console.log(error);
    store.pageLoader({ mode: "off" });
  }
};
const handleChangeSearch = useDebounceFn((e) => {
  search.value = e?.target?.value;
  onFetchList();
}, 500);
const apiDelete = async () => {
  try {
    store.pageLoader({ mode: "on" });
    await toppingGroupService.delete(idToppingGroupDelete.value);
    const newToppingGroups = listToppingGroups.value.filter(
      (item) => item.id !== idToppingGroupDelete.value
    );
    listToppingGroups.value = newToppingGroups;
    idToppingGroupDelete.value = null;
    store.pageLoader({ mode: "off" });
  } catch (error) {
    console.log(error);
    store.pageLoader({ mode: "off" });
  }
};

watch(limit, async () => {
  currentPage.value = 1;
  await onFetchList();
});

// Apply a few Bootstrap 5 optimizations
onMounted(async () => {
  currentPage.value = scrollStore.page;
  search.value = searchStore.search;
  if (dataFetch.storeData?.["merchant-topping-groups-list"]?.length > 0) {
    await (listToppingGroups.value =
      dataFetch.storeData?.["merchant-topping-groups-list"]);
    total.value = dataFetch.total?.["merchant-topping-groups-list"];
    store.pageLoader({ mode: "off" });
  } else {
    await onFetchList();
  }

  // Remove labels from
  document.querySelectorAll("#datasetLength label").forEach((el) => {
    el.remove();
  });

  // Replace select classes
  let selectLength = document.querySelector("#datasetLength select");

  if (selectLength) {
    selectLength.classList = "";
    selectLength.classList.add("form-select");
    selectLength.style.width = "80px";
  }

  if (scrollStore.formUpdateSuccess) {
    const item = document.querySelector(`.toppingItem-${scrollStore.idItem}`);
    item.scrollIntoView({
      behavior: "smooth",
      block: "start",
    });
    scrollStore.setUpdateSuccess();
  }
});

const onOpenDeleteConfirm = (id) => {
  toast
    .fire({
      title: "Are you sure?",
      text: "You will not be able to recover this topping group!",
      icon: "warning",
      showCancelButton: true,
      customClass: {
        confirmButton: "btn btn-danger m-1",
        cancelButton: "btn btn-info m-1",
      },
      confirmButtonText: "Yes, delete!",
      html: false,
      preConfirm: () => {
        return toppingGroupService.delete(id);
      },
    })
    .then((result) => {
      if (result.value && !result.value?.error) {
        toast.fire("Deleted!", "Topping group has been deleted.", "success");
        onFetchList();
      } else if (result.dismiss === "cancel") {
        toast.fire("Cancelled", "Topping group is safe", "error");
      }
    });
};
</script>

<template>
  <BasePageHeading
    :title="t('pages.topping_group.name')"
    :subtitle="t('pages.topping_group.labels.label_head_list')"
  >
    <template #extra>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-alt">
          <li class="breadcrumb-item">
            <a class="link-fx" href="javascript:void(0)">{{
              t("pages.topping_group.labels.manages")
            }}</a>
          </li>
          <li class="breadcrumb-item" aria-current="page">
            {{ t("pages.topping_group.labels.manages") }}
          </li>
        </ol>
      </nav>
    </template>
  </BasePageHeading>

  <div class="content">
    <BaseBlock :title="t('menu.topping_groups')">
      <template #options>
        <e-button
          type="info"
          size="sm"
          @click="
            () => router.pushByName({ name: 'merchant-topping-groups-create' })
          "
          ><i class="fa fa-plus opacity-50 me-1"></i>
          {{ t("pages.topping_group.titles.add") }}</e-button
        >
      </template>
      <Dataset
        v-slot="{ ds }"
        :ds-data="listToppingGroups"
        :ds-sortby="sortBy"
        :ds-search-in="['id', 'name', 'store_id']"
      >
        <div class="row" :data-page-count="ds.dsPagecount">
          <div id="datasetLength" class="col-md-8 py-2">
            <DatasetShow v-show="false" :dsShowEntries="100" />
            <div class="form-inline">
              <select class="form-select" style="width: 80px" v-model="limit">
                <option :value="5">5</option>
                <option :value="10">10</option>
                <option :value="25">25</option>
                <option :value="50">50</option>
                <option :value="100">100</option>
              </select>
            </div>
          </div>
          <div class="col-md-4 py-2">
            <input
              v-model="search"
              type="text"
              class="form-control"
              id="form-name"
              :placeholder="t('pages.placeholder.enter')"
              @input="handleChangeSearch"
            />
          </div>
        </div>

        <hr />
        <div class="row" v-if="listToppingGroups?.length">
          <div class="col-md-12">
            <div class="table-responsive">
              <table class="table table-striped mb-0">
                <thead>
                  <tr>
                    <th v-for="th in cols" :key="th.field">
                      {{ th.name }}
                    </th>
                    <th class="text-end" scope="col">
                      {{ t("pages.topping_group.fields.action") }}
                    </th>
                  </tr>
                </thead>
                <DatasetItem tag="tbody" class="fs-sm">
                  <template #default="{ row }">
                    <tr :class="`toppingItem-${row?.id}`">
                      <td style="min-width: 50px">
                        <RouterLink :to="`topping-groups/${row.id}`">
                          {{ row.id }}
                        </RouterLink>
                      </td>
                      <td style="min-width: 150px">{{ row.name }}</td>
                      <td class="text-end">
                        <div class="btn-group">
                          <button
                            type="button"
                            class="btn btn-sm btn-alt-secondary"
                          >
                            <i
                              class="fa fa-fw fa-pencil-alt"
                              @click="
                                () =>
                                  router.pushByPath(
                                    `/topping-groups/${row.id}/update`
                                  )
                              "
                            ></i>
                          </button>
                          <button
                            type="button"
                            class="btn btn-sm btn-alt-secondary"
                            @click="onOpenDeleteConfirm(row.id)"
                          >
                            <i class="fa fa-fw fa-times"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </template>
                </DatasetItem>
              </table>
            </div>
          </div>
        </div>
        <EListEmpty v-else />
        <div
          class="d-flex flex-md-row flex-column justify-content-between align-items-center"
        >
          <DatasetInfo class="py-3 fs-sm" />
          <el-pagination
            v-if="visible"
            v-model:current-page="currentPage"
            @current-change="onFetchList"
            background
            v-model:page-size="limit"
            layout="prev, pager, next"
            :prev-text="t('pages.footer.previous')"
            :next-text="t('pages.footer.next')"
            :total="total"
          />
        </div>
      </Dataset>
    </BaseBlock>
  </div>

  <EModal
    id="modal-delete"
    :title="`Delete Item ${idToppingGroupDelete}`"
    subtitle="Are you sure to delete it?"
    :handle-confirm="apiDelete"
  />
</template>

<style lang="scss" scoped>
.gg-select {
  box-sizing: border-box;
  position: relative;
  display: block;
  transform: scale(1);
  width: 22px;
  height: 22px;
}
.gg-select::after,
.gg-select::before {
  content: "";
  display: block;
  box-sizing: border-box;
  position: absolute;
  width: 8px;
  height: 8px;
  left: 7px;
  transform: rotate(-45deg);
}
.gg-select::before {
  border-left: 2px solid;
  border-bottom: 2px solid;
  bottom: 4px;
  opacity: 0.3;
}
.gg-select::after {
  border-right: 2px solid;
  border-top: 2px solid;
  top: 4px;
  opacity: 0.3;
}
th.sort {
  cursor: pointer;
  user-select: none;
  &.asc {
    .gg-select::after {
      opacity: 1;
    }
  }
  &.desc {
    .gg-select::before {
      opacity: 1;
    }
  }
}
</style>
