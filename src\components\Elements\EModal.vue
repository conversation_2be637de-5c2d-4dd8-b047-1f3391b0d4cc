<script setup>
import {ref} from 'vue'
const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  cancelText: {
    type: String,
    default: 'Cancel'
  },
  okText: {
    type: String,
    default: 'Confirm'
  },
  okType: {
    type: String,
    default: 'button'
  },
  position: {
    type: String,
    default: 'modal-dialog-centered'
  },
  size: {
    type: String,
    default: ''
  },
  closeOnSubmit: {
    type: Boolean,
    default: true,
  },
  hiddenButtonOk: {
    type: Boolean,
    default: false
  }

})
const emit = defineEmits(['confirm', 'close'])

const close = () => {
  emit('close');
};
const closeBtnRef = ref()
const closeModal = () => {
  closeBtnRef.value.click()
}
defineExpose({
  closeModal
})
</script>

<template>
  <div class="modal" :id="props.id" tabindex="-1" role="dialog" aria-hidden="true">
    <div :class="`modal-dialog ${props.position} ${props.size}`" role="document">
      <div class="modal-content">
        <BaseBlock :title="props.title" transparent class="mb-0">
          <template #options>
            <button
              type="button"
              class="btn-block-option"
              data-bs-dismiss="modal"
              aria-label="Close"
              @click="close"
              ref="closeBtnRef"
            >
              <i class="fa fa-fw fa-times"></i>
            </button>
          </template>

          <template #content>
            <div class="block-content fs-sm">
              <slot />
            </div>
            <slot name="childrenComponent"></slot>
            <div class="block-content block-content-full text-end bg-body">
              <slot name="footer">
                <button
                  type="button"
                  class="btn btn-sm btn-alt-secondary me-1"
                  data-bs-dismiss="modal"
                  @click="close"
                >
                  {{ props.cancelText }}
                </button>
                <button
                  :type="props.okType"
                  :hidden="props.hiddenButtonOk"
                  class="btn btn-sm btn-primary text-white"
                  data-bs-dismiss="modal"
                  v-if="props.closeOnSubmit"
                  @click="emit('confirm')"
                >
                  {{ props.okText }}
                </button>
                <button
                  :type="props.okType"
                  class="btn btn-sm btn-primary text-white"
                  v-else
                  @click="emit('confirm')"
                >
                  {{ props.okText }}
                </button>
              </slot>
            </div>
          </template>
        </BaseBlock>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
