import { createApp } from "vue";
import { createPinia } from "pinia";
import App from "./App.vue";
import VueCookies from "vue3-cookies";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import i18n from "@/i18n";

// You can use the following starter router instead of the default one as a clean starting point
// import router from "./router/starter";
import router from "./router/starter";

// Template components
import BaseBlock from "@/components/BaseBlock.vue";
import BaseBackground from "@/components/BaseBackground.vue";
import BasePageHeading from "@/components/BasePageHeading.vue";

// Template directives
import clickRipple from "@/directives/clickRipple";

// Bootstrap framework
import * as bootstrap from "bootstrap";
window.bootstrap = bootstrap;

// Craft new application
const app = createApp(App);

// Register global components
app.component("BaseBlock", BaseBlock);
app.component("BaseBackground", BaseBackground);
app.component("BasePageHeading", BasePageHeading);

// Register global directives
app.directive("click-ripple", clickRipple);

// Use Pinia and Vue Router
app.use(createPinia());
app.use(i18n);
app.use(router);
app.use(ElementPlus);
app.use(VueCookies);

// ..and finally mount it!
app.mount("#app");
